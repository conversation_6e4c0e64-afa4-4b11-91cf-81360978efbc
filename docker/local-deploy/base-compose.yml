version: '3.4'

x-logging: &default-logging
  driver: "json-file"
  options:
    tag: "{{.ImageName}}/{{.Name}}/{{.ID}}"
    labels: application.name,application.type
x-resource: &defaut-resource
  resources:
    limits:
      memory: ${MEMORY_LIMIT}
x-networks: &defaut-network
  - app_net

services:
  # 核心反向代理
  nginx:
    image: docker-hub.54w.com/nginx:1.25.2
    container_name: nginx
    logging: *default-logging
    restart: always
    ports:
    - "28080:28080"
    volumes:
    - /root/base-deploy/nginx/conf.d:/etc/nginx/conf.d/
    - /root/base-deploy/nginx/nginx.conf:/etc/nginx/nginx.conf
    networks: *defaut-network


networks:
  app_net:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16

