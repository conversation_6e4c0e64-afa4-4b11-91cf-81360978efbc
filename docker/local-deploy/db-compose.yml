x-logging: &default-logging
  driver: "json-file"
  options:
    tag: "{{.ImageName}}/{{.Name}}/{{.ID}}"
    labels: application.name,application.type
x-resource: &defaut-resource
  resources:
    limits:
      memory: ${MEMORY_LIMIT}
x-networks: &defaut-network
  - app_net

services:

  # postgres数据库（测试环境使用，线上使用生产环境公共数据库服务）
  # docker-compose stop postgres && docker-compose rm postgres -f && docker volume rm deploy_postgres-volume

  # 高速缓存
  redis:
    container_name: redis6
    restart: always
    logging: *default-logging
    image: docker-hub.54w.com/redis:5.0
    command: redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - 127.0.0.1:26381:6379
    labels:
      application.name: "Redis"
    networks: *defaut-network

  # postgres数据库（测试环境使用，线上使用生产环境公共数据库服务）
  # docker-compose stop postgres && docker-compose rm postgres -f && docker volume rm deploy_postgres-volume
  postgres:
    container_name: postgres6
    restart: always
    image: docker-hub.54w.com/corpusops/pgrouting-bare:14-3-3.4
    labels:
      application.name: "postgres"
    logging: *default-logging
    privileged: true
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 8G
    volumes:
      - ./postgres/data:/var/lib/postgresql/data
      - ./postgres/initdb.d:/docker-entrypoint-initdb.d
    environment:
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    ports:
      - 25433:5432
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      retries: 5
    networks: *defaut-network
