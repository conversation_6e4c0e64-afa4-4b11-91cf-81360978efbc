x-logging: &default-logging
  driver: "json-file"
  options:
    tag: "{{.ImageName}}/{{.Name}}/{{.ID}}"
    labels: application.name,application.type
x-resource: &defaut-resource
  resources:
    limits:
      memory: ${MEMORY_LIMIT}

x-networks: &defaut-network
  - app_net
x-environment: &app-environment
  NACOS_SERVER_ADDR: ${NACOS_SERVER_ADDR}
  DATABASE: ${DATABASE}
  DATABASE_HOST: ${APP_DATABASE_HOST}
  DATABASE_PORT: ${APP_DATABASE_PORT}
  DATABASE_USERNAME: ${APP_DATABASE_USERNAME}
  DATABASE_PASSWORD: ${APP_DATABASE_PASSWORD}
  REDIS_DB: ${REDIS_DB}
  REDIS_HOST: ${REDIS_HOST}
  REDIS_PORT: ${REDIS_PORT}
  REDIS_PASSWORD: ${REDIS_PASSWORD}


services:

  # cdes界面
  cdes-admin:
    container_name: admin-admin
    image:  ${DI_HUB}/${DI_GROUP}/cdes-admin:${DI_VERSION}
    ports:
      - "127.0.0.1:18275:80"
    networks:
      *defaut-network
  # cdes界面
  cdes-ui:
    container_name: admin-ui
    image:  ${DI_HUB}/${DI_GROUP}/cdes-ui:${DI_VERSION}
    ports:
      - "127.0.0.1:8276:80"
    networks:
      *defaut-network

  # cdes主服务
  cdes:
    container_name: hbut
    image: ${DI_HUB}/${DI_GROUP}/hbut:${DI_VERSION}
    depends_on:
      - postgres
    environment: *app-environment
    ports:
      - "127.0.0.1:18277:8284"
    networks: *defaut-network
