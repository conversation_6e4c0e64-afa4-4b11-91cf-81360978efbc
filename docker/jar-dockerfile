# 注意这里必须使用这个特殊的openjdk镜像构建才能支持J2V8运行，J2V8默认采用glibc编译的C库，而alpine的linux默认提供是musl
# 针对这个差异此处镜像通过使用musl重新编译J2V8，使其可以在openjdk-alpine正常工作。
FROM docker-hub.54w.com/hn4930/openjdk-alpine-j2v8:jdk8-4.6-font

ARG JAR_FILE
COPY ${JAR_FILE} app.jar


ENTRYPOINT java -Duser.timezone=Asia/Shanghai -Djava.security.egd=file:/dev/./urandom  -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8000 -jar /app.jar
