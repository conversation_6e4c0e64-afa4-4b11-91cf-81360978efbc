# By using this file the CI/CD system will pick up these values, note the memory setting below
# Gradle daemon on by default, may want to disable for CI/CD
org.gradle.daemon=true
org.gradle.parallel=false
org.gradle.jvmargs=-Djava.security.egd=file:/dev/./urandom \
  -Xmx4096m -Xms1g \
  -Dorg.gradle.internal.http.socketTimeout=300000 \
  -Dfile.encoding=UTF-8 \
  -Dcom.sun.xml.bind.v2.bytecode.ClassTailor.noOptimize=true

group=org.ahead4.cloud
version=1.0.0
docker_group=hbut-cloud
# docker login --username=release docker-hub.54w.com
hub=docker-hub.54w.com