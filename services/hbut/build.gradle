plugins {
    id 'java'
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
}

project.description = 'doc-flow'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

//依赖管理
dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:2020.0.3"
        mavenBom "com.alibaba.cloud:spring-cloud-alibaba-dependencies:2021.1"
    }
}
configurations.all {
    resolutionStrategy {
        force 'org.codehaus.groovy:groovy:4.0.10'
        force 'org.apache.groovy:groovy:4.0.10'
    }
}
dependencies {
    implementation 'org.springframework.retry:spring-retry'
    implementation 'org.springframework:spring-aspects'
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'com.fasterxml:jackson-xml-databind:0.6.2'
    implementation 'com.fasterxml.jackson:jackson-base:2.14.2'
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.junit.jupiter:junit-jupiter:5.10.0'

    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-logging'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-security'

    //<!--cloud nacos 服务注册中心-->
//    implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery'
    //<!--cloud nacos 配置中心 -->
//    implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config'
    //<!--cloud sentinel 限流依赖-->
//    implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel'
    //<!--cloud nacos Sentinel 采用配置限流 （支持采用 Nacos 作为规则配置数据源）-->
//    implementation 'com.alibaba.csp:sentinel-datasource-nacos:1.6.3'
    //<!--cloud commons 模块引入-->
//    implementation 'org.springframework.cloud:spring-cloud-commons'
//    implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap'
    implementation 'org.hibernate.validator:hibernate-validator:6.2.4.Final'

    implementation "com.baomidou:mybatis-plus-boot-starter:$mybatisPlusVersion" // $mybatisPlusVersion"
    // 连接池
    implementation "com.alibaba:druid-spring-boot-starter:1.2.6"
    // 动态数据源
    implementation 'com.baomidou:dynamic-datasource-spring-boot-starter:3.4.0'

    implementation 'org.springframework.boot:spring-boot-starter-data-redis'

    //    数据库驱动

    // 达梦数据库驱动
    implementation 'org.ahead4:dm-jdbc-driver:18.0.0'
    // oracle数据库驱动
    implementation 'org.ahead4:oacle-jdbc-driver:8.0.0'
    implementation 'org.ahead4:oracle-jdbc-driver-i18n:8.0.0'
    // mysql数据库驱动
    runtimeOnly 'mysql:mysql-connector-java'
    implementation "org.postgresql:postgresql"

    // 私有云服务组件



    implementation 'javax.persistence:javax.persistence-api:2.2'
    // mybatis-velocity 模版引擎，处理动态api
    implementation 'org.mybatis.scripting:mybatis-velocity:2.1.0'
    // Alibaba Druid
    implementation 'com.alibaba:druid:1.2.6'
    //as 私有云服务组件

    implementation "org.ahead4.cloud:entities:${orgAhead4CloudVersion}"
    implementation "org.ahead4.cloud:interfaces:${orgAhead4CloudVersion}"
    implementation "org.ahead4.cloud:mybatis-plus-start:${orgAhead4CloudVersion}"
    implementation "org.ahead4.cloud:cache-start:${orgAhead4CloudVersion}"

    implementation "org.ahead4.cloud:common-tools:${orgAhead4CloudVersion}"
    implementation "org.ahead4.cloud:security-service:${orgAhead4CloudVersion}"
    implementation "org.ahead4.cloud:common-service:${orgAhead4CloudVersion}"
    implementation "org.ahead4.cloud:common-web-service:${orgAhead4CloudVersion}"
    implementation "org.ahead4.cloud:exception-service:${orgAhead4CloudVersion}"
    implementation "org.ahead4.cloud:logger-service:${orgAhead4CloudVersion}"
    implementation "org.ahead4.cloud:permission-service:${orgAhead4CloudVersion}"

    implementation 'com.squareup.okhttp3:okhttp:3.14.9'
    implementation 'com.squareup.okhttp3:logging-interceptor:3.14.9'
    implementation 'com.belerweb:pinyin4j:2.5.1'
    implementation 'org.springframework.security:spring-security-core'
    implementation 'org.springframework.security:spring-security-web'
    implementation 'org.springframework.boot:spring-boot-starter-freemarker:2.1.5.RELEASE'
    //ps 私有云服务组件
    // 权限服务模块
    implementation "org.ahead4.cloud:permission-api:${orgAhead4CloudVersion}"

    // 动态API服务
    implementation "org.ahead4.cloud:dynamic-api:${orgAhead4CloudVersion}"
    implementation 'org.apache.groovy:groovy:4.0.21'
    // 动态API服务
    implementation("org.ahead4.cloud:dynamic-service:${orgAhead4CloudVersion}") {
        exclude group: 'org.apache.groovy', module: 'groovy'
    }
    implementation 'com.baomidou:mybatis-plus-generator:3.4.1'
    // easyExcel 依赖commons-io版本过低，无法使用4.0以上版本
    implementation 'com.alibaba:easyexcel:3.3.4'
    implementation 'cn.hutool:hutool-all:5.8.26'

    implementation 'net.unicon.cas:cas-client-autoconfig-support:2.1.0-GA'
    implementation 'org.flowable:flowable-spring-boot-starter:6.8.1'
    implementation 'com.googlecode.aviator:aviator:5.4.3'
    implementation 'org.apache.commons:commons-collections4:4.4'
    implementation 'org.apache.commons:commons-lang3:3.12.0'
    implementation 'org.apache.commons:commons-text:1.9'
}

tasks.named('test') {
    useJUnitPlatform()
}
