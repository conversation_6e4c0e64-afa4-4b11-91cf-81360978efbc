<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ahead4.cdes.mapper.SmsReplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.ahead4.cdes.entity.SmsReply">
        <id column="id" property="id"/>
        <result column="reply_id" property="replyId"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="call_mdn" property="callMdn"/>
        <result column="content" property="content"/>
        <result column="reply_time" property="replyTime"/>
        <result column="is_confirmed" property="isConfirmed"/>
        <result column="confirm_time" property="confirmTime"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, reply_id, phone_number, call_mdn, content, reply_time, is_confirmed, confirm_time,
        create_time, create_by, update_time, update_by
    </sql>

    <!-- 批量插入短信上行回复 -->
    <insert id="insertBatch">
        INSERT INTO t_sms_reply (
            reply_id, phone_number, call_mdn, content, reply_time, is_confirmed, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.replyId}, #{item.phoneNumber}, #{item.callMdn}, #{item.content}, 
            #{item.replyTime}, #{item.isConfirmed}, now()
            )
        </foreach>
    </insert>
</mapper>
