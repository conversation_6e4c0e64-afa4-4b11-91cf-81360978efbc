<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ahead4.cdes.mapper.CodeMapper">

    <update id="initBillDailyCode">
        create sequence  if not exists ${sequenceName} start with 1 increment by 1
    </update>

    <delete id="clearExpiredCode">
         DROP SEQUENCE IF EXISTS #{sequenceName};
    </delete>

    <select id="getSequenceList" resultType="java.lang.String">
        SELECT sequence_name FROM information_schema.sequences WHERE sequence_name LIKE concat('%' , #{date})
    </select>
</mapper>
