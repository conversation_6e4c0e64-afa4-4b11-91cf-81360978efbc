<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ahead4.cdes.mapper.DocFlowMapper">

    <select id="ccPage" resultType="org.ahead4.cdes.entity.DocFlow">
        select d.*
        from wf_copy c
        inner join hb_doc_flow d on c.business_key = d.id
        <where> d.del_flag = '0' and c.user_id = #{username}
        <if test="ew.customSqlSegment != null and ew.customSqlSegment != ''">
            AND ${ew.customSqlSegment.substring(5)}
        </if>
        </where>
        order by c.create_time desc

    </select>

    <select id="finishedList" resultType="org.ahead4.cdes.entity.DocFlow">
        select  id, flow_code, doc_title, doc_type, priority_level, is_remind, doc_desp,
         is_public, make_copy, del_flag, creator, create_time, updator, update_time,
        proc_inst_id, "status"
        from hb_doc_flow
        where proc_inst_id in (
            select proc_inst_id_ from ACT_HI_TASKINST
            where ASSIGNEE_ = #{username}
              and END_TIME_ is not null
            group by proc_inst_id_
        )
        and del_flag = '0'
        <if test="ew.customSqlSegment != null and ew.customSqlSegment != ''">
             <choose>
                <when test="ew.customSqlSegment.trim().startsWith('where') || ew.customSqlSegment.trim().startsWith('WHERE')">
                    AND  ${ew.customSqlSegment.substring(5)}
                </when>
                <otherwise>
                    ${ew.customSqlSegment}
                </otherwise>
            </choose>
        </if>
    </select>
</mapper>