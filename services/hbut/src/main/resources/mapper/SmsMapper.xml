<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ahead4.cdes.mapper.SmsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.ahead4.cdes.entity.SmsRecord">
        <id column="id" property="id"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="content" property="content"/>
        <result column="type" property="type"/>
        <result column="template_id" property="templateId"/>
        <result column="template_params" property="templateParams"/>
        <result column="status" property="status"/>
        <result column="send_time" property="sendTime"/>
        <result column="receipt_time" property="receiptTime"/>
        <result column="receipt_code" property="receiptCode"/>
        <result column="receipt_desc" property="receiptDesc"/>
        <result column="business_type" property="businessType"/>
        <result column="business_id" property="businessId"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="task_id" property="taskId"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, phone_number, content, type, template_id, template_params, status, send_time,
        receipt_time, receipt_code, receipt_desc, business_type, business_id, serial_number, task_id,
        create_time, create_by, update_time, update_by
    </sql>

    <!-- 分页查询短信记录 -->
    <select id="selectSmsRecordPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sms_record
        <where>
            <if test="phoneNumber != null and phoneNumber != ''">
                AND phone_number = #{phoneNumber}
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                AND serial_number = #{serialNumber}
            </if>
            <if test="businessType != null and businessType != ''">
                AND business_type = #{businessType}
            </if>
            <if test="businessId != null and businessId != ''">
                AND business_id = #{businessId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="startTime != null and startTime != ''">
                AND send_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND send_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY send_time DESC
    </select>

    <update id="updateBatchBySerialNumberAndPhoneNumber">
        <foreach collection="list" item="item" separator=";">
            UPDATE t_sms_record
            <set>
                <if test="item.status != null">
                    status = #{item.status},
                </if>
                <if test="item.receiptDesc != null and item.receiptDesc != ''">
                    receipt_desc = #{item.receiptDesc},
                </if>
                <if test="item.receiptCode != null and item.receiptCode != ''">
                    receipt_code = #{item.receiptCode},
                </if>
                <if test="item.receiptTime != null">
                    receipt_time = #{item.receiptTime},
                </if>
                <if test="item.taskId != null and item.taskId != ''">
                    task_id = #{item.taskId},
                </if>
                update_time = NOW()
            </set>
            WHERE serial_number = #{item.serialNumber}
            AND phone_number = #{item.phoneNumber}
        </foreach>
    </update>
</mapper>
