server:
  port: 8293
  servlet:
    context-path: /docFlow-v1
spring:
  application:
    name: DocFlow-service
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  datasource:
    dynamic:
      primary: '主数据库' #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        "[主数据库]":
          druid:
            initial-size: 5
            min-idle: 5
            max-active: 100
            max-wait: 6000
            time-between-eviction-runs-millis: 6000
            min-evictable-idle-time-millis: 30000
            validation-query: SELECT 1
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://${DATABASE_HOST:*************}:${DATABASE_PORT:25445}/${DATABASE:hbut}?tcpKeepAlive=true&reWriteBatchedInserts=true&ApplicationName=${spring.application.name}&stringtype=unspecified
          username: ${DATABASE_USERNAME:hbut}
          password: ${DATABASE_PASSWORD:nlwUBWY8}
  redis:
    # ${REDIS_PASSWORD:}
    password: ${REDIS_PASSWORD:ahead4@redis}
    database: ${REDIS_DB:1}
    host: ${REDIS_HOST:*************}
    port: ${REDIS_PORT:21381}
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        min-idle: 0
        max-idle: 8

mybatis-plus:
  #  config-location: classpath:mybatis/mybatis-config.xml
  mapper-locations: classpath*:mybatis/mapper/*Dao.xml,classpath*:mybatis/mapper/*Mapper.xml,classpath*:mapper/*Dao.xml,classpath*:mapper/**/*Mapper.xml
  configuration:
    #log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    #全局映射启用缓存
    cache-enabled: true
    use-generated-keys: true
    default-executor-type: reuse
    map-underscore-to-camel-case: true
    #打开延迟加载的开关
    lazy-loading-enabled: true
    #按需要加载
    aggressive-lazy-loading: false
    #修改默认的脚本引擎为重写的 MybatisXMLLanguage
    default-scripting-language: org.ahead4.jdbc.mybatis.DynamicMybatisXMLLanguageDriver


flowable:
  database-schema-update: true
  history-level: full
#  async-executor-activate: false
  process-definition-location-prefix: classpath:/processes/
  process-definition-location-suffixes:
    - doc_flow.bpmn20.xml
    - 0411-doc_flow.bpmn20.xml
    - 0531-doc_flow.bpmn20.xml
#    - .bpmn

sysconfig:
  # 业主行政区划
  owner-area-code: 4201
  # 是否显示业主行政区划上级
  owner-area-show-superior: true
  # 默认缓存设置
  default-cache: REDIS
  # 默认缓存设置
  token-store: CACHE_SERVICE

# 短信服务配置
sms:
  dev: false # 是否为调试模式
  super-code: FK2MHH00456y# # 调试模式下此验证码可以直接通过短信认证
  enable: false
  sp-code: ${SMS_SP_CODE:223179}
  # 用户名
  login-name: ${SMS_LOGIN_NAME:hb_gydx}
  # 接口密钥
  password: ${SMS_PASSWORD:ey5hBLa0xgq6f5}
  # 接口地址
  api-url: ${SMS_API_URL:https://api.ums86.com:9600/sms/Api}
  # 发送短信接口
  send-url: ${SMS_SEND_URL:/Send.do}
  # 回执查询接口
  report-url: ${SMS_REPORT_URL:/report.do}
  # 上行回复查询接口
  reply-url: ${SMS_REPLY_URL:/reply.do}
  # 上行回复确认接口
  reply-confirm-url: ${SMS_REPLY_CONFIRM_URL:/replyConfirm.do}
  # 验证码配置
  verification-code:
    # 验证码有效期（分钟）
    expire-minutes: 5
    # 验证码短信模板ID
    template-id: ${SMS_VERIFICATION_CODE_TEMPLATE_ID:2431012310488}
    # 验证码获取间隔时间（秒）
    content: ${SMS_VERIFICATION_CODE_CONTENT:您的验证码为${code}，有效期${expireMinutes}分钟}
  # 同一手机号24小时内验证码发送最大次数
  max-verification-code-per-day: 10
  # 同一IP 24小时内验证码请求最大次数
  max-request-per-ip-per-day: 30
  # 代理号码池
  proxy-num-pool:


token:
  #token有效时间，单位分钟 24*60=1440
  expireTime: 1440
  #token加密密钥
  secretKey: b5df7b6c-7239-480d-851c-1159b0fbd3c2
  #初始密码后缀
  init-password: $123init

cas:
  server-url-prefix: https://auth.hbut.edu.cn/authserver
  server-login-url: https://auth.hbut.edu.cn/authserver/login
  client-host-url: https://tools.hbut.edu.cn
  validation-type: cas3
  authentication-url-patterns:
    - /cas-auth/login
    - /cas-auth/login/doc
    - /cas-auth/login/email
    - /cas-auth/login/email-manage
allow:
  sso:
    cas: true
exclude:
  paths:
    - /csrf
    - /sys-auth/token/**
    - /suppliers/register
    - /security/**
    - /api/**
    - /error
    - /swagger-ui/**
    - /swagger-ui.html
    - /v2/api-docs
    - /swagger/**
    - /swagger-resources/**
    - /webjars/**
    - /metrics/**
    - /cas-auth/**
tencent:
  copid:
  email-secret:
  log-secret:
  allow:
    delete: false
  user-policy:
    step-length: -90
    step-num: 1
    chunk-size: 1
    disable: false
hbut:
  docking:
    oa:
      base-url: https://cube.hbut.edu.cn
      appid:
      secret:
    dc:
      base-url: https://oshall.hbut.edu.cn
      secret:
      decrypt-secret:


kkview:
  # 容器名称 kkview:8012/kkview
  host: kkview:8012/kkview/

logging:
  file:
    path: /log
    name: micro-default.log
  level:
    org.springframework.web: info
    org.springframework.security: info
    org.springframework.security.web: info
    org.springframework.security.authentication: info
    org.springframework.security.config: info
    com.baomidou.mybatisplus: warn
    #org:flowable: debug
    org.flowable.common.engine.impl.persistence: debug
    org.flowable.job.service.impl.persistence: debug
    org.flowable.common.engine.impl.persistence.entity.PropertyEntityImpl.selectProperty: info
    org.flowable.job.service.impl.persistence.entity.JobEntityImpl.selectJobsToExecute: info
    org.flowable.job.service.impl.persistence.entity.TimerJobEntityImpl.selectTimerJobsToExecute: info
    org.flowable.common.engine.impl.persistence.TimerJobEntityImpl.selectTimerJobsToExecute: info
    org.flowable.job.service.impl.persistence.entity.ExternalWorkerJobEntityImpl.selectExpiredExternalWorkerJobs: info
    org.flowable.job.service.impl.persistence.entity.TimerJobEntityImpl.selectExpiredTimerJobs: info
    org.flowable.job.service.impl.persistence.entity.JobEntityImpl.selectExpiredJobs: info

