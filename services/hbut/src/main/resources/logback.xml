<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="1 seconds">

    <contextName>logback</contextName>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
        <!--        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">-->
        <!--            <evaluator class="ch.qos.logback.classic.boolex.JaninoEventEvaluator">-->
        <!--                <expression>-->
        <!--                    return event.getThreadName().startsWith("flowable-bpmn-acquire-timer-jobs") ||-->
        <!--                    event.getThreadName().startsWith("flowable-bpmn-acquire-async-jobs") ||-->
        <!--                    event.getThreadName().startsWith("flowable-cmmn-acquire-timer-jobs") ||-->
        <!--                    event.getThreadName().startsWith("flowable-cmmn-acquire-async-jobs") ||-->
        <!--                    event.getThreadName().startsWith("flowable-bpmn-reset-expired-jobs") ||-->
        <!--                    event.getThreadName().startsWith("flowable-cmmn-reset-expired-jobs");-->
        <!--                </expression>-->
        <!--            </evaluator>-->
        <!--            <onMatch>DENY</onMatch>-->
        <!--            <onMismatch>NEUTRAL</onMismatch>-->
        <!--        </filter>-->
    </appender>

    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${LOG_FILE}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${LOG_FILE}.%d{yyyy-MM-dd}.%i.zip</fileNamePattern>
            <maxFileSize>20MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>%date %level [%thread] %logger [%file : %line] %msg%n
            </pattern>
        </encoder>
    </appender>

<!--    <logger name="org.apache.ibatis" level="DEBUG"/>-->
<!--    <logger name="org.mybatis" level="DEBUG"/>-->
<!--    <logger name="java.sql" level="DEBUG"/>-->
<!--    <logger name="org.apache.ibatis.logging" level="DEBUG"/>-->

    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </root>

</configuration>