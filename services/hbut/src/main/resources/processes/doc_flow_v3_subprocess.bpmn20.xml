<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL"
                   xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                   xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                   xmlns:flowable="http://flowable.org/bpmn" id="diagram_Process_dept_subprocess_v3"
                   targetNamespace="http://flowable.org/bpmn"
                   xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
    <bpmn2:process id="doc_flow_v3_subprocess" name="业务部门处理子流程V3" isExecutable="true">
        <bpmn2:startEvent id="SubEvent_start">
            <bpmn2:outgoing>SubFlow_start_to_dept_head</bpmn2:outgoing>
        </bpmn2:startEvent>
        
        <bpmn2:userTask id="SubActivity_dept_head" name="部门负责人" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="POST" flowable:assignee="${assignee.username}"
                        flowable:candidateGroups="POSTDeptHead" flowable:text="${assignee.deptName}">
            <bpmn2:extensionElements>
                <flowable:documentation>deptHead</flowable:documentation>
                <flowable:taskListener expression="${docFlowV3WfHandler.updateStatus(execution)}" event="assignment"/>
                <flowable:taskListener expression="${docFlowV3WfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>SubFlow_start_to_dept_head</bpmn2:incoming>
            <bpmn2:incoming>Flow_back_to_dept_head</bpmn2:incoming>
            <bpmn2:outgoing>SubFlow_dept_head_to_gateway</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics
                    flowable:collection="${docFlowV3WfHandler.getUserByDeptCode(execution, deptCode, SubActivity_dept_head, 'leader')}"
                    flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">
                    ${nrOfCompletedInstances &gt;= nrOfInstances}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        
        <bpmn2:exclusiveGateway id="SubGateway_approval" name="审批结果">
            <bpmn2:incoming>SubFlow_dept_head_to_gateway</bpmn2:incoming>
            <bpmn2:outgoing>SubFlow_approved</bpmn2:outgoing>
            <bpmn2:outgoing>SubFlow_rejected</bpmn2:outgoing>
        </bpmn2:exclusiveGateway>
        
        <bpmn2:userTask id="SubActivity_doc_manager_reject" name="校办公文负责人"
                        flowable:formKey="key_1906235449754161153" flowable:dataType="INITIATOR"
                        flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:extensionElements>
                <flowable:documentation>docManagerReject</flowable:documentation>
                <flowable:taskListener expression="${docFlowV3WfHandler.updateStatus(execution)}" event="assignment"/>
                <flowable:taskListener expression="${docFlowV3WfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>SubFlow_rejected</bpmn2:incoming>
            <bpmn2:outgoing>SubFlow_doc_manager_to_deputy</bpmn2:outgoing>
        </bpmn2:userTask>
        
        <bpmn2:userTask id="SubActivity_deputy_reject" name="校办副主任" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="DEPT:AND:POST" flowable:assignee="${assignee}"
                        flowable:candidateGroups="DEPT10101,POSTDeputyHead" flowable:text="校办副主任">
            <bpmn2:extensionElements>
                <flowable:documentation>deputyReject</flowable:documentation>
                <flowable:taskListener expression="${docFlowV3WfHandler.updateStatus(execution)}" event="assignment"/>
                <flowable:taskListener expression="${docFlowV3WfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>SubFlow_doc_manager_to_deputy</bpmn2:incoming>
            <bpmn2:outgoing>SubFlow_deputy_to_gateway2</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics flowable:collection="${docFlowV3WfHandler.getUserIds(execution)}"
                                                    flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">${nrOfCompletedInstances &gt; 0}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        
        <bpmn2:exclusiveGateway id="SubGateway_need_leader" name="是否需要领导审批">
            <bpmn2:incoming>SubFlow_deputy_to_gateway2</bpmn2:incoming>
            <bpmn2:outgoing>SubFlow_leader_needed</bpmn2:outgoing>
            <bpmn2:outgoing>SubFlow_no_leader_needed</bpmn2:outgoing>
        </bpmn2:exclusiveGateway>
        
        <bpmn2:userTask id="SubActivity_school_leader" name="校领导批示" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="POST" flowable:assignee="${assignee.username}"
                        flowable:candidateGroups="ROLEPresident" flowable:text="校领导">
            <bpmn2:extensionElements>
                <flowable:documentation>schoolLeaderApproval</flowable:documentation>
                <flowable:taskListener expression="${docFlowV3WfHandler.updateStatus(execution)}" event="assignment"/>
                <flowable:taskListener expression="${docFlowV3WfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>SubFlow_leader_needed</bpmn2:incoming>
            <bpmn2:outgoing>SubFlow_leader_to_final_doc_manager</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics isSequential="true"
                                                    flowable:collection="${SubActivity_school_leader}"
                                                    flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">
                    ${nrOfCompletedInstances &gt;= nrOfInstances}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        
        <bpmn2:userTask id="SubActivity_final_doc_manager" name="校办公文负责人"
                        flowable:formKey="key_1906235449754161153" flowable:dataType="INITIATOR"
                        flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:extensionElements>
                <flowable:documentation>finalDocManager</flowable:documentation>
                <flowable:taskListener expression="${docFlowV3WfHandler.updateStatus(execution)}" event="assignment"/>
                <flowable:taskListener expression="${docFlowV3WfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>SubFlow_leader_to_final_doc_manager</bpmn2:incoming>
            <bpmn2:incoming>SubFlow_no_leader_needed</bpmn2:incoming>
            <bpmn2:outgoing>SubFlow_final_to_gateway</bpmn2:outgoing>
        </bpmn2:userTask>
        
        <bpmn2:exclusiveGateway id="SubGateway_need_dept_proc" name="是否需要部门处理">
            <bpmn2:incoming>SubFlow_final_to_gateway</bpmn2:incoming>
            <bpmn2:outgoing>SubFlow_need_dept_proc</bpmn2:outgoing>
            <bpmn2:outgoing>SubFlow_no_dept_proc</bpmn2:outgoing>
        </bpmn2:exclusiveGateway>
        
        <bpmn2:endEvent id="SubEvent_approved_end">
            <bpmn2:incoming>SubFlow_approved</bpmn2:incoming>
            <bpmn2:incoming>SubFlow_no_dept_proc</bpmn2:incoming>
        </bpmn2:endEvent>
        
        <!-- 流程连线 -->
        <bpmn2:sequenceFlow id="SubFlow_start_to_dept_head" sourceRef="SubEvent_start" targetRef="SubActivity_dept_head"/>
        <bpmn2:sequenceFlow id="SubFlow_dept_head_to_gateway" sourceRef="SubActivity_dept_head" targetRef="SubGateway_approval"/>
        <bpmn2:sequenceFlow id="SubFlow_approved" name="已阅" sourceRef="SubGateway_approval" targetRef="SubEvent_approved_end">
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${deptHeadResult == 'approved'}</bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
        <bpmn2:sequenceFlow id="SubFlow_rejected" name="不通过" sourceRef="SubGateway_approval" targetRef="SubActivity_doc_manager_reject">
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${deptHeadResult == 'rejected'}</bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
        <bpmn2:sequenceFlow id="SubFlow_doc_manager_to_deputy" sourceRef="SubActivity_doc_manager_reject" targetRef="SubActivity_deputy_reject"/>
        <bpmn2:sequenceFlow id="SubFlow_deputy_to_gateway2" sourceRef="SubActivity_deputy_reject" targetRef="SubGateway_need_leader"/>
        <bpmn2:sequenceFlow id="SubFlow_leader_needed" name="领导批示" sourceRef="SubGateway_need_leader" targetRef="SubActivity_school_leader">
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${needLeaderApproval == true}</bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
        <bpmn2:sequenceFlow id="SubFlow_no_leader_needed" name="无需批示" sourceRef="SubGateway_need_leader" targetRef="SubActivity_final_doc_manager">
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${needLeaderApproval == false}</bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
        <bpmn2:sequenceFlow id="SubFlow_leader_to_final_doc_manager" sourceRef="SubActivity_school_leader" targetRef="SubActivity_final_doc_manager"/>
        <bpmn2:sequenceFlow id="SubFlow_final_to_gateway" sourceRef="SubActivity_final_doc_manager" targetRef="SubGateway_need_dept_proc"/>
        <bpmn2:sequenceFlow id="SubFlow_need_dept_proc" name="部门处理" sourceRef="SubGateway_need_dept_proc" targetRef="SubActivity_dept_head">
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${needDeptProc == true}</bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
        <bpmn2:sequenceFlow id="SubFlow_no_dept_proc" name="部门无需处理" sourceRef="SubGateway_need_dept_proc" targetRef="SubEvent_approved_end">
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${needDeptProc == false}</bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
        <bpmn2:sequenceFlow id="Flow_back_to_dept_head" sourceRef="SubGateway_need_dept_proc" targetRef="SubActivity_dept_head"/>
    </bpmn2:process>
    
    <!-- BPMN图形信息 -->
    <bpmndi:BPMNDiagram id="SubBPMNDiagram_v3">
        <bpmndi:BPMNPlane id="SubBPMNPlane_v3" bpmnElement="doc_flow_v3_subprocess">
            <bpmndi:BPMNEdge id="SubFlow_start_to_dept_head_di" bpmnElement="SubFlow_start_to_dept_head">
                <di:waypoint x="188" y="300"/>
                <di:waypoint x="240" y="300"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_dept_head_to_gateway_di" bpmnElement="SubFlow_dept_head_to_gateway">
                <di:waypoint x="340" y="300"/>
                <di:waypoint x="395" y="300"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_approved_di" bpmnElement="SubFlow_approved">
                <di:waypoint x="420" y="275"/>
                <di:waypoint x="420" y="200"/>
                <di:waypoint x="1032" y="200"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_rejected_di" bpmnElement="SubFlow_rejected">
                <di:waypoint x="445" y="300"/>
                <di:waypoint x="500" y="300"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_doc_manager_to_deputy_di" bpmnElement="SubFlow_doc_manager_to_deputy">
                <di:waypoint x="600" y="300"/>
                <di:waypoint x="640" y="300"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_deputy_to_gateway2_di" bpmnElement="SubFlow_deputy_to_gateway2">
                <di:waypoint x="740" y="300"/>
                <di:waypoint x="775" y="300"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_leader_needed_di" bpmnElement="SubFlow_leader_needed">
                <di:waypoint x="800" y="275"/>
                <di:waypoint x="800" y="240"/>
                <di:waypoint x="860" y="240"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_no_leader_needed_di" bpmnElement="SubFlow_no_leader_needed">
                <di:waypoint x="825" y="300"/>
                <di:waypoint x="1020" y="300"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_leader_to_final_doc_manager_di" bpmnElement="SubFlow_leader_to_final_doc_manager">
                <di:waypoint x="910" y="280"/>
                <di:waypoint x="910" y="300"/>
                <di:waypoint x="1020" y="300"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_final_to_gateway_di" bpmnElement="SubFlow_final_to_gateway">
                <di:waypoint x="1070" y="340"/>
                <di:waypoint x="1070" y="375"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_need_dept_proc_di" bpmnElement="SubFlow_need_dept_proc">
                <di:waypoint x="1045" y="400"/>
                <di:waypoint x="290" y="400"/>
                <di:waypoint x="290" y="340"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_no_dept_proc_di" bpmnElement="SubFlow_no_dept_proc">
                <di:waypoint x="1095" y="400"/>
                <di:waypoint x="1150" y="400"/>
                <di:waypoint x="1150" y="200"/>
                <di:waypoint x="1068" y="200"/>
            </bpmndi:BPMNEdge>
            
            <!-- 节点图形定义 -->
            <bpmndi:BPMNShape id="SubEvent_start_di" bpmnElement="SubEvent_start">
                <dc:Bounds x="152" y="282" width="36" height="36"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubActivity_dept_head_di" bpmnElement="SubActivity_dept_head">
                <dc:Bounds x="240" y="260" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubGateway_approval_di" bpmnElement="SubGateway_approval" isMarkerVisible="true">
                <dc:Bounds x="395" y="275" width="50" height="50"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubActivity_doc_manager_reject_di" bpmnElement="SubActivity_doc_manager_reject">
                <dc:Bounds x="500" y="260" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubActivity_deputy_reject_di" bpmnElement="SubActivity_deputy_reject">
                <dc:Bounds x="640" y="260" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubGateway_need_leader_di" bpmnElement="SubGateway_need_leader" isMarkerVisible="true">
                <dc:Bounds x="775" y="275" width="50" height="50"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubActivity_school_leader_di" bpmnElement="SubActivity_school_leader">
                <dc:Bounds x="860" y="200" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubActivity_final_doc_manager_di" bpmnElement="SubActivity_final_doc_manager">
                <dc:Bounds x="1020" y="260" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubGateway_need_dept_proc_di" bpmnElement="SubGateway_need_dept_proc" isMarkerVisible="true">
                <dc:Bounds x="1045" y="375" width="50" height="50"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubEvent_approved_end_di" bpmnElement="SubEvent_approved_end">
                <dc:Bounds x="1032" y="182" width="36" height="36"/>
            </bpmndi:BPMNShape>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn2:definitions>
