<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL"
                   xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                   xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                   xmlns:flowable="http://flowable.org/bpmn" id="diagram_Process_1743599583451"
                   targetNamespace="http://flowable.org/bpmn"
                   xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
    <bpmn2:process id="doc_flow" name="公文流转" isExecutable="true">
        <bpmn2:startEvent id="Event_1vncqpy">
            <bpmn2:outgoing>Flow_1igocn0</bpmn2:outgoing>
        </bpmn2:startEvent>
        <bpmn2:sequenceFlow id="Flow_1igocn0" sourceRef="Event_1vncqpy" targetRef="Activity_0ajb0z4"/>
        <bpmn2:userTask id="Activity_0ajb0z4" name="提交申请" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:extensionElements>
                <flowable:documentation>submit</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment" />
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_1igocn0</bpmn2:incoming>
            <bpmn2:outgoing>Flow_1n2gn8m</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:userTask id="schoolLeader" name="校领导" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="POST" flowable:assignee="${assignee}"
                        flowable:candidateGroups="ROLEPresident"
                        flowable:text="校领导,校领导">
            <bpmn2:extensionElements>
                <flowable:documentation>schoolLeader</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment" />
                <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_1n2gn8m</bpmn2:incoming>
            <bpmn2:outgoing>Flow_035fr1h</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics isSequential="false"
                                                    flowable:collection="${docFlowWfHandler.getUserIds(execution)}"
                                                    flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_1n2gn8m" sourceRef="Activity_0ajb0z4" targetRef="schoolLeader"/>
        <bpmn2:userTask id="Activity_13rmy52" name="校办处理" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:extensionElements>
                <flowable:documentation>officeHandling</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment" />
                <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_035fr1h</bpmn2:incoming>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_035fr1h" sourceRef="schoolLeader" targetRef="Activity_13rmy52"/>
        <bpmn2:userTask id="inChargeLeader" name="分管领导" flowable:dataType="CCDTO"
                        flowable:text="分管领导"
                        flowable:formKey="key_1906235449754161153" flowable:assignee="${assignee}">
            <bpmn2:extensionElements>
                <flowable:documentation>inChargeLeader</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment" />
                <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:outgoing>Flow_0uh74oi</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics isSequential="false"
                                                    flowable:collection="${docFlowWfHandler.getUserIds(execution)}"
                                                    flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">
                    ${nrOfCompletedInstances &gt;= nrOfInstances}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_15xh211" sourceRef="Activity_13rmy52" targetRef="inChargeLeader"/>
        <bpmn2:userTask id="Activity_0taj9qw" name="校办分发" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:extensionElements>
                <flowable:documentation>officeDistribution</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment" />
                <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_0uh74oi</bpmn2:incoming>
            <bpmn2:outgoing>Flow_1jkigwq</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_0uh74oi" sourceRef="inChargeLeader" targetRef="Activity_0taj9qw"/>
        <bpmn2:userTask id="Activity_0ps2oli" name="业务部门处理" flowable:formKey="key_1906235449754161153"
        flowable:dataType="CCDTO" flowable:assignee="${assignee}" >
            <bpmn2:extensionElements>
                <flowable:documentation>departmentHandling</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment" />
                <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:outgoing>Flow_17jqb60</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics
                    flowable:collection="${docFlowWfHandler.getUserIds(execution)}"
                                                    flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">
                    ${nrOfCompletedInstances &gt;= nrOfInstances}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_1jkigwq" sourceRef="Activity_0taj9qw" targetRef="Activity_0ps2oli"/>
        <bpmn2:userTask id="Activity_1pc53bv" name="校办归档" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:extensionElements>
                <flowable:documentation>officeArchiving</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment" />
                <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_17jqb60</bpmn2:incoming>
            <bpmn2:outgoing>Flow_0trcfoh</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_17jqb60" sourceRef="Activity_0ps2oli" targetRef="Activity_1pc53bv"/>
        <bpmn2:endEvent id="Event_06e6pt1">
            <bpmn2:incoming>Flow_0trcfoh</bpmn2:incoming>
        </bpmn2:endEvent>
        <bpmn2:sequenceFlow id="Flow_0trcfoh" sourceRef="Activity_1pc53bv" targetRef="Event_06e6pt1"/>
    </bpmn2:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1743599583451">
            <bpmndi:BPMNEdge id="Flow_0trcfoh_di" bpmnElement="Flow_0trcfoh">
                <di:waypoint x="1420" y="270"/>
                <di:waypoint x="1502" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_17jqb60_di" bpmnElement="Flow_17jqb60">
                <di:waypoint x="1240" y="270"/>
                <di:waypoint x="1320" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1jkigwq_di" bpmnElement="Flow_1jkigwq">
                <di:waypoint x="1060" y="270"/>
                <di:waypoint x="1140" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0uh74oi_di" bpmnElement="Flow_0uh74oi">
                <di:waypoint x="880" y="270"/>
                <di:waypoint x="960" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_15xh211_di" bpmnElement="Flow_15xh211">
                <di:waypoint x="700" y="270"/>
                <di:waypoint x="780" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_035fr1h_di" bpmnElement="Flow_035fr1h">
                <di:waypoint x="520" y="270"/>
                <di:waypoint x="600" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1n2gn8m_di" bpmnElement="Flow_1n2gn8m">
                <di:waypoint x="340" y="270"/>
                <di:waypoint x="420" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1igocn0_di" bpmnElement="Flow_1igocn0">
                <di:waypoint x="208" y="270"/>
                <di:waypoint x="240" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="Event_1vncqpy_di" bpmnElement="Event_1vncqpy">
                <dc:Bounds x="172" y="252" width="36" height="36"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0ajb0z4_di" bpmnElement="Activity_0ajb0z4">
                <dc:Bounds x="240" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="schoolLeader_di" bpmnElement="schoolLeader">
                <dc:Bounds x="420" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_13rmy52_di" bpmnElement="Activity_13rmy52">
                <dc:Bounds x="600" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="inChargeLeader_di" bpmnElement="inChargeLeader">
                <dc:Bounds x="780" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0taj9qw_di" bpmnElement="Activity_0taj9qw">
                <dc:Bounds x="960" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0ps2oli_di" bpmnElement="Activity_0ps2oli">
                <dc:Bounds x="1140" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1pc53bv_di" bpmnElement="Activity_1pc53bv">
                <dc:Bounds x="1320" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_06e6pt1_di" bpmnElement="Event_06e6pt1">
                <dc:Bounds x="1502" y="252" width="36" height="36"/>
            </bpmndi:BPMNShape>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn2:definitions>



