<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL"
                   xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                   xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                   xmlns:flowable="http://flowable.org/bpmn" id="diagram_Process_1743599583451"
                   targetNamespace="http://flowable.org/bpmn"
                   xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
    <bpmn2:process id="Process_1743599583451" name="公文流转" isExecutable="true">
        <bpmn2:startEvent id="Event_1vncqpy">
            <bpmn2:outgoing>Flow_1igocn0</bpmn2:outgoing>
        </bpmn2:startEvent>
        <bpmn2:sequenceFlow id="Flow_1igocn0" sourceRef="Event_1vncqpy" targetRef="Activity_0ajb0z4"/>
        <bpmn2:userTask id="Activity_0ajb0z4" name="较办发起公文流转" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:incoming>Flow_1igocn0</bpmn2:incoming>
            <bpmn2:outgoing>Flow_1n2gn8m</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:userTask id="Activity_1948irg" name="校领导" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="DEPT:AND:ROLE" flowable:assignee="${assignee}"
                        flowable:candidateGroups="DEPT10100,ROLEschoolLeader"
                        flowable:text="校领导,校领导">
            <bpmn2:incoming>Flow_1n2gn8m</bpmn2:incoming>
            <bpmn2:outgoing>Flow_035fr1h</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics isSequential="true"
                                                    flowable:collection="${multiInstanceHandler.getUserIds(execution)}"
                                                    flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">
                    ${nrOfCompletedInstances &gt;= nrOfInstances}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_1n2gn8m" sourceRef="Activity_0ajb0z4" targetRef="Activity_1948irg"/>
        <bpmn2:userTask id="Activity_13rmy52" name="校办" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:incoming>Flow_035fr1h</bpmn2:incoming>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_035fr1h" sourceRef="Activity_1948irg" targetRef="Activity_13rmy52"/>
        <bpmn2:userTask id="Activity_0le4zpe" name="分管领导" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="ROLES" flowable:assignee="${assignee}"
                        flowable:candidateUsers="ROLEinChargeLeader"
                        flowable:text="若依,若依管理员">
            <bpmn2:outgoing>Flow_0uh74oi</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics flowable:collection="${multiInstanceHandler.getUserIds(execution)}"
                                                    flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">${nrOfCompletedInstances &gt; 0}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_15xh211" sourceRef="Activity_13rmy52" targetRef="Activity_0le4zpe"/>
        <bpmn2:userTask id="Activity_0taj9qw" name="校办" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:incoming>Flow_0uh74oi</bpmn2:incoming>
            <bpmn2:outgoing>Flow_1jkigwq</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_0uh74oi" sourceRef="Activity_0le4zpe" targetRef="Activity_0taj9qw"/>
        <bpmn2:userTask id="Activity_0ps2oli" name="职能单位/教学单位" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="DEPTS" flowable:assignee="${assignee}"
                        flowable:candidateUsers="DEPT:10112,DEPT10203,DEPT10201,DEPT10314"
                        flowable:text="职能部门,教学部门">
            <bpmn2:outgoing>Flow_17jqb60</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics flowable:collection="${multiInstanceHandler.getUserIds(execution)}"
                                                    flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">
                    ${nrOfCompletedInstances &gt;= nrOfInstances}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_1jkigwq" sourceRef="Activity_0taj9qw" targetRef="Activity_0ps2oli"/>
        <bpmn2:userTask id="Activity_1pc53bv" name="校办" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:incoming>Flow_17jqb60</bpmn2:incoming>
            <bpmn2:outgoing>Flow_0trcfoh</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:sequenceFlow id="Flow_17jqb60" sourceRef="Activity_0ps2oli" targetRef="Activity_1pc53bv"/>
        <bpmn2:endEvent id="Event_06e6pt1">
            <bpmn2:incoming>Flow_0trcfoh</bpmn2:incoming>
        </bpmn2:endEvent>
        <bpmn2:sequenceFlow id="Flow_0trcfoh" sourceRef="Activity_1pc53bv" targetRef="Event_06e6pt1"/>
    </bpmn2:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1743599583451">
            <bpmndi:BPMNEdge id="Flow_0trcfoh_di" bpmnElement="Flow_0trcfoh">
                <di:waypoint x="1420" y="270"/>
                <di:waypoint x="1502" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_17jqb60_di" bpmnElement="Flow_17jqb60">
                <di:waypoint x="1240" y="270"/>
                <di:waypoint x="1320" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1jkigwq_di" bpmnElement="Flow_1jkigwq">
                <di:waypoint x="1060" y="270"/>
                <di:waypoint x="1140" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0uh74oi_di" bpmnElement="Flow_0uh74oi">
                <di:waypoint x="880" y="270"/>
                <di:waypoint x="960" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_15xh211_di" bpmnElement="Flow_15xh211">
                <di:waypoint x="700" y="270"/>
                <di:waypoint x="780" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_035fr1h_di" bpmnElement="Flow_035fr1h">
                <di:waypoint x="520" y="270"/>
                <di:waypoint x="600" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1n2gn8m_di" bpmnElement="Flow_1n2gn8m">
                <di:waypoint x="340" y="270"/>
                <di:waypoint x="420" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1igocn0_di" bpmnElement="Flow_1igocn0">
                <di:waypoint x="208" y="270"/>
                <di:waypoint x="240" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="Event_1vncqpy_di" bpmnElement="Event_1vncqpy">
                <dc:Bounds x="172" y="252" width="36" height="36"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0ajb0z4_di" bpmnElement="Activity_0ajb0z4">
                <dc:Bounds x="240" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1948irg_di" bpmnElement="Activity_1948irg">
                <dc:Bounds x="420" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_13rmy52_di" bpmnElement="Activity_13rmy52">
                <dc:Bounds x="600" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0le4zpe_di" bpmnElement="Activity_0le4zpe">
                <dc:Bounds x="780" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0taj9qw_di" bpmnElement="Activity_0taj9qw">
                <dc:Bounds x="960" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0ps2oli_di" bpmnElement="Activity_0ps2oli">
                <dc:Bounds x="1140" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1pc53bv_di" bpmnElement="Activity_1pc53bv">
                <dc:Bounds x="1320" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_06e6pt1_di" bpmnElement="Event_06e6pt1">
                <dc:Bounds x="1502" y="252" width="36" height="36"/>
            </bpmndi:BPMNShape>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn2:definitions>