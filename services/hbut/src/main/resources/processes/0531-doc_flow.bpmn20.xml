<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL"
                   xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                   xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                   xmlns:flowable="http://flowable.org/bpmn" id="diagram_Process_0531"
                   targetNamespace="http://flowable.org/bpmn"
                   xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
    <bpmn2:process id="doc_flow_v2" name="公文流转" isExecutable="true">
        <bpmn2:startEvent id="Event_start">
            <bpmn2:outgoing>Flow_start_to_submit</bpmn2:outgoing>
        </bpmn2:startEvent>
        <bpmn2:userTask id="Activity_submit" name="发起公文流转" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:extensionElements>
                <flowable:documentation>submit</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_start_to_submit</bpmn2:incoming>
            <bpmn2:outgoing>Flow_submit_to_deputy</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:userTask id="Activity_deputy_director" name="校办副主任" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="DEPT:AND:POST" flowable:assignee="${assignee}"
                        flowable:candidateGroups="DEPT10101,POSTDeputyHead" flowable:text="校办副主任">
            <bpmn2:extensionElements>
                <flowable:documentation>deputyDirector</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment"/>
                <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_submit_to_deputy</bpmn2:incoming>
            <bpmn2:outgoing>Flow_deputy_to_doc_manager</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics flowable:collection="${docFlowWfHandler.getUserIds(execution)}"
                                                    flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">${nrOfCompletedInstances &gt; 0}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        <bpmn2:userTask id="Activity_doc_manager" name="公文负责人" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="公文负责人">
            <bpmn2:extensionElements>
                <flowable:documentation>docManager</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment"/>
                <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_deputy_to_doc_manager</bpmn2:incoming>
            <bpmn2:outgoing>Flow_doc_manager_to_school_leader</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:userTask id="Activity_school_leader" name="校领导" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="POST" flowable:assignee="${assignee.username}"
                        flowable:candidateGroups="ROLEPresident" flowable:text="校领导">
            <bpmn2:extensionElements>
                <flowable:documentation>schoolLeader</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment"/>
                <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_doc_manager_to_school_leader</bpmn2:incoming>
            <bpmn2:outgoing>Flow_school_leader_to_office_pre</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics isSequential="true" flowable:collection="${Activity_school_leader}"
                                                    flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">
                    ${nrOfCompletedInstances &gt;= nrOfInstances}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        <bpmn2:userTask id="Activity_office_handler_pre" name="公文负责人" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="公文负责人">
            <bpmn2:extensionElements>
                <flowable:documentation>docManager</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment"/>
                <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_school_leader_to_office_pre</bpmn2:incoming>
            <bpmn2:outgoing>Flow_office_pre_to_subprocess</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:subProcess id="Activity_dept_subprocess" name="业务部门处理">
            <bpmn2:extensionElements>
                <flowable:documentation>departmentHandling</flowable:documentation>
                <flowable:in source="selectedDepts" target="deptList"/>
                <flowable:in source="docId" target="docId"/>
                <flowable:in source="initiator" target="initiator"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_office_pre_to_subprocess</bpmn2:incoming>
            <bpmn2:outgoing>Flow_subprocess_to_office_handler</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics
                    flowable:collection="${docFlowV2WfHandler.getDynamicDepts(execution, Activity_dept_subprocess)}"
                    flowable:elementVariable="dept">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">
                    ${docFlowV2WfHandler.checkDynamicSubProcessCompletion(execution, nrOfCompletedInstances, nrOfInstances, nrOfActiveInstances)}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
            <bpmn2:startEvent id="SubEvent_start">
                <bpmn2:outgoing>SubFlow_start_to_dept_head</bpmn2:outgoing>
            </bpmn2:startEvent>
            <bpmn2:userTask id="SubActivity_dept_head" name="部门负责人" flowable:formKey="key_1906235449754161153"
                            flowable:dataType="POST" flowable:assignee="${assignee.username}"
                            flowable:candidateGroups="POSTDeptHead" flowable:text="${assignee.deptName}">
                <bpmn2:extensionElements>
                    <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}"
                                           event="assignment"/>
                    <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
                </bpmn2:extensionElements>
                <bpmn2:incoming>SubFlow_start_to_dept_head</bpmn2:incoming>
                <bpmn2:incoming>Flow_1xq7hr6</bpmn2:incoming>
                <bpmn2:outgoing>SubFlow_dept_head_to_gateway</bpmn2:outgoing>
                <bpmn2:multiInstanceLoopCharacteristics
                        flowable:collection="${docFlowV2WfHandler.getUserByDeptCode(execution, dept, Activity_dept_subprocess, 'leader')}"
                        flowable:elementVariable="assignee">
                    <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">
                        ${nrOfCompletedInstances &gt;= nrOfInstances}
                    </bpmn2:completionCondition>
                </bpmn2:multiInstanceLoopCharacteristics>
            </bpmn2:userTask>
            <bpmn2:exclusiveGateway id="SubGateway_approval" name="审批结果">
                <bpmn2:incoming>SubFlow_dept_head_to_gateway</bpmn2:incoming>
                <bpmn2:outgoing>SubFlow_approved</bpmn2:outgoing>
                <bpmn2:outgoing>SubFlow_rejected</bpmn2:outgoing>
            </bpmn2:exclusiveGateway>
            <bpmn2:endEvent id="SubEvent_approved_end">
                <bpmn2:incoming>Flow_0egzu84</bpmn2:incoming>
                <bpmn2:incoming>SubFlow_approved</bpmn2:incoming>
            </bpmn2:endEvent>
            <bpmn2:userTask id="SubActivity_doc_manager_reject" name="校办公文负责人"
                            flowable:formKey="key_1906235449754161153" flowable:dataType="INITIATOR"
                            flowable:assignee="${initiator}" flowable:text="流程发起人">
                <bpmn2:extensionElements>
                    <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}"
                                           event="assignment"/>
                    <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
                </bpmn2:extensionElements>
                <bpmn2:incoming>SubFlow_rejected</bpmn2:incoming>
                <bpmn2:outgoing>SubFlow_doc_manager_to_deputy</bpmn2:outgoing>
            </bpmn2:userTask>
            <bpmn2:userTask id="SubActivity_deputy_reject" name="校办副主任" flowable:formKey="key_1906235449754161153"
                            flowable:dataType="DEPT:AND:POST" flowable:assignee="${assignee}"
                            flowable:candidateGroups="DEPT10101,POSTDeputyHead" flowable:text="校办副主任">
                <bpmn2:extensionElements>
                    <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}"
                                           event="assignment"/>
                    <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
                </bpmn2:extensionElements>
                <bpmn2:incoming>SubFlow_doc_manager_to_deputy</bpmn2:incoming>
                <bpmn2:outgoing>SubFlow_deputy_to_gateway2</bpmn2:outgoing>
                <bpmn2:multiInstanceLoopCharacteristics flowable:collection="${docFlowWfHandler.getUserIds(execution)}"
                                                        flowable:elementVariable="assignee">
                    <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">${nrOfCompletedInstances &gt; 0}
                    </bpmn2:completionCondition>
                </bpmn2:multiInstanceLoopCharacteristics>
            </bpmn2:userTask>
            <bpmn2:exclusiveGateway id="SubGateway_need_leader" name="是否需要领导审批">
                <bpmn2:incoming>SubFlow_deputy_to_gateway2</bpmn2:incoming>
                <bpmn2:outgoing>SubFlow_leader_needed</bpmn2:outgoing>
                <bpmn2:outgoing>SubFlow_no_leader_needed</bpmn2:outgoing>
            </bpmn2:exclusiveGateway>
            <bpmn2:userTask id="SubActivity_school_leader" name="校领导批示" flowable:formKey="key_1906235449754161153"
                            flowable:dataType="POST" flowable:assignee="${assignee.username}"
                            flowable:candidateGroups="ROLEPresident" flowable:text="校领导">
                <bpmn2:extensionElements>
                    <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}"
                                           event="assignment"/>
                    <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
                </bpmn2:extensionElements>
                <bpmn2:incoming>SubFlow_leader_needed</bpmn2:incoming>
                <bpmn2:outgoing>SubFlow_leader_to_final_doc_manager</bpmn2:outgoing>
                <bpmn2:multiInstanceLoopCharacteristics isSequential="true"
                                                        flowable:collection="${SubActivity_school_leader}"
                                                        flowable:elementVariable="assignee">
                    <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">
                        ${nrOfCompletedInstances &gt;= nrOfInstances}
                    </bpmn2:completionCondition>
                </bpmn2:multiInstanceLoopCharacteristics>
            </bpmn2:userTask>
            <bpmn2:userTask id="SubActivity_final_doc_manager" name="校办公文负责人"
                            flowable:formKey="key_1906235449754161153" flowable:dataType="INITIATOR"
                            flowable:assignee="${initiator}" flowable:text="流程发起人">
                <bpmn2:extensionElements>
                    <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}"
                                           event="assignment"/>
                    <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
                </bpmn2:extensionElements>
                <bpmn2:incoming>SubFlow_leader_to_final_doc_manager</bpmn2:incoming>
                <bpmn2:incoming>SubFlow_no_leader_needed</bpmn2:incoming>
                <bpmn2:outgoing>Flow_0ab3f9i</bpmn2:outgoing>
            </bpmn2:userTask>
            <bpmn2:sequenceFlow id="SubFlow_start_to_dept_head" sourceRef="SubEvent_start"
                                targetRef="SubActivity_dept_head"/>
            <bpmn2:sequenceFlow id="SubFlow_dept_head_to_gateway" sourceRef="SubActivity_dept_head"
                                targetRef="SubGateway_approval"/>
            <bpmn2:sequenceFlow id="SubFlow_approved" name="已阅" sourceRef="SubGateway_approval"
                                targetRef="SubEvent_approved_end">
                <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${deptHeadResult == 'approved'}
                </bpmn2:conditionExpression>
            </bpmn2:sequenceFlow>
            <bpmn2:sequenceFlow id="SubFlow_doc_manager_to_deputy" sourceRef="SubActivity_doc_manager_reject"
                                targetRef="SubActivity_deputy_reject"/>
            <bpmn2:sequenceFlow id="SubFlow_deputy_to_gateway2" sourceRef="SubActivity_deputy_reject"
                                targetRef="SubGateway_need_leader"/>
            <bpmn2:sequenceFlow id="SubFlow_leader_needed" name="领导批示" sourceRef="SubGateway_need_leader"
                                targetRef="SubActivity_school_leader">
                <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${needLeaderApproval == true}
                </bpmn2:conditionExpression>
            </bpmn2:sequenceFlow>
            <bpmn2:sequenceFlow id="SubFlow_leader_to_final_doc_manager" sourceRef="SubActivity_school_leader"
                                targetRef="SubActivity_final_doc_manager"/>
            <bpmn2:sequenceFlow id="SubFlow_no_leader_needed" name="无需批示" sourceRef="SubGateway_need_leader"
                                targetRef="SubActivity_final_doc_manager">
                <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${needLeaderApproval == false}
                </bpmn2:conditionExpression>
            </bpmn2:sequenceFlow>
            <bpmn2:exclusiveGateway id="Gateway_05blr3l">
                <bpmn2:incoming>Flow_0ab3f9i</bpmn2:incoming>
                <bpmn2:outgoing>Flow_1xq7hr6</bpmn2:outgoing>
                <bpmn2:outgoing>Flow_0egzu84</bpmn2:outgoing>
            </bpmn2:exclusiveGateway>
            <bpmn2:sequenceFlow id="Flow_0ab3f9i" sourceRef="SubActivity_final_doc_manager"
                                targetRef="Gateway_05blr3l"/>
            <bpmn2:sequenceFlow id="Flow_1xq7hr6" name="部门处理" sourceRef="Gateway_05blr3l"
                                targetRef="SubActivity_dept_head">
                <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${needDeptProc== true}
                </bpmn2:conditionExpression>
            </bpmn2:sequenceFlow>
            <bpmn2:sequenceFlow id="Flow_0egzu84" name="部门无需处理" sourceRef="Gateway_05blr3l"
                                targetRef="SubEvent_approved_end">
                <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${needDeptProc== false}
                </bpmn2:conditionExpression>
            </bpmn2:sequenceFlow>
            <bpmn2:sequenceFlow id="SubFlow_rejected" name="不通过" sourceRef="SubGateway_approval"
                                targetRef="SubActivity_doc_manager_reject">
                <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${deptHeadResult == 'rejected'}
                </bpmn2:conditionExpression>
            </bpmn2:sequenceFlow>
        </bpmn2:subProcess>
        <bpmn2:userTask id="Activity_office_handler" name="校办负责人" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:extensionElements>
                <flowable:documentation>docManager</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment"/>
                <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_subprocess_to_office_handler</bpmn2:incoming>
            <bpmn2:outgoing>Flow_0d2a6ht</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:exclusiveGateway id="Gateway_need_feedback" name="是否需要反馈">
            <bpmn2:incoming>Flow_0d2a6ht</bpmn2:incoming>
            <bpmn2:outgoing>Flow_need_feedback</bpmn2:outgoing>
            <bpmn2:outgoing>Flow_no_feedback</bpmn2:outgoing>
        </bpmn2:exclusiveGateway>
        <bpmn2:userTask id="Activity_dept_liaison" name="部门联络员处理" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="CCDTO" flowable:assignee="${assignee.username}"
                        flowable:text="${assignee.deptName}">
            <bpmn2:extensionElements>
                <flowable:documentation>deptLiaison</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment"/>
                <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_need_feedback</bpmn2:incoming>
            <bpmn2:outgoing>Flow_liaison_to_final_doc_manager</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics
                    flowable:collection="${docFlowV2WfHandler.getDeptHeadByDeptCode(execution, dept, Activity_dept_subprocess, 'contact')}"
                                                    flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">
                    ${nrOfCompletedInstances &gt;= nrOfInstances}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        <bpmn2:userTask id="Activity_final_doc_manager" name="公文归档" flowable:formKey="key_1906235449754161153"
                        flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:extensionElements>
                <flowable:documentation>officeArchiving</flowable:documentation>
                <flowable:taskListener expression="${docFlowWfHandler.updateStatus(execution)}" event="assignment"/>
                <flowable:taskListener expression="${docFlowWfHandler.notify(task)}" event="assignment"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_liaison_to_final_doc_manager</bpmn2:incoming>
            <bpmn2:outgoing>Flow_final_to_end</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:endEvent id="Event_end">
            <bpmn2:incoming>Flow_no_feedback</bpmn2:incoming>
            <bpmn2:incoming>Flow_final_to_end</bpmn2:incoming>
        </bpmn2:endEvent>
        <bpmn2:sequenceFlow id="Flow_start_to_submit" sourceRef="Event_start" targetRef="Activity_submit"/>
        <bpmn2:sequenceFlow id="Flow_submit_to_deputy" sourceRef="Activity_submit"
                            targetRef="Activity_deputy_director"/>
        <bpmn2:sequenceFlow id="Flow_deputy_to_doc_manager" sourceRef="Activity_deputy_director"
                            targetRef="Activity_doc_manager"/>
        <bpmn2:sequenceFlow id="Flow_doc_manager_to_school_leader" sourceRef="Activity_doc_manager"
                            targetRef="Activity_school_leader"/>
        <bpmn2:sequenceFlow id="Flow_school_leader_to_office_pre" sourceRef="Activity_school_leader"
                            targetRef="Activity_office_handler_pre"/>
        <bpmn2:sequenceFlow id="Flow_office_pre_to_subprocess" sourceRef="Activity_office_handler_pre"
                            targetRef="Activity_dept_subprocess"/>
        <bpmn2:sequenceFlow id="Flow_need_feedback" name="需要反馈" sourceRef="Gateway_need_feedback"
                            targetRef="Activity_dept_liaison">
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${needFeedback == true}
            </bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
        <bpmn2:sequenceFlow id="Flow_no_feedback" name="不需要反馈" sourceRef="Gateway_need_feedback"
                            targetRef="Event_end">
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${needFeedback == false}
            </bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
        <bpmn2:sequenceFlow id="Flow_liaison_to_final_doc_manager" sourceRef="Activity_dept_liaison"
                            targetRef="Activity_final_doc_manager"/>
        <bpmn2:sequenceFlow id="Flow_final_to_end" sourceRef="Activity_final_doc_manager" targetRef="Event_end"/>
        <bpmn2:sequenceFlow id="Flow_subprocess_to_office_handler" sourceRef="Activity_dept_subprocess"
                            targetRef="Activity_office_handler"/>
        <bpmn2:sequenceFlow id="Flow_0d2a6ht" sourceRef="Activity_office_handler" targetRef="Gateway_need_feedback"/>
    </bpmn2:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="doc_flow_v2">
            <bpmndi:BPMNEdge id="Flow_0d2a6ht_di" bpmnElement="Flow_0d2a6ht">
                <di:waypoint x="1980" y="270"/>
                <di:waypoint x="2155" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_subprocess_to_office_handler_di" bpmnElement="Flow_subprocess_to_office_handler">
                <di:waypoint x="1930" y="400"/>
                <di:waypoint x="1930" y="310"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_final_to_end_di" bpmnElement="Flow_final_to_end">
                <di:waypoint x="2788" y="240"/>
                <di:waypoint x="2788" y="262"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_liaison_to_final_doc_manager_di" bpmnElement="Flow_liaison_to_final_doc_manager">
                <di:waypoint x="2630" y="200"/>
                <di:waypoint x="2738" y="200"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_no_feedback_di" bpmnElement="Flow_no_feedback">
                <di:waypoint x="2205" y="270"/>
                <di:waypoint x="2773" y="270"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="2455" y="246" width="55" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_need_feedback_di" bpmnElement="Flow_need_feedback">
                <di:waypoint x="2180" y="245"/>
                <di:waypoint x="2180" y="200"/>
                <di:waypoint x="2530" y="200"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="2173" y="213" width="45" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_office_pre_to_subprocess_di" bpmnElement="Flow_office_pre_to_subprocess">
                <di:waypoint x="880" y="260"/>
                <di:waypoint x="1200" y="260"/>
                <di:waypoint x="1200" y="400"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_school_leader_to_office_pre_di" bpmnElement="Flow_school_leader_to_office_pre">
                <di:waypoint x="760" y="270"/>
                <di:waypoint x="780" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_doc_manager_to_school_leader_di" bpmnElement="Flow_doc_manager_to_school_leader">
                <di:waypoint x="620" y="270"/>
                <di:waypoint x="660" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_deputy_to_doc_manager_di" bpmnElement="Flow_deputy_to_doc_manager">
                <di:waypoint x="480" y="270"/>
                <di:waypoint x="520" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_submit_to_deputy_di" bpmnElement="Flow_submit_to_deputy">
                <di:waypoint x="340" y="270"/>
                <di:waypoint x="380" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_start_to_submit_di" bpmnElement="Flow_start_to_submit">
                <di:waypoint x="208" y="270"/>
                <di:waypoint x="240" y="270"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="Event_start_di" bpmnElement="Event_start">
                <dc:Bounds x="172" y="252" width="36" height="36"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_submit_di" bpmnElement="Activity_submit">
                <dc:Bounds x="240" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_deputy_director_di" bpmnElement="Activity_deputy_director">
                <dc:Bounds x="380" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_doc_manager_di" bpmnElement="Activity_doc_manager">
                <dc:Bounds x="520" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_school_leader_di" bpmnElement="Activity_school_leader">
                <dc:Bounds x="660" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_office_handler_pre_di" bpmnElement="Activity_office_handler_pre">
                <dc:Bounds x="780" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_need_feedback_di" bpmnElement="Gateway_need_feedback" isMarkerVisible="true">
                <dc:Bounds x="2155" y="245" width="50" height="50"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="2147" y="305" width="67" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1c8sd5k_di" bpmnElement="Activity_dept_subprocess" isExpanded="true">
                <dc:Bounds x="910" y="400" width="1440" height="340"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SubFlow_rejected_di" bpmnElement="SubFlow_rejected">
                <di:waypoint x="1265" y="580"/>
                <di:waypoint x="1320" y="580"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1276" y="562" width="34" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0egzu84_di" bpmnElement="Flow_0egzu84">
                <di:waypoint x="2045" y="580"/>
                <di:waypoint x="2182" y="580"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="2099" y="563" width="67" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1xq7hr6_di" bpmnElement="Flow_1xq7hr6">
                <di:waypoint x="2020" y="605"/>
                <di:waypoint x="2020" y="660"/>
                <di:waypoint x="1140" y="660"/>
                <di:waypoint x="1140" y="620"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1558" y="642" width="45" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0ab3f9i_di" bpmnElement="Flow_0ab3f9i">
                <di:waypoint x="1960" y="580"/>
                <di:waypoint x="1995" y="580"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_no_leader_needed_di" bpmnElement="SubFlow_no_leader_needed">
                <di:waypoint x="1620" y="555"/>
                <di:waypoint x="1620" y="520"/>
                <di:waypoint x="1910" y="520"/>
                <di:waypoint x="1910" y="540"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1743" y="502" width="45" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_leader_to_final_doc_manager_di"
                             bpmnElement="SubFlow_leader_to_final_doc_manager">
                <di:waypoint x="1820" y="580"/>
                <di:waypoint x="1860" y="580"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_leader_needed_di" bpmnElement="SubFlow_leader_needed">
                <di:waypoint x="1645" y="580"/>
                <di:waypoint x="1720" y="580"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1660" y="562" width="45" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_deputy_to_gateway2_di" bpmnElement="SubFlow_deputy_to_gateway2">
                <di:waypoint x="1560" y="580"/>
                <di:waypoint x="1595" y="580"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_doc_manager_to_deputy_di" bpmnElement="SubFlow_doc_manager_to_deputy">
                <di:waypoint x="1420" y="580"/>
                <di:waypoint x="1460" y="580"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_approved_di" bpmnElement="SubFlow_approved">
                <di:waypoint x="1240" y="555"/>
                <di:waypoint x="1240" y="480"/>
                <di:waypoint x="2200" y="480"/>
                <di:waypoint x="2200" y="562"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1709" y="462" width="23" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_dept_head_to_gateway_di" bpmnElement="SubFlow_dept_head_to_gateway">
                <di:waypoint x="1190" y="580"/>
                <di:waypoint x="1215" y="580"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SubFlow_start_to_dept_head_di" bpmnElement="SubFlow_start_to_dept_head">
                <di:waypoint x="1068" y="580"/>
                <di:waypoint x="1090" y="580"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="SubEvent_start_di" bpmnElement="SubEvent_start">
                <dc:Bounds x="1032" y="562" width="36" height="36"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubActivity_dept_head_di" bpmnElement="SubActivity_dept_head">
                <dc:Bounds x="1090" y="540" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubGateway_approval_di" bpmnElement="SubGateway_approval" isMarkerVisible="true">
                <dc:Bounds x="1215" y="555" width="50" height="50"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1217.5" y="615" width="45" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubEvent_approved_end_di" bpmnElement="SubEvent_approved_end">
                <dc:Bounds x="2182" y="562" width="36" height="36"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubActivity_doc_manager_reject_di" bpmnElement="SubActivity_doc_manager_reject">
                <dc:Bounds x="1320" y="540" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubActivity_deputy_reject_di" bpmnElement="SubActivity_deputy_reject">
                <dc:Bounds x="1460" y="540" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubGateway_need_leader_di" bpmnElement="SubGateway_need_leader"
                              isMarkerVisible="true">
                <dc:Bounds x="1595" y="555" width="50" height="50"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1575" y="615" width="89" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubActivity_school_leader_di" bpmnElement="SubActivity_school_leader">
                <dc:Bounds x="1720" y="540" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SubActivity_final_doc_manager_di" bpmnElement="SubActivity_final_doc_manager">
                <dc:Bounds x="1860" y="540" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_05blr3l_di" bpmnElement="Gateway_05blr3l" isMarkerVisible="true">
                <dc:Bounds x="1995" y="555" width="50" height="50"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_office_handler_di" bpmnElement="Activity_office_handler">
                <dc:Bounds x="1880" y="230" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_dept_liaison_di" bpmnElement="Activity_dept_liaison">
                <dc:Bounds x="2530" y="160" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_final_doc_manager_di" bpmnElement="Activity_final_doc_manager">
                <dc:Bounds x="2738" y="160" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_end_di" bpmnElement="Event_end">
                <dc:Bounds x="2770" y="262" width="36" height="36"/>
            </bpmndi:BPMNShape>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn2:definitions>