-- 创建短信记录表
CREATE TABLE t_sms_record (
    id VARCHAR(64) PRIMARY KEY,
    phone_number VARCHAR(20) NOT NULL,
    content TEXT,
    type INT,
    template_id VARCHAR(64),
    template_params TEXT,
    status INT,
    send_time TIMESTAMP,
    receipt_time TIMESTAMP,
    receipt_code VARCHAR(10),
    receipt_desc VARCHAR(255),
    business_type VARCHAR(64),
    business_id VARCHAR(64),
    serial_number VARCHAR(64),
    task_id VARCHAR(64),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator VARCHAR(64),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updator VARCHAR(64)
);

-- 为列添加注释
COMMENT ON COLUMN t_sms_record.phone_number IS '手机号码';
COMMENT ON COLUMN t_sms_record.content IS '短信内容';
COMMENT ON COLUMN t_sms_record.type IS '短信类型（1：验证码，2：通知，3：营销）';
COMMENT ON COLUMN t_sms_record.template_id IS '短信模板ID';
COMMENT ON COLUMN t_sms_record.template_params IS '短信模板参数（JSON格式）';
COMMENT ON COLUMN t_sms_record.status IS '发送状态（0：失败，1：成功，2：处理中）';
COMMENT ON COLUMN t_sms_record.send_time IS '发送时间';
COMMENT ON COLUMN t_sms_record.receipt_time IS '回执时间';
COMMENT ON COLUMN t_sms_record.receipt_code IS '回执状态码';
COMMENT ON COLUMN t_sms_record.receipt_desc IS '回执描述';
COMMENT ON COLUMN t_sms_record.business_type IS '业务类型';
COMMENT ON COLUMN t_sms_record.business_id IS '业务ID';
COMMENT ON COLUMN t_sms_record.serial_number IS '流水号';
COMMENT ON COLUMN t_sms_record.task_id IS '任务ID';
COMMENT ON COLUMN t_sms_record.create_time IS '创建时间';
COMMENT ON COLUMN t_sms_record.creator IS '创建人';
COMMENT ON COLUMN t_sms_record.update_time IS '更新时间';
COMMENT ON COLUMN t_sms_record.updator IS '更新人';

-- 创建短信上行回复表
CREATE TABLE t_sms_reply (
    id VARCHAR(64) PRIMARY KEY,
    reply_id VARCHAR(64),
    phone_number VARCHAR(20),
    call_mdn VARCHAR(64),
    content TEXT,
    reply_time TIMESTAMP,
    is_confirmed BOOLEAN DEFAULT FALSE,
    confirm_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator VARCHAR(64),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updator VARCHAR(64)
);

-- 为列添加注释
COMMENT ON COLUMN t_sms_reply.reply_id IS '回复ID（平台返回的ID）';
COMMENT ON COLUMN t_sms_reply.phone_number IS '手机号码';
COMMENT ON COLUMN t_sms_reply.call_mdn IS '接入号';
COMMENT ON COLUMN t_sms_reply.content IS '回复内容';
COMMENT ON COLUMN t_sms_reply.reply_time IS '回复时间';
COMMENT ON COLUMN t_sms_reply.is_confirmed IS '是否已确认';
COMMENT ON COLUMN t_sms_reply.confirm_time IS '确认时间';
COMMENT ON COLUMN t_sms_reply.create_time IS '创建时间';
COMMENT ON COLUMN t_sms_reply.creator IS '创建人';
COMMENT ON COLUMN t_sms_reply.update_time IS '更新时间';
COMMENT ON COLUMN t_sms_reply.updator IS '更新人';

-- 创建索引
CREATE INDEX idx_sms_phone_number ON t_sms_record(phone_number);
CREATE INDEX idx_sms_serial_number ON t_sms_record(serial_number);
CREATE INDEX idx_sms_business_id ON t_sms_record(business_id);
CREATE INDEX idx_sms_business_type ON t_sms_record(business_type);
CREATE INDEX idx_sms_send_time ON t_sms_record(send_time);

CREATE INDEX idx_sms_reply_phone_number ON t_sms_reply(phone_number);
CREATE INDEX idx_sms_reply_reply_id ON t_sms_reply(reply_id);
CREATE INDEX idx_sms_reply_reply_time ON t_sms_reply(reply_time);