#set($editPath="'/${table.entityName}Edit'")
#set($router = '$router')

<template>
    <div class="app-container">
        <el-card header="${table.comment}查询">
            <el-form ref="searchForm" :inline="true" :model="searchForm">
                #foreach($column in $columns)
                    #if($column.comments != "")
                        <el-form-item label="${column.comments}:" label-width="100px">
                            <el-input v-model="searchForm.${column.attrname}" placeholder="${column.comments}"></el-input>
                        </el-form-item>
                    #end
                #end

                <el-form-item>
                    <el-button type="primary" @click="$refs.dynamicTable.refresh()">查询</el-button>
                    <el-button @click="reset()">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <el-card style="margin-top: 20px;">
            <div slot="header">
                ${table.comment}列表
                <div style="display: inline-block; float: right;">
                    <el-button @click="${router}.push({path: ${editPath} })">新增</el-button>
                    <el-button @click="$refs.${table.entityName}EditDialog.openAdd().then(() => $refs.dynamicTable.refresh())">新增</el-button>
                    <el-button @click="bacthDel()">批量删除</el-button>
                </div>
            </div>

            <dynamic-table ref="dynamicTable" :columns="columns" :loader="loader">
                <template v-slot:_name="{ row, column }">
                    <el-button type="text" @click="${router}.push({path: ${editPath}, query: {id: row.id} })">{{ row[column.property] }}</el-button>
                </template>
                <template v-slot:_opration="{ row, column }">
                    <el-button type="text" @click="${router}.push({path: ${editPath}, query: {id: row.id}})">编辑</el-button>
                    <el-button type="text" @click="$refs.${table.entityName}EditDialog.openDetail(row.id, 'edit').then(() => $refs.dynamicTable.refresh())">编辑</el-button>
                    <el-button type="text" @click="del([row.id])">删除</el-button>
                </template>
            </dynamic-table>
            <${table.entityName}EditDialog ref="${table.entityName}EditDialog" />
        </el-card>
    </div>
</template>

<script>
import DynamicTable from '@/components/DynamicTable'
import ${table.entityName}EditDialog from './${table.entityName}EditDialog'
import { find${table.entityName}Page, batchDelete${table.entityName} } from '@/api/api-${table.entityName}'

export default {
    name: '${table.entityName}List',
    components: {
        DynamicTable, ${table.entityName}EditDialog
    },
    data() {
        return {
            searchForm: {},
            columns: [
                {
                    type: 'selection',
                    width: '55'
                },
                #foreach($column in $table.fields)
                    #if($column.comment != "")
                        #if($foreach.index == 1)
                            {
                                slotName: '_name',
                                prop: '$column.propertyName',
                                label: '$column.comment'
                            },
                        #end
                        #if($foreach.index != 1)
                            {
                                prop: '$column.propertyName',
                                label: '$column.comment'
                            },
                        #end
                    #end
                #end
                {
                    label: '操作',
                    width: 110,
                    align: 'center',
                    prop: '_opration',
                    slotName: '_opration'
                }
            ]
        }
    },
    methods: {
        async loader(requestAdaptor) {
            // 获取查询表单和高级查询表单
            const { searchForm } = this
            // 设定查询表单
            requestAdaptor.setAdvancedSearchVlaues({ ...searchForm })
            // 构建参数发送请求
            const { data } = await find${table.entityName}Page(requestAdaptor.build())
            // 直接返回报文，分页table将会自动创建ResponseAdaptor
            return data
        },
        bacthDel() {
            const selections = this.$refs.dynamicTable.$refs.dynamicTable.selection
            const ids = []
            selections.forEach(item => {
                ids.push(item.id)
            })
            this.del(ids)
        },
        async del(ids) {
            const { data } = await batchDelete${table.entityName}(ids)
            if (data.code === 200) {
                this.$message.success('删除成功！')
                this.$refs.dynamicTable.refresh()
            } else {
                this.$message.error('系统异常, 删除失败!')
            }
        },
        reset() {
            this.$refs.searchForm.resetFields()
            this.searchForm = {}
            this.$refs.dynamicTable.refresh()
        }
    }
}
</script>
