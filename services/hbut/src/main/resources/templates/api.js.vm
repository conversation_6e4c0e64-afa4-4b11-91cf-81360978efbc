/*
 * @Author: ${author}
 * @LastEditors: ${author}
 * @Date: ${date}
 * @LastEditTime: ${date}
 * @Description: $!{table.comment}
 */
import request from '@/utils/request'

/**
 * 获取列表
 * @param {*} param  参数
 */
export function find${table.entityName}Page(param) {
    return request({
        url: '/${controllerMappingHyphen}/page',
        method: 'post',
        data: param
    })
}
/**
 * 获取不分页列表
 * @param {*} param  参数
 */
export function find${table.entityName}List(param) {
    return request({
        url: '/${controllerMappingHyphen}/list',
        method: 'post',
        data: param
    })
}

/**
 * 获取详情
 * @param {*} param  参数
 */
export function get${table.entityName}Detail(id) {
    return request({
        url: `/${controllerMappingHyphen}/detail/` + id,
        method: 'get'
    })
}

/**
 * 保存
 * @param {*} param  参数
 */
export function save${table.entityName}(formData) {
    // 存在id就更新
    if (formData.id) return update${table.entityName}(formData)
    return request({
        url: '/${controllerMappingHyphen}',
        method: 'post',
        data: formData
    })
}
/**
 * 更新
 * @param {*} param  参数
 */
export function update${table.entityName}(formData) {
    return request({
        url: '/${controllerMappingHyphen}',
        method: 'put',
        data: formData
    })
}

/**
 * 删除
 * @param {*} param  参数
 */
export function batchDelete${table.entityName}(ids) {
    if (!Array.isArray(ids)) {
        ids = [ids]
    }
    ids = ids.map(str => 'idlist='+str).join('&')
    return request({
        url: '/${controllerMappingHyphen}/delete?'+ids,
        method: 'delete'
    })
}
