import org.ahead4.cdes.dto.*
import org.ahead4.cdes.entity.*
import org.ahead4.dbms.service.ApiRealese

import java.math.RoundingMode
import org.springframework.context.ApplicationContext
import org.ahead4.common.tools.SpringContextHolder
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.alibaba.fastjson.JSONPath
import org.ahead4.common.utils.*
import org.ahead4.permission.utils.AreaCodeHelp


def springContext = SpringContextHolder.getApplicationContext();
def apiReleaseService = SpringContextHolder.getBean(ApiRealese.class);




def __exec() {
    ScriptContext<BigDecimal> context = new ScriptContext<>("");
    context.raw = new JSONArray()
    // TODO 计算代码放这里
    def result = BigDecimal.ZERO
    Map<String, List<JSONObject>> mapping = new HashMap<>()
    // 先按照填报人分组数据
    for (i in 0..<context.raw.size()) {
        def obj = context.raw.getJSONObject(i)
        String owner = obj.getString("owner")
        List<JSONObject> ownerResultItems = mapping.get(owner)
        if (ownerResultItems = null) {
            ownerResultItems = new ArrayList<JSONObject>()
            mapping.put(owner, ownerResultItems)
        }
        ownerResultItems.add(obj)
    }
    // 遍历分组数据，
    for (group in mapping) {
        def groupResult = BigDecimal.ZERO
        for (item in group.value) {
            def val = item.getBigDecimal("value")
            if (val != null) {
                groupResult += (val * 0.2)
            }
        }
        result += groupResult
    }
    if (mapping.size() == 0) return BigDecimal.ZERO
    return result / mapping.size()
}
context.result = __exec()


print(context.result)