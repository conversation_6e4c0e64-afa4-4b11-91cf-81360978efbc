<template>
    <el-dialog
            title="${table.comment}列表"
            custom-class="form-dialog"
            :visible.sync="dialogVisible"
            width="70%"
            :before-close="handleClose"
    >
        <div style="margin-bottom:12px">
            <el-form ref="searchForm" :inline="true" :model="searchForm">
                #foreach($column in $table.fields)
                    #if($column.comment != "")
                        <el-form-item label="${column.comment}" label-width="100px">
                            <el-input v-model="searchForm.${column.propertyName}" placeholder="${column.comment}"></el-input>
                        </el-form-item>
                    #end
                #end

                <el-form-item>
                    <el-button type="primary" @click="$refs.dynamicTable.refresh()">查询</el-button>
                    <el-button @click="reset()">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <dynamic-table ref="dynamicTable" :columns="columns" :loader="loader" />

        <template v-slot:footer>
            <el-row style="margin-top:5px;margin-right:10px">
                <el-col style="text-align: center;">
                    <el-button type="primary" @click="certain">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </el-col>
            </el-row>
        </template>
    </el-dialog>
</template>

<script>
    import DynamicTable from '@/components/DynamicTable'
    import { findSysDeptTestList } from '@/api/api-${table.entityName}'

    export default {
        name: '${table.entityName}ListDialog}',
        components: {
            DynamicTable
        },
        data() {
            return {
                resolve: null,
                reject: null,
                dialogVisible: false,
                searchForm: {},
                columns: [
                    {
                        type: 'selection',
                        width: '55'
                    },
                    #foreach($column in $table.fields)
                        #if($column.comment != "")
                            {
                                prop: '$column.propertyName',
                                label: '$column.comment'
                            } #if($foreach.hasNext) , #end
                        #end
                    #end
                ]
            }
        },
        methods: {
            handleClose(done) {
                this.reset()
                // 关闭
                done()
            },
            clearSelection() {
                this.$refs.dynamicTable.$refs.dynamicTable.clearSelection()
            },
            show() {
                this.formData = { }
                this.dialogVisible = true
                return new Promise((resolve, reject) => {
                    this.resolve = resolve
                    this.reject = reject
                })
            },
            async loader(requestAdaptor) {
                // 获取查询表单和高级查询表单
                const { searchForm } = this
                // 设定查询表单
                requestAdaptor.setAdvancedSearchVlaues({ ...searchForm })
                // 构建参数发送请求
                const { data } = await findSysDeptTestList(requestAdaptor.build())
                // 直接返回报文，分页table将会自动创建ResponseAdaptor
                return data
            },
            reset() {
                this.clearSelection()
                this.$refs.searchForm.resetFields()
                        this.searchForm = {}
                this.$refs.dynamicTable.refresh()
            },
            cancel() {
                this.reject()
                this.dialogVisible = false
            },
            certain() {
                this.resolve(this.$refs.dynamicTable.$refs.dynamicTable.selection)
                this.dialogVisible = false
            }
        }
    }
</script>
