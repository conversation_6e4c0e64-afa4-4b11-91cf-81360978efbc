#set($editPath="/${table.entityName}Edit")
#set($router = '$router')

<!--
 * @Description: $!{table.comment}
 * @Author: ${author}
 * @Date: ${date}
 * @LastEditors:
 * @LastEditTime: ${date}
-->
<template>
  <div>
    <el-card shadow="never">
      <el-form :inline="true" :model="formInline" class="form-inline">
  #foreach($column in $table.fields)
      #if($column.comment != "")
        <el-form-item label="${column.comment}:" label-width="100px">
          <el-input v-model="formInline.${column.propertyName}$$LIKE" placeholder="${column.comment}" clearable @keyup.enter.native="table.refresh();"></el-input>
        </el-form-item>
      #end
  #end
        <el-form-item>
          <el-button-group class="ml-4">
            <el-button type="primary" @click="table.refresh();">查询</el-button>
            <!-- <el-button type="danger">批量删除</el-button> -->
            <el-button @click="router.push('/FormLibEdit')" >新增</el-button>
          </el-button-group>
        </el-form-item>
      </el-form>
      <el-divider />
      <DynamicTable
          ref="table"
          :columns="columns"
          tableKey="FormLibList"

          pageable
          :loader="loader"
      >
        <template v-slot:_opration="{ row, column }">
          <el-link @click="handlePreview(row)" type="primary">预览</el-link>
          <el-divider direction="vertical" />
          <el-link @click="handleEdit(row)" type="primary">编辑</el-link>
          <el-divider direction="vertical" />
          <el-link @click="handleDelete(row)" type="primary">删除</el-link>
        </template>
      </DynamicTable>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, reactive } from "vue";
  import { find${table.entityName}Page, batchDelete${table.entityName} } from "@/api/${table.entityName}-api.js";
  import { ElMessage, ElMessageBox } from "element-plus";
  import { useRouter } from "vue-router";
  import DynamicTable from "@/components/DynamicTable/DynamicTablePaged.vue";
  import RequestAdaptor from "@/components/DynamicTable/adaptor/RequestAdaptor";
  const table = ref();
  const router = useRouter();

  const formInline = reactive({
#foreach($column in $table.fields)
#if($column.comment != "")
    // $column.comment
    $column.propertyName: undefined#if($foreach.hasNext), #end
#end
#end
  })
  const columns = [
#foreach($column in $table.fields)
  #if($column.comment != "")
    #if($foreach.index == 1)
    {
      slotName: '_name',
      prop: '$column.propertyName',
      label: '$column.comment'
    }#end
    #if($foreach.index != 1)
    {
      prop: '$column.propertyName',
      label: '$column.comment'
    }#end,
  #end
#end
    { prop: "_opration", label: "操作", align: "center", slotName: "_opration" },
  ];
  const loader = async (requestAdaptor: RequestAdaptor) => {
    try {
      requestAdaptor.setAdvancedSearchVlaues(formInline)
      const { data } = await find${table.entityName}Page(requestAdaptor.build());
      return data
    } catch (error) {
      console.error(error)
    }
  };

  const handleEdit = (row: any) => {
    router.push("/${editPath}?id=" + row.id);
  };
  const handlePreview = (row: any) => {
    router.push("/${editPath}?id=" + row.id);
  };

  const handleDelete = async (row: any) => {
    await ElMessageBox.confirm(
        '确定要删除吗?',
        '警告',
        {
          type: 'error',
        }
    )
    try {
      await batchDelete${table.entityName}(row.id);
      ElMessage.success("Deleted successfully");
      table.value.refresh();
    } catch (error) {
      ElMessage.error("删除失败！");
    }
  };

  // onMounted(() => {});
</script>

<style>
  .pagination {
    margin: 20px 0;
  }
</style>
