<!--
 * @Description: $!{table.comment}
 * @Author: ${author}
 * @Date: ${date}
 * @LastEditors:
 * @LastEditTime: ${date}

-->
<template>
  <div style="position: relative;">
    <div class="backBtn">
      <el-button plain class="el-back"  @click="router.go(-1)">返回</el-button>
      <el-button plain class="el-back" v-if="!(type&&type==='detail')" @click="editInfo">提交</el-button>
    </div>
    <el-card shadow="never" class="formCard" >
      <template #header>
        <div class="header">
          <div class="title">$table.comment</div>
        </div>
      </template>
      <el-form :model="baseForm" :disabled="type&&type==='detail'" ref="bForm" class="base-form" label-width="100px">
        <el-row>

#foreach($column in $table.fields)
#if($column.comment != "")
          <!-- $column.comment -->
          <el-col :span="8">
            <el-form-item label="$column.comment：" prop="$column.propertyName" :rule="[{ required: true, message: '请输入${column.comment}', trigger: 'blur' }]">
    #if($column.columnType.getType() == 'String')
              <el-input v-model="baseForm.$column.propertyName"
                        placeholder="请输入$column.comment"
                        clearable
              />
    #end
    #if($column.columnType.getType() == 'Date')
              <el-date-picker
                  v-model="baseForm.$column.propertyName"
                  type="datetime"
                  placeholder="请选择$column.comment"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  clearable
              />
    #end
    #if($column.columnType.getType() == 'int' || $column.columnType.getType() == 'float' || $column.columnType.getType() == 'double' || $column.columnType.getType() == 'Integer' || $column.columnType.getType() == 'Integer' || $column.columnType.getType() == 'Integer' || $column.columnType.getType() == 'Float' || $column.columnType.getType() == 'Double')
              <el-input-number
                  v-model="baseForm.$column.propertyName"
                  type="datetime"
                  placeholder="请选择$column.comment"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  clearable
              />
    #end
            </el-form-item>
          </el-col>

#end
#end
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>
<script setup lang="ts">
  import { reactive, ref, onMounted, watch } from "vue";
  import { useRoute, useRouter } from "vue-router";
  import GUID from '@/utils/guid'
  import { get${table.entityName}Detail, save${table.entityName} } from '@/api/${table.entityName}-api'

  import { ElMessage, ElMessageBox } from "element-plus";

  const route = useRoute();
  const router = useRouter();
  const { id, type } = <any>route.query
  const baseForm = ref<any>({});
  const formRef = ref()
  const loading = ref(false)


  async function get${table.entityName}() {
    try {
      if (id) {
        const base = await get${table.entityName}Detail(id)
        baseForm.value = base
      } else {
        formRef.value.resetFields()
      }
    } catch (error) {
      console.error(error)
    }
  }

  async function editInfo() {
    try {
      if(type && type === 'detail') {
        return
      }
      await save${table.entityName}(baseForm.value)
      ElMessage.success('更新成功')
    } catch (error) {
      console.error(error)
    }
  }


  onMounted(() => {
    get${table.entityName}()
  })

</script>
<style lang="scss" scoped>
</style>
