#set($refs= '$refs')

<template>
    <div class="app-container">
        <el-card>
            <template v-slot:header>
                <el-row>
                    <el-col :span="12">{{ type === 'add' ? '新增' : '编辑' }}${table.comment}</el-col>
                    <el-col :span="12" style="text-align: right;">
                        <el-button @click="$router.go(-1)">返回</el-button>
                        <el-button type="primary" @click="submit">保存</el-button>
                    </el-col>
                </el-row>
            </template>

            <dynamic-form ref="form" v-model="formData" :fields="fields" style="width:50%;" />
        </el-card>
    </div>
</template>

<script>
import { get${table.entityName}Detail, save${table.entityName} } from '@/api/${table.entityName}-api'
import DynamicForm from '@/components/DynamicForm'

export default {
    name: '${table.entityName}Edit',
    components: {
        DynamicForm
    },
    data() {
        return {
            type: 'add',
            formData: {},
            fields: {
                #foreach($column in $table.fields)
                    #if($column.comment != "")
                        $column.propertyName: {
                            label: '$column.comment',
                            key: '$column.propertyName',
                            span: 24,
                            component: 'ElInput',
                            rules: [
                                { required: true, message: '请输入${column.comment}', trigger: 'blur' }
                            #if($column.columnType.getType() == 'string')
                                #if($column.empty == 'NO')
                                    ,
                                #end
                                { max: ${column.charLength}, message: '最大长度在${column.charLength}个字符', trigger: 'blur' }
                            #end
                            ]
                        } #if($foreach.hasNext) , #end
                    #end
                #end

            }
        }
    },
    mounted() {
        this.loadDateil()
    },
    beforeDestroy() {
    },
    methods: {
        async loadDateil() {
            const { id } = this.$route.query
            if (!id && id !== 0) {
                this.type = 'edit'
                return
            }
            const { data } = await get${table.entityName}Detail(id)
            this.formData = data
        },
        submit() {
            this.${refs}.form.validate(validate => {
                if (!validate) return
                save${table.entityName}(this.formData).then(res => {
                    const { data } = res
                    if (data.code === 200) {
                        this.$message.success('保存成功！')
                        this.loadDateil()
                        this.$router.go(-1)
                    } else {
                        this.$message.error('系统异常')
                    }
                })
            })
        }
    }
}
</script>

<style lang="scss" scoped>
    .sql-container{
        height: 400px;
    }
</style>
