package ${package.Controller};

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;

#if(${restControllerStyle})
import org.springframework.web.bind.annotation.RestController;
#else
import org.springframework.stereotype.Controller;
#end
#if(${superControllerClassPackage})
import ${superControllerClassPackage};
#end

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.ahead4.web.presentate.HttpMsg;
import org.ahead4.common.dto.PageParam;
import org.ahead4.security.utils.AccessHolder;
import ${package.Entity}.${entity};
import ${package.Service}.${table.serviceName};

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.ahead4.common.dto.Page;
import static org.ahead4.jdbc.utils.MybatisPlusUtils.*;

/**
 * <p>
 * $!{table.comment} 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${restControllerStyle})
@RestController
#else
@Controller
#end
@RequestMapping("#if(${package.ModuleName})/${package.ModuleName}#end/#if(${controllerMappingHyphenStyle})${controllerMappingHyphen}#else${table.entityPath}#end")
@Api(value = "$!{table.comment}", description = "$!{table.comment}控制器")
#if(${kotlin})
class ${table.controllerName}#if(${superControllerClass}) : ${superControllerClass}()#end

#else
#if(${superControllerClass})
public class ${table.controllerName} extends ${superControllerClass} {
#else
public class ${table.controllerName} {
#end
    @Autowired
    ${table.serviceName} service;
    /**
     * 详情
     * @return
     */
    @GetMapping("detail/{id}")
    @ApiOperation(value = "详情")
    public ${entity} get(@PathVariable String id) {
        return service.getById(id);
    }

    /**
     * 分页
     * @return
     */
    @PostMapping("page")
    @ApiOperation(value = "分页")
    public Page<${entity}> page(@RequestBody PageParam param) {
        return plusPage2Page(service.page(param2PlusPage(param), param2PlusWrapper(param)));
    }

    /**
     * 全部
     * @return
     */
    @PostMapping("list")
    @ApiOperation(value = "全部")
    public List<${entity}> list(@RequestBody PageParam param) {
        return service.list(param2PlusWrapper(param));
    }

    /**
     * 新增
     * @return
     */
    @PostMapping
    @ApiOperation(value = "新增")
    public HttpMsg save(@RequestBody ${entity} entity) {
        entity.setCreateTime(new Date());
        entity.setCreator(AccessHolder.username());
        service.save(entity);
        return HttpMsg.ok().data(entity);
    }
    /**
     * 修改
     * @return
     */
    @PutMapping
    @ApiOperation(value = "修改")
    public HttpMsg update(@RequestBody ${entity} entity) {
        QueryWrapper<${entity}> wrapper = new QueryWrapper<>();
        wrapper.eq(true, "id", entity.getId());
        entity.setUpdateTime(new Date());
        entity.setUpdator(AccessHolder.username());
        service.update(entity, wrapper);
        return HttpMsg.ok().data(entity);
    }
    /**
     * 删除
     * @return
     */
    @DeleteMapping({"{id}", ""})
    @ApiOperation(value = "删除")
    public HttpMsg delete(@PathVariable(required = false) String id, @RequestParam(required = false) List<String> idlist) {
        boolean r = false;
        if (!CollectionUtils.isEmpty(idlist)) {
            r = service.removeByIds(idlist);
        } else if (!StringUtils.isEmpty(id)) {
            r = service.removeById(id);
        }
        return r ? HttpMsg.ok("删除成功！") : HttpMsg.error("删除失败！");
    }
}

#end