#set($refs= '$refs')

<template>
    <el-dialog
            custom-class="form-dialog"
            :visible.sync="dialogVisible"
            width="30%"
            :before-close="handleClose"
            :title="type === 'add' ? '新增' : '编辑'"
    >
        <div slot="title" class="dialog-header">{{ type === 'add' ? '新增' : '编辑' }}${table.comment}</div>
        <dynamic-form ref="form" v-model="formData" :fields="fields" :disabled="type === 'detail'" />
        <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false;reject()">取 消</el-button>
      <el-button v-if="type !== 'detail'" type="primary" @click="submitForm('accountForm')">确 定</el-button>
    </span>
    </el-dialog>
</template>

<script>
import { get${table.entityName}Detail, save${table.entityName} } from '@/api/api-${table.entityName}'
import DynamicForm from '@/components/DynamicForm'

export default {
    name: '${table.entityName}EditDialog',
    components: {
        DynamicForm
    },
    data() {
        return {
            type: 'detail',
            resolve: null,
            reject: null,
            dialogVisible: false,
            formData: {},
            fields: {
                #foreach($column in $table.fields)
                    #if($column.comment != "")
                            $column.propertyName: {
                        label: '$column.comment',
                        key: '$column.propertyName',
                        span: 24,
                        component: 'ElInput',
                        rules: [
                          { required: true, message: '请输入${column.comment}', trigger: 'blur' }
                            #if($column.columnType.getType() == 'string')
                                #if($column.empty == 'NO')
                                  ,
                                #end
                              { max: ${column.charLength}, message: '最大长度在${column.charLength}个字符', trigger: 'blur' }
                            #end
                        ]
                      } #if($foreach.hasNext) , #end
                    #end
                #end
                // dbType: {
                //     label: '数据库类型',
                //     key: 'dbType',
                //     span: 24,
                //     component: 'BaseElSelect',
                //     props: {
                //         options: [{ label: oracle, value: oracle }, { label: mysql, value: mysql }]
                //     }
                // },
            }
        }
    },
    mounted() {
    },
    beforeDestroy() {
    },
    methods: {
        // 关闭前检查
        handleClose(done) {
            this.reset()
            // 关闭
            done()
        },
        reset() {
            this.formData = {}
            this.type = 'detail'
            this.resolve = null
            this.reject = null
        },
        openDetail(primaryKey, type = 'detail') {
            this.primaryKey = primaryKey
            this.type = type
            this.loadDateil()
            this.dialogVisible = true
            return new Promise((resolve, reject) => {
                this.resolve = resolve
                this.reject = reject
            })
        },
        openAdd() {
            this.type = 'add'
            this.formData = { }
            this.dialogVisible = true
            return new Promise((resolve, reject) => {
                this.resolve = resolve
                this.reject = reject
            })
        },
        // 加载详情
        async loadDateil() {
            if (!this.primaryKey && this.primaryKey !== 0) return
            const { data } = await get${table.entityName}Detail(this.primaryKey)
            this.formData = data
        },
        // 提交表单
        submitForm() {
            // 校验
            this.${refs}.form.validate(validate => {
                if (!validate) return
                save${table.entityName} (this.formData).then(res => {
                    const { data } = res
                    if (data.code === 200) {
                        this.$message.success('保存成功！')
                        this.loadDateil()
                        this.dialogVisible = false
                        this.resolve()
                        this.reset()
                    } else {
                        this.$message.error('系统异常')
                    }
                })
            })
        }
    }
}
</script>

<style scoped>
    .dialog-header {
        font-size:14px;
        font-weight:600;
        color:#fff;
        line-height:20px;
        margin-left: 4px;
    }
</style>