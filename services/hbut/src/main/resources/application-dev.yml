spring:
  datasource:
    dynamic:
      datasource:
        "[主数据库]":
          url: jdbc:postgresql://${DATABASE_HOST:127.0.0.1}:${DATABASE_PORT:25445}/${DATABASE:hbut}?tcpKeepAlive=true&reWriteBatchedInserts=true&ApplicationName=${spring.application.name}&stringtype=unspecified
          username: ${DATABASE_USERNAME:hbut}
          password: ${DATABASE_PASSWORD:nlwUBWY8}

  redis:
#    password: ${REDIS_PASSWORD:ahead4@redis}
    database: ${REDIS_DB:1}
    host: ${REDIS_HOST:127.0.0.1}
    port: ${REDIS_PORT:6379}


sms:
  # 代理号码池
  enable: false
  proxy-num-pool:



# 开启debug日志

logging:
  file:
    path: ./log
    name: micro-default.log
  level:
    org.springframework.web: debug
    org.springframework.security: debug
    org.springframework.security.web: debug
    org.springframework.security.authentication: debug
    org.springframework.security.config: debug
    com.baomidou.mybatisplus: warn
    #org:flowable: debug
    org.flowable.common.engine.impl.persistence: debug
    org.flowable.job.service.impl.persistence: debug
    org.flowable.job.service.impl.persistence.entity.JobEntityImpl.selectJobsToExecute: debug
    org.flowable.job.service.impl.persistence.entity.TimerJobEntityImpl.selectTimerJobsToExecute: debug
    org.flowable.job.service.impl.persistence.entity.ExternalWorkerJobEntityImpl.selectExpiredExternalWorkerJobs: debug
    org.flowable.job.service.impl.persistence.entity.TimerJobEntityImpl.selectExpiredTimerJobs: debug
    org.flowable.job.service.impl.persistence.entity.JobEntityImpl.selectExpiredJobs: debug
    org.flowable.common.engine.impl.persistence.TimerJobEntityImpl.selectTimerJobsToExecute: debug
    org.flowable.common.engine.impl.persistence.entity.PropertyEntityImpl.selectProperty: debug

mybatis-plus:
  configuration:
    #    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


exclude:
  paths:
    - /csrf
    - /sys-auth/token/**
    - /suppliers/register
    - /security/**
    - /api/**
    - /error
    - /swagger-ui/**
    - /swagger-ui.html
    - /v2/api-docs
    - /swagger/**
    - /swagger-resources/**
    - /webjars/**
    - /metrics/**
    - /cas-auth/**

kkview:
  # 容器名称 kkview:8012/kkview
  host: https://hubt.54w.com/kkview/
