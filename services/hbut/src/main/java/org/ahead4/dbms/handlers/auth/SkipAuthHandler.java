package org.ahead4.dbms.handlers.auth;

import lombok.extern.slf4j.Slf4j;
import org.ahead4.common.utils.IdentitiesUtils;
import org.ahead4.dbms.dto.ApiContext;
import org.ahead4.dbms.entity.SqlTemplate;
import org.ahead4.dbms.handlers.auth.AuthHandler;
import org.ahead4.security.dto.IUserDetail;
import org.ahead4.security.utils.AccessHolder;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

@Slf4j
@Component("AuthHandler::skip-auth")
public class SkipAuthHandler implements AuthHandler {

    @Override
    public IUserDetail handler(HttpServletRequest request, HttpServletResponse response, SqlTemplate dto, ApiContext context) {
        AccessHolder.token(IdentitiesUtils.uuid2());
        Date curDate = new Date();
        String curStr = DateFormatUtils.format(curDate, "yyyyMMddHHmmss");
        return IUserDetail
                .builder()
                .username("skipauth-"+ curStr)
                .displayname("跳过授权"+curStr)
                .build();
    }

    @Override
    public Object result(Object r, SqlTemplate dto, HttpServletRequest request, HttpServletResponse response, ApiContext context) {
        AccessHolder.logout(true);
        return AuthHandler.super.result(r, dto, request, response, context);
    }
}