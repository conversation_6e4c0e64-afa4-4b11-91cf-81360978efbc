package org.ahead4.dbms.handlers.auth;

import lombok.extern.slf4j.Slf4j;
import org.ahead4.dbms.dto.ApiContext;
import org.ahead4.dbms.entity.SqlTemplate;
import org.ahead4.dbms.handlers.auth.AuthHandler;
import org.ahead4.security.dto.IUserDetail;
import org.ahead4.security.utils.AccessHolder;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@Component("AuthHandler::sys-auth")
public class SysAuthHandler implements AuthHandler {
    @Override
    public IUserDetail handler(HttpServletRequest request, HttpServletResponse response, SqlTemplate dto, ApiContext context) {
        return AccessHolder.user();
    }
}