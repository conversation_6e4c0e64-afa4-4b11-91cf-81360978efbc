package org.ahead4.email.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.ahead4.cdes.utils.ValidatorUtils;
import org.ahead4.email.entity.EduEmailDto;
import org.ahead4.redis.utils.RedisUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.ahead4.web.presentate.HttpMsg;
import org.ahead4.common.dto.PageParam;
import org.ahead4.security.utils.AccessHolder;
import org.ahead4.email.entity.EduEmail;
import org.ahead4.email.service.EduEmailService;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.ahead4.common.dto.Page;

import static org.ahead4.email.service.impl.EduEmailServiceImpl.EDU_EMAIL_DATA_SYNC_TASK_LOCK;
import static org.ahead4.jdbc.utils.MybatisPlusUtils.*;

/**
 * <p>
 * 教育邮箱用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@RestController
@RequestMapping("/edu-email")
@Api(value = "教育邮箱用户表", description = "教育邮箱用户表控制器")
public class EduEmailController {
    @Autowired
    EduEmailService service;
    @Autowired
    EduEmailService eduEmailService;
    /**
     * 详情
     * @return
     */
    @GetMapping("detail/{id}")
    @ApiOperation(value = "详情")
    public EduEmail get(@PathVariable String id) {
        return service.getWithUpdateStats(id);
    }

    /**
     * 分页
     * @return
     */
    @PostMapping("page")
    @ApiOperation(value = "分页")
    public Page<EduEmail> page(@RequestBody PageParam param) {
        if (param.getSortMap().isEmpty()) {
            param.getSortMap().put("update_time", "DESC");
        }
        return plusPage2Page(service.page(param2PlusPage(param), param2PlusWrapper(param)));
    }

//    /**
//     * 新增
//     * @return
//     */
//    @PostMapping
//    @ApiOperation(value = "新增")
//    public HttpMsg save(@RequestBody EduEmail entity) {
//        entity.setCreateTime(new Date());
//        entity.setCreator(AccessHolder.username());
//        service.save(entity);
//        return HttpMsg.ok().data(entity);
//    }
//    /**
//     * 修改
//     * @return
//     */
//    @PutMapping
//    @ApiOperation(value = "修改")
//    public HttpMsg update(@RequestBody EduEmail entity) {
//        QueryWrapper<EduEmail> wrapper = new QueryWrapper<>();
//        wrapper.eq(true, "id", entity.getId());
//        entity.setUpdateTime(new Date());
//        entity.setUpdator(AccessHolder.username());
//        service.update(entity, wrapper);
//        return HttpMsg.ok().data(entity);
//    }
    /**
     * 修改
     * @return
     */
    @PutMapping("reset-password/{id}")
    @ApiOperation(value = "重置密码")
    public HttpMsg update(@PathVariable String id, String password) {

        EduEmail email = service.getById(id);
        if (email == null) {
            return HttpMsg.error("操作失败！用户不存在");
        }
        String msg = eduEmailService.changePassword(email.getId(), email.getEmailAddr(), password);
        return StringUtils.isEmpty(msg) ? HttpMsg.ok() : HttpMsg.error(msg);

    }


    /**
     * 同步
     * @return
     */
    @GetMapping("sync")
    @ApiOperation(value = "手动同步")
    public HttpMsg sync() {
        service.startSyncData();
        return HttpMsg.ok().put("running", RedisUtil.hasKey(EDU_EMAIL_DATA_SYNC_TASK_LOCK));
    }
    /**
     * 同步
     * @return
     */
    @GetMapping("sync-status")
    @ApiOperation(value = "获取同步状态")
    public HttpMsg syncStatus() {
        return HttpMsg.ok().put("running", RedisUtil.hasKey(EDU_EMAIL_DATA_SYNC_TASK_LOCK));
    }
    /**
     * 删除
     * @return
     */
    @DeleteMapping({"{id}", ""})
    @ApiOperation(value = "删除")
    public HttpMsg delete(@PathVariable(required = false) String id) {
        try {
            eduEmailService.deleteUserEmailAccount(id);
        } catch (Exception e) {
            return HttpMsg.error(e.getMessage());
        }
        return HttpMsg.ok("删除成功！");
    }
    /**
     * 删除
     * @return
     */
    @PutMapping({"status/{id}/{status}"})
    @ApiOperation(value = "修改邮箱账号启用状态")
    public HttpMsg status(@PathVariable String id, @PathVariable String status) {
        EduEmail email = service.getById(id);
        if(email == null) {
            throw new RuntimeException(id + ": EMAIL不存在");
        }
        int statusInt = StringUtils.equals(status, "1") ? 1 : 0;
        email.setEnable(String.valueOf(statusInt));
        boolean result = eduEmailService.updateUserStatus(email.getEmailAddr(), statusInt);
        if (result) {
            eduEmailService.saveOrUpdate(email);
        } else {
            return HttpMsg.error("更新失败");
        }

        return HttpMsg.ok("操作成功！");
    }
    @ApiOperation(value = "无认证注册邮箱")
    @PostMapping("signup")
    public HttpMsg signup(EduEmailDto param) {
        // 目前不支持其他域名
        param.setDomain("hbut.edu.cn");
        String domain = StringUtils.defaultString(param.getDomain(), "hbut.edu.cn");
        EduEmail emailUser = new EduEmail();
        // 检查邮箱是否被注册
        if (eduEmailService.checkEmailExist(emailUser.getEmailAddr())) {
            // 处理校验错误
            return HttpMsg.error(HttpStatus.BAD_REQUEST, "邮箱地址已经被注册！");
        }
        emailUser.setId(param.getId());
        emailUser.setName(param.getName());
        emailUser.setPhone(param.getPhone());
        emailUser.setEnable("1");
        emailUser.setEmailAddr(param.getId() +"@"+ domain);
        emailUser.setIdCardNo(param.getIdCardNo());
        // 注册
//        eduEmailService.signup(emailUser, param.getPassword());
        return HttpMsg.ok();
    }
}

