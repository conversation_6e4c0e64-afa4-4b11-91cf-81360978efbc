package org.ahead4.email.provider.tencent.internal;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
public class LoginAuditTask {
    private final int monthCount;

    private String emailSecret;

    private String logSecret;

    private String corpId;

    private final String DOMAIN = "mail.hbut.edu.cn";

    public LoginAuditTask(int monthCount, String coprId, String emailSecret, String logSecret) {
        this.monthCount = monthCount;
        this.emailSecret = emailSecret;
        this.logSecret = logSecret;
        this.corpId = coprId;
    }

    public void run() {
        EmailClient client = null;
        try {
            client = new EmailClient(this.corpId, this.emailSecret, this.logSecret);
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }

        DepartmentService departmentService = client.department();
        UserService userService = client.user();
        MailLogService mailLogService = client.Log();
        try {
            List<DepartmentDTO> departments = departmentService.list(1); // 顶级部门

            for (DepartmentDTO dept : departments) {
                List<UserDTO> users = userService.listUsersByDepartment(dept.getId(), true);

                for (UserDTO user : users) {
                    LocalDate end = LocalDate.now();
                    LocalDate start = end.minusMonths(monthCount);
                    DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                    // 查询登录日志
                    MailLogService.LoginLogQueryRequest loginReq = new MailLogService.LoginLogQueryRequest();
                    loginReq.setUserid(user.getUserid());
                    loginReq.setBegin_date(start.format(fmt));
                    loginReq.setEnd_date(end.format(fmt));

                    List<MailLogService.LoginLogEntry> loginLogs = mailLogService.queryLoginLogs(loginReq);

                    if (!loginLogs.isEmpty()) {
                        loginLogs.sort(Comparator.comparingLong(MailLogService.LoginLogEntry::getTime).reversed());
                        MailLogService.LoginLogEntry last = loginLogs.get(0);
                        System.out.println("用户: " + user.getUserid() + "，最后登录时间: " + Instant.ofEpochSecond(last.getTime()));
                    } else {
                        System.out.println("用户: " + user.getUserid() + "，最近 " + monthCount + " 个月无登录记录。");
                    }

                    String start_date = start.format(fmt);
                    String end_date = end.format(fmt);

                    MailLogService.UserMailLogRequest request = new MailLogService.UserMailLogRequest();
                    request.setUserId(user.getUserid());
                    request.setBeginDate(start_date);
                    request.setEndDate(end_date);

                    MailLogService.UserMailLogResponse response = mailLogService.queryUserMailStatus(request);
                    int sentCount = 0;
                    Long lastSentTime = 0L;
                    if (!response.getList().isEmpty()) {
                        List<MailLogService.MailEntry> mailList = response.getList();
                        mailList.sort(Comparator.comparingLong(MailLogService.MailEntry::getTime));
                        sentCount = mailList.size();
                        lastSentTime = mailList.get(0).getTime();
                    }

                    System.out.println("用户: " + user.getUserid() + "，最近 " + monthCount +
                            " 个月发送邮件数: " + sentCount +
                            " 最后一次发送时间 " + lastSentTime);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 全局
        //                    Date startDate = Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant());
//                    Date endDate = Date.from(end.atStartOfDay(ZoneId.systemDefault()).toInstant());
//
//                    // 查询邮件发送日志
//                    MailLogService.MailStatusQueryRequest mailReq =
//                            new MailLogService.MailStatusQueryRequest(DOMAIN, startDate, endDate);
//
//                    MailLogService.MailStatus mailLogs = mailLogService.queryMailStatus(mailReq);
//                    int sentCount = mailLogs != null ? mailLogs.getSendsum() : 0;
    }
}
