package org.ahead4.email.provider.tencent;

import lombok.extern.slf4j.Slf4j;
import org.ahead4.email.operation.MailStats;
import org.ahead4.email.operation.StatsOp;
import org.ahead4.email.provider.tencent.internal.EmailClient;
import org.ahead4.email.provider.tencent.internal.MailLogService;

import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
public class TencentStatsOp implements StatsOp {

    private List<String> domains;
    private EmailClient client;

    public TencentStatsOp(EmailClient client) {
        this.client = client;
        this.domains = Arrays.asList("hbut.edu.cn", "mail.hbut.edu.cn");
    }

    @Override
    public Map<String, MailStats> getEmailStats(Date startDate, Date endDate) {
        Map<String, MailStats> dStats = new HashMap<>();
        for (String domain : this.domains) {
            MailLogService.MailStatus status = new MailLogService.MailStatus(0, 0);
            try {
                status = this.client.Log()
                        .queryMailStatus(new MailLogService
                                .MailStatusQueryRequest(domain, startDate, endDate));
            } catch (Exception e) {
                log.error("Failed to query mail stats for domain: {}", domain, e);
            }
            dStats.put(domain, status.toStats());
        }
        return dStats;
    }
    DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    @Override
    public MailLogService.LoginLogEntry getLastLoginLog(LocalDate start, LocalDate end, String userid) {

        // 查询登录日志
        MailLogService.LoginLogQueryRequest loginReq = new MailLogService.LoginLogQueryRequest();
        loginReq.setUserid(userid);
        loginReq.setBegin_date(start.format(fmt));
        loginReq.setEnd_date(end.format(fmt));

        try {
            List<MailLogService.LoginLogEntry> loginLogs = client.Log().queryLoginLogs(loginReq);

            if (!loginLogs.isEmpty()) {
                loginLogs.sort(Comparator.comparingLong(MailLogService.LoginLogEntry::getTime).reversed());
                MailLogService.LoginLogEntry last = loginLogs.get(0);
                System.out.println("用户: " +userid + "，最后登录时间: " + Instant.ofEpochSecond(last.getTime()));
                return last;
            } else {
                System.out.println("用户: " + userid + "，"+loginReq.getBegin_date() + "至" +loginReq.getEnd_date()+ "间无登录记录。");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }
    @Override
    public MailLogService.MailEntry getUserMailStats(LocalDate start, LocalDate end, String userid) {

        // 查询登录日志
        MailLogService.UserMailLogRequest mailReq = new MailLogService.UserMailLogRequest();
        mailReq.setUserId(userid);
        mailReq.setBeginDate(start.format(fmt));
        mailReq.setEndDate(end.format(fmt));
        mailReq.setMailType(1); // 只要发件
        try {
            MailLogService.UserMailLogResponse userMailStatus = client.Log().queryUserMailStatus(mailReq);
            List<MailLogService.MailEntry> mailStatusList = userMailStatus.getList();
            if (!mailStatusList.isEmpty()) {
                mailStatusList.sort(Comparator.comparingLong(MailLogService.MailEntry::getTime).reversed());
//                MailLogService.MailEntry send = null;
//                MailLogService.MailEntry received = null;
                for (MailLogService.MailEntry mailStatus : mailStatusList) {
                    if (mailStatus.getMailType() == 1)
                        return mailStatus;
//                    if (received == null && mailStatus.getMailType() == 2) received = mailStatus;
//                    if(send!=null && received!=null) break;
                }
                return null;
            } else {
                System.out.println("用户: " + userid + "，"+mailReq.getBeginDate()
                        + "至" +mailReq.getEndDate()+ "间无发信记录。");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
