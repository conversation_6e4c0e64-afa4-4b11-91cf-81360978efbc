package org.ahead4.email.provider.tencent.internal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.ahead4.email.operation.Department;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DepartmentDTO {
    private long id;
    private String name;
    private long parentid;
    private int order;

    // 从 Department 构造 DepartmentDTO
    public DepartmentDTO(Department department) {
        this.id = department.getDepartmentId();
        this.name = department.getDepartmentName();
    }

    public Department toDepartment() {
        Department department = new Department();
        department.setDepartmentId(this.id);
        department.setDepartmentName(this.name);
        return department;
    }
}
