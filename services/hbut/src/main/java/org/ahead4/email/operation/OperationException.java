package org.ahead4.email.operation;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OperationException extends Exception{

    public OperationException(String errorMsg) {
        super(errorMsg);;
        log.error("operation failed: {} ", errorMsg);
    }

    public OperationException(String errorMsg, Exception e) {
        super(errorMsg + " with: " + e.getMessage());
        log.error("operation failed: {} with exception: {} ", errorMsg, e.getMessage());
    }
}
