package org.ahead4.email.operation;

import org.ahead4.email.provider.tencent.internal.ErrorCodeException;
import org.ahead4.email.provider.tencent.internal.UserDTO;

public interface EmailAccountOp {
    void createEmailAccount(final UserEmailAccount account) throws OperationException;

    void changePassword(String userId, String password) throws OperationException, ErrorCodeException;

    void deleteEmailAccount(String userId) throws OperationException;

    void updateEmailAccount(UserEmailAccount userDTO) throws OperationException;

    UserEmailAccount getEmailAccount(String userId) throws OperationException;

    UserMailStats getEmailAccountStats(String userId) throws OperationException;
}
