package org.ahead4.email.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.common.tools.SpringContextHolder;
import org.ahead4.email.entity.EduEmail;
import org.ahead4.email.entity.EduEmailDto;
import org.ahead4.email.mapper.EduEmailMapper;
import org.ahead4.email.operation.*;
import org.ahead4.email.provider.tencent.TencentDepartmentOp;
import org.ahead4.email.provider.tencent.TencentEmailAccountOp;
import org.ahead4.email.provider.tencent.TencentStatsOp;
import org.ahead4.email.provider.tencent.internal.EmailClient;
import org.ahead4.email.provider.tencent.internal.ErrorCodeException;
import org.ahead4.email.provider.tencent.internal.MailLogService;
import org.ahead4.email.provider.tencent.internal.UserDTO;
import org.ahead4.email.service.EduEmailLogService;
import org.ahead4.email.service.EduEmailService;
import org.ahead4.hbut.dto.DockingStudent;
import org.ahead4.hbut.service.DockingService;
import org.ahead4.redis.service.StringKeyRedisTemplate;
import org.ahead4.redis.utils.RedisUtil;
import org.ahead4.security.utils.AccessHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.time.Instant;

/**
 * <p>
 * 教育邮箱用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Service
@Slf4j
public class EduEmailServiceImpl extends ServiceImpl<EduEmailMapper, EduEmail> implements EduEmailService {

    @Autowired
    DockingService dockingService;
    @Autowired
    EduEmailLogService eduEmailLogService;

    public static final String EDU_EMAIL_DATA_SYNC_TASK_LOCK = "EDU_EMAIL_DATA_SYNC_TASK_LOCK";


    EmailClient client;

    DepartmentOp deptOp;

    StatsOp  statsOp;

    EmailAccountOp emailAccountOp;

    /**
     * 是否禁用同步
     */
    @Value("${tencent.user-policy.disable:false}")
    Boolean disableSync;


    /**
     * 分块数量（影响每次同步占总用户数的比例设为3就是每次同步1/3的用户）
     */
    @Value("${tencent.user-policy.chunk-size:3}")
    Integer chunkSize;
    /**
     * 同步步数，决定同步日期向前步进几次
     */
    @Value("${tencent.user-policy.step-num:1}")
    Integer stepNum;
    /**
     * 同步步长，决定同步日期向前步进天数，负数为向过去回溯
     */
    @Value("${tencent.user-policy.step-length:-90}")
    Integer stepLength;

    @Value("${tencent.allow.delete:false}")
    Boolean allowDelete;

    public EduEmailServiceImpl(
            @Value("${tencent.copid:}")
            String copid,
            @Value("${tencent.email-secret:}")
            String emailSecret,
            @Value("${tencent.log-secret:}")
            String logSecret,
            SpringContextHolder context
    ) throws Exception {
        if (!StringUtils.isAllBlank(copid, emailSecret, logSecret)) {
            this.client = new EmailClient(copid, emailSecret, logSecret);
            this.deptOp = new TencentDepartmentOp(client);
            this.statsOp = new TencentStatsOp(client);
            this.emailAccountOp = new TencentEmailAccountOp(client);
        }
    }
//    @Scheduled(fixedRate = 10 * 60 * 1000) // 每10分钟刷新一次token
//    public void refreshToken() {
//        if (this.client !=null) {
//            this.client.refreshToken();
//        }
//    }
    @Override
    public EduEmail getWithUpdateStats(String userId) {
        EduEmail before = this.getById(userId);
        if (before == null) {
            return null;
        }
        if (!(StringUtils.isEmpty(before.getEmailAddr())) && this.checkEmailExist(before.getEmailAddr())) {
            LocalDate endDate = LocalDate.now(); // 当前日期
            LocalDate startDate = endDate.minusMonths(1); // 当前日期往回推1个月
            MailLogService.MailEntry sent = statsOp.getUserMailStats(startDate, endDate, before.getEmailAddr());
            if (sent != null) {
                Date last = Date.from(Instant.ofEpochSecond(sent.getTime()));
                if (before.getLastSendTime() == null || last.after(before.getLastSendTime())) {
                    before.setLastSendTime(last);
                }
            }
            MailLogService.LoginLogEntry login = statsOp.getLastLoginLog(startDate, endDate, before.getEmailAddr());
            if (login != null) {
                Date last = Date.from(Instant.ofEpochSecond(login.getTime()));
                if (before.getLastLoginTime() == null || last.after(before.getLastLoginTime())) {
                    before.setLastLoginTime(last);
                    before.setLastLoginIp(login.getIp());
                }
            }
            before.setSyncTime(new Date());
            this.saveOrUpdate(before);
        }
        return before;
    }

    @Override
    public boolean checkStudent(EduEmailDto param, StringBuilder errorMsg) {
        if (StringUtils.equals("test2025xs", param.getId()) &&
                StringUtils.equals("测试学生1", param.getName()) &&
                StringUtils.equals("15623614229", param.getPhone()) &&
                StringUtils.equals("******************", param.getIdCardNo())) {
            return true;
        }

        DockingStudent json = dockingService.getStudent(param.getId());
        // Check if the JSON object is null or not matching
        if (json == null) {
            errorMsg.append(param.getId() + " 不存在");
            log.info("No data found for student: {}", param.getId());
            return false;
        }

        // Individual checks with simple "A VS B" error messages
        if (!StringUtils.equals(json.getCode(), param.getId())) {
            errorMsg.append(param.getId() + " VS " + json.getCode());
            log.info("Diff param: {};;; docking param: {}", param, json);
            return false;
        }

        if (!StringUtils.equals(json.getPhone(), param.getPhone())) {
            errorMsg.append(param.getPhone() + " VS " + json.getPhone());
            log.info("Diff param: {};;; docking param: {}", param, json);
            return false;
        }

        if (!StringUtils.equals(json.getIdCardNo(), param.getIdCardNo())) {
            errorMsg.append(param.getIdCardNo() + " VS " + json.getIdCardNo());
            log.info("Diff param: {};;; docking param: {}", param, json);
            return false;
        }

        if (!StringUtils.equals(json.getName(), param.getName())) {
            errorMsg.append(param.getName() + " VS " + json.getName());
            log.info("Diff param: {};;; docking param: {}", param, json);
            return false;
        }

        // If all checks pass, return true
        return true;
    }


    @Override
    public boolean checkEmailExist(String email) {
        try {
            return emailAccountOp.getEmailAccount(email) != null;
        } catch (OperationException e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }

    @Override
    public boolean updateUserStatus(String userId, int status) {
        if (!allowDelete) {
            return false;
        }
        try {
            UserEmailAccount email = emailAccountOp.getEmailAccount(userId);
            if (null == email){
                eduEmailLogService.push("修改", "用户"+AccessHolder.username()+"对邮箱"+userId+"进行进行"+(status == 1 ? "解封" : "封禁") + "操作失败，原因：邮箱不存在", "失败");
                return false;
            }
            if (email.getEnable() != status) {
                UserEmailAccount update = new UserEmailAccount();
                update.setUserId(userId);
                update.setEnable(status);
                emailAccountOp.updateEmailAccount(update);
            }
            eduEmailLogService.push("修改", "用户"+AccessHolder.username()+"对邮箱"+userId+"进行进行"+(status == 1 ? "解封" : "封禁") + "操作成功", "成功");
            return true;

        } catch (OperationException e) {
            log.error(e.getMessage(), e);
            eduEmailLogService.push("修改", "用户"+AccessHolder.username()+"对邮箱"+userId+"进行进行"+(status == 1 ? "解封" : "封禁") + "操作失败，原因："+e.getMessage(), "失败");

            return false;
        }
    }

    /**
     * 删除用户的邮箱账号
     * @param id
     */
    @Override
    public void deleteUserEmailAccount(String id) {
        if (!allowDelete) {
            throw new RuntimeException("系统不允许删除邮箱");
        }
        EduEmail email = this.getById(id);
        if (email == null) {
            return;
        }
        try {
            if (email.getEmailAddr()!= null && !(email.getEmailAddr().isEmpty())) {
                UserEmailAccount exist = emailAccountOp.getEmailAccount(email.getEmailAddr());
                if (exist != null) {
                    emailAccountOp.deleteEmailAccount(email.getEmailAddr());
                }
            }
            this.removeById(id);
            eduEmailLogService.push("删除", "用户"+AccessHolder.username()+"对邮箱"+email.getEmailAddr()+"进行进行删除" + "操作成功", "成功");

        } catch (OperationException e) {
            log.error(e.getMessage(), e);
            eduEmailLogService.push("删除", "用户"+AccessHolder.username()+"对邮箱"+email.getEmailAddr()+"进行进行删除" + "操作失败，原因："+e.getMessage(), "失败");

            throw new RuntimeException(e);
        }
    }


    @Override
    public String signup(EduEmail eduEmail, String password, boolean isStudent) {
        String result = "失败";
        String msg = "";
        try {
            String emailAddr = eduEmail.getEmailAddr();
            UserEmailAccount account = new UserEmailAccount();
            account.setPassword(password);
            if (isStudent) {
                account.setDepartmentId(5592082150127578564L);
                account.setDepartment("学生");
            } else {
                account.setDepartmentId(5592082150127578531L);
                account.setDepartment("教职工");
            }
            account.setMobile(eduEmail.getPhone());
            account.setUserId(emailAddr);
            account.setDisplayName(eduEmail.getName());
            emailAccountOp.createEmailAccount(account);
            eduEmail.setUserid(emailAddr);
            eduEmail.setCreateTime(new Date());
            eduEmail.setCreator(AccessHolder.username());
            eduEmail.setMailDeptId(String.valueOf(account.getDepartmentId()));
            eduEmail.setMailDeptName(account.getDepartment());
            this.saveOrUpdate(eduEmail);
            result = "成功";
            return null;
        } catch (Exception e) {
            result = "失败";
            msg = e.getMessage();
            if (StringUtils.contains(msg, "invalid password")) {
                return "密码必须同时包含大小写字母和数字，长度8-32位，不包含账户信息!";
            }
            return "注册失败！";
        } finally {
            eduEmailLogService.push("注册邮箱",
                    "用户"+AccessHolder.username()+
                            "进行"+eduEmail.getEmailAddr()
                            +"注册操作"+result+(!msg.equals("") ? ("原因："+msg) : "")
                    , result);
        }
    }
    @Override
    public String changePassword(String id, String email, String password) {
        if (!allowDelete) {
            return "系统不允许修改密码";
        }
        String result = "失败";
        String msg = "";
        try {
            emailAccountOp.changePassword(email, password);
            this.lambdaUpdate()
                    .set(EduEmail::getUpdateTime, LocalDate.now())
                    .set(EduEmail::getUpdator, AccessHolder.username())
                    .eq(EduEmail::getId, id)
                    .update();
            result = "成功";
            return null;
        } catch (ErrorCodeException e) {
            result = "失败";
            msg = e.getMessage();
            return msg;
        }catch (Exception e) {
            result = "失败";
            msg = e.getMessage();
            return "密码修改失败";
        } finally {
            eduEmailLogService.push("修改密码", "用户"+AccessHolder.username()+"对邮箱"+email+"进行密码重置操作"+result+(!msg.equals("") ? ("原因："+msg) : ""), result);
        }
    }


    /**
     * 0点执行同步
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void syncData() {
        if (disableSync) {
            log.info("Ignore syn due to configuration");
            return;
        }
        startSyncData();
    }

    /**
     * 启动同步流程
     */
    @Async
    public void startSyncData() {

        if (RedisUtil.hasKey(EDU_EMAIL_DATA_SYNC_TASK_LOCK)) {
            log.info("教育邮箱数据同步任务已有节点正在执行，本节点跳过。");
            return;
        }
        try {
            // 获取锁
            RedisUtil.set(EDU_EMAIL_DATA_SYNC_TASK_LOCK, System.currentTimeMillis(), 60 * 60L);

            // 将List转为Map，key为id，value为部门
            Map<Long, Department> map = deptOp.listAll().stream()
                    .collect(Collectors.toMap(
                            Department::getDepartmentId,  // Key
                            dept -> dept,                 // Value
                            (existing, replacement) -> existing  // 保留已存在的值，忽略新值
                    ));
            // 遍历部门
            List<UserDTO> users = new ArrayList<>();
            try {
                // 按照部门取回全部用户邮箱
                users = client.user().listUsersByDepartment(1L, true);
//                    allUsers.addAll(users); // 记录到全部用户集合方便后面检查哪些被删除了
            }   catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            // 补全全部字段
            for (UserDTO user : users) {
                String email = user.getUserid();
                String id = email.split("@")[0];
                // 插入或更新
                EduEmail eduEmail = this.getById(id);
                eduEmail = eduEmail == null ? new EduEmail() : eduEmail;
                eduEmail.setId(id);
                eduEmail.setName(user.getName());
                eduEmail.setEmailAddr(email);
                eduEmail.setUserid(email);
                if(user.getDepartment() != null) {
                    eduEmail.setMailDeptId(user.getDepartment().stream().map(String::valueOf).collect(Collectors.joining(",")));
                    eduEmail.setMailDeptName(user.getDepartment().stream().map(deptId -> map.getOrDefault(deptId, new Department()).getDepartmentName()).collect(Collectors.joining(",")));
                }
                eduEmail.setEnable(String.valueOf(user.getEnable()));
                eduEmail.setGender(user.getGender());
                eduEmail.setPhone(user.getMobile());
                eduEmail.setPosition(user.getPosition());
                if (user.getDepartment() != null ) {
                    eduEmail.setMailDeptId(user.getDepartment().stream().map(String::valueOf).collect(Collectors.joining(",")));
                }
                if (user.getSlaves() != null ) {
                    eduEmail.setSlaves(String.join(",", user.getSlaves()));
                }
                if (StringUtils.isEmpty(eduEmail.getCreator())) {
                    eduEmail.setCreator("sync-task");
                    eduEmail.setCreateTime(new Date());
                    eduEmail.setUpdator("sync-task");
                    eduEmail.setUpdateTime(new Date());
                    eduEmail.setSyncTime(new Date());
                }
                this.saveOrUpdate(eduEmail);
            }
            Integer total = this.query().count();
            Page<EduEmail> curUserSyncTasks = this.lambdaQuery()
                    .lt(EduEmail::getSyncTime, LocalDate.now().plusDays(-3)
                            .atStartOfDay())
                    .or().isNull(EduEmail::getSyncTime)
                    .page(new Page<>(1, total / chunkSize +100)); //每次取出1/7 + 100条
            curUserSyncTasks.getRecords().forEach(el -> {
                if (StringUtils.isEmpty(el.getEmailAddr())) { //一般不会到这里
                    el.setEmailAddr(el.getUserid()+"@hbut.edu.cn");
                    rollingUpdateLastTime(el.getEmailAddr(), LocalDate.now(), stepNum, stepLength);
                    el.setEmailAddr(el.getUserid()+"@mail.hbut.edu.cn");
                    rollingUpdateLastTime(el.getEmailAddr(), LocalDate.now(), stepNum, stepLength);
                } else {
                    rollingUpdateLastTime(el.getEmailAddr(), LocalDate.now(), stepNum, stepLength);
                }
            });            // TODO 查找被删除的标记被删除
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            // 释放锁
            RedisUtil.del(EDU_EMAIL_DATA_SYNC_TASK_LOCK);
        }
    }

    /**
     * 时间滚动查找最后一次发信和登录时间
     * @param allUsers 用户对象集合
     * @param start 开始时间
     * @param stepNum 步数
     * @param stepLength 步长（天）
     */
    public void rollingUpdateLastTime(List<UserDTO> allUsers,LocalDate start, int stepNum, int stepLength) {
        for (UserDTO user : allUsers) {
            try {
                rollingUpdateLastTime(user.getUserid(), start, stepNum, stepLength);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 时间滚动查找最后一次发信和登录时间
     * @param userid 用户id
     * @param start 开始时间（LocalDate）
     * @param stepNum 步数
     * @param stepLength 步长（天）
     */
    public void rollingUpdateLastTime(String userid, LocalDate start, int stepNum, int stepLength) {
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        UserEmailAccount email = null;
        try {
            email = emailAccountOp.getEmailAccount(userid);
        } catch (OperationException e) {
            log.error("tell us why: {};", userid, e);
        }
        if (null == email){
            return;
        }
        StatsOp statsOp = new TencentStatsOp(client);

        LocalDate currentStart = start;
        for (int i = 0; i < stepNum; i++) {
            // 当前步进的开始时间
            LocalDate stepStart = currentStart;
            // 增加步长天数，得到结束时间
            LocalDate stepEnd = stepStart.plusDays(stepLength);
            // 更新下一轮的开始时间（步进后的起始点）
            currentStart = stepEnd;
            if (stepStart.isAfter(stepEnd)) {
                LocalDate temp = stepStart;
                stepStart = stepEnd;
                stepEnd = temp;
            }
            MailLogService.LoginLogEntry lastLoginLog = null;
            try {
                lastLoginLog = statsOp.getLastLoginLog(stepStart, stepEnd, userid);
            } catch (Exception e) {
                if (StringUtils.contains(e.getMessage(), "query date overlimit")) {
                    continue;
                }
                log.error(e.getMessage(), e);
            }
            LocalDateTime lastLoginTime = null;
            if (lastLoginLog == null) {
                lastLoginLog = new MailLogService.LoginLogEntry();
            }
            if (lastLoginLog.getTime() != null) {
                // 将秒级时间戳转为 Instant
                Instant instant = Instant.ofEpochSecond(lastLoginLog.getTime());
                lastLoginTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
            }
            MailLogService.MailEntry lastSendLog = null;
            LocalDateTime lastSendTime = null;
            try {
                lastSendLog = statsOp.getUserMailStats(stepStart, stepEnd, userid);
            } catch (Exception e) {
                if (StringUtils.contains(e.getMessage(), "query date overlimit")) {
                    continue;
                }
                log.error(e.getMessage(), e);
            }
            if (lastSendLog != null && lastSendLog.getTime() != null) {
                Instant entryInstant = Instant.ofEpochSecond(lastSendLog.getTime());
                lastSendTime = entryInstant.atZone(ZoneId.systemDefault()).toLocalDateTime();
            }
            this.lambdaUpdate()
                    .set(EduEmail::getLastLoginTime, lastLoginTime)
                    .set(EduEmail::getLastSendTime, lastSendTime)
                    .set(EduEmail::getLastLoginIp, lastLoginLog.getIp())
                    .set(EduEmail::getSyncTime, new Date())
                    .eq(EduEmail::getEmailAddr, userid)
                    .update();
        }
    }
}
