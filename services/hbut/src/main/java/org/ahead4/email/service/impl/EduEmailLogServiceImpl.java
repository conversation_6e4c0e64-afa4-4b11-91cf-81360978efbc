package org.ahead4.email.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.email.entity.EduEmailLog;
import org.ahead4.email.mapper.EduEmailLogMapper;
import org.ahead4.email.service.EduEmailLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.ahead4.logger.dto.ParamDto;
import org.ahead4.security.dto.IUserDetail;
import org.ahead4.security.utils.AccessHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 教育邮箱操作审计日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Service
@Slf4j
public class EduEmailLogServiceImpl extends ServiceImpl<EduEmailLogMapper, EduEmailLog> implements EduEmailLogService {

//    @Async
    @Override
    public EduEmailLog push(String username, String type, ParamDto params, String remark, String result, Exception exception) {
        EduEmailLog.EduEmailLogBuilder builder = EduEmailLog.builder();
        if (StringUtils.isNotEmpty(type)) builder.type(type);
        if (StringUtils.isNotEmpty(username)) builder.username(username);
        if (params != null) {
            builder.params(JSON.toJSONString(params));
        }
        if (exception != null) builder.ex(JSON.toJSONString(exception));
        return push(builder.remark(remark).result(result).build());
    }
//    @Async
    @Override
    public EduEmailLog push(String type, String remark, String result) {
        EduEmailLog.EduEmailLogBuilder builder = EduEmailLog.builder();
        if (StringUtils.isNotEmpty(type)) builder.type(type);
        return push(builder.remark(remark).result(result).build());
    }
//    @Async
    @Override
    public EduEmailLog push(String username, String type, String remark, String result, Exception exception) {
        EduEmailLog.EduEmailLogBuilder builder = EduEmailLog.builder();
        if (StringUtils.isNotEmpty(type)) builder.type(type);
        if (StringUtils.isNotEmpty(username)) builder.username(username);
        if (exception != null) builder.ex(JSON.toJSONString(exception));
        EduEmailLog entity = builder.remark(remark).result(result).build();
        return push(entity);
    }

//    @Async
    @Override
    public EduEmailLog push(EduEmailLog entity) {

        try {
            entity.setCreateTime(new Date());
            entity.setSource(AccessHolder.getClientInfo());
            entity.setIp(entity.getSource().getIp());
            entity.setUrl(entity.getSource().getUrl());
            entity.setMethod(entity.getSource().getMethod());
            if (entity.getSource() != null && entity.getSource().getCurrentUser() != null) {
                IUserDetail user = entity.getSource().getCurrentUser();
                entity.setCreator(user.getUsername());
                entity.setName(user.getDisplayname());
                entity.setUsername(user.getUsername());
                entity.setMobile(user.getMobile());
            }
            this.save(entity);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(),e);
        }
        return null;
    }
}
