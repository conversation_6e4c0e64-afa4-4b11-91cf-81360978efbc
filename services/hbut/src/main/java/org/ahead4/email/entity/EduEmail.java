package org.ahead4.email.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 教育邮箱用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="EduEmail对象", description="教育邮箱用户表")
public class EduEmail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学工号（主键）")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证号")
    private String idCardNo;

    @ApiModelProperty(value = "企业邮箱成员UserID")
    private String userid;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "邮箱地址")
    private String emailAddr;

    @ApiModelProperty(value = "别名列表：1、Slaves上限为5个；2、Slaves为邮箱格式")
    private String slaves;

    @ApiModelProperty(value = "职位信息")
    private String position;

    @ApiModelProperty(value = "性别：0表示未定义，1表示男性，2表示女性")
    private String gender;

    @ApiModelProperty(value = "启用/禁用成员。1表示启用成员，0表示禁用成员")
    private String enable;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "部门id")
    private String deptId;

    @ApiModelProperty(value = "邮箱部门名称")
    private String mailDeptName;

    @ApiModelProperty(value = "邮箱部门id")
    private String mailDeptId;

    @ApiModelProperty(value = "最后登录时间")
    private Date lastLoginTime;

    @ApiModelProperty(value = "最后发送邮件时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastSendTime;
    @ApiModelProperty(value = "最后接收邮件时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastReceiveTime;
    @ApiModelProperty(value = "最后登录邮箱IP")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String lastLoginIp;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updator;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncTime;


}
