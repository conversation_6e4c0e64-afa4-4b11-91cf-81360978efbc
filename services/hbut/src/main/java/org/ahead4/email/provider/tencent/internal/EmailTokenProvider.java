package org.ahead4.email.provider.tencent.internal;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.ahead4.redis.utils.RedisUtil;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

public class EmailTokenProvider {
    private static final String EXMAIL_QQ_TOKEN_KEY = "EXMAIL_QQ_TOKEN_ACCESS_TOKEN_";
    private static final String EXMAIL_QQ_LOCK_KEY = "EXMAIL_QQ_REFRSH_TOKEN_LOCK_";
    private static final String TOKEN_URL = "https://api.exmail.qq.com/cgi-bin/gettoken";
    private final String corpId;
    private final String corpSecret;
    private final String type;

    public EmailTokenProvider(String corpId, String corpSecret, String type) throws Exception {
        this.corpId = corpId;
        this.corpSecret = corpSecret;
        this.type = type;
    }

    public void fetchAccessToken() throws Exception {
        if (RedisUtil.hasKey(EXMAIL_QQ_LOCK_KEY+type)) {
            return;
        }
        try {
            RedisUtil.set(EXMAIL_QQ_LOCK_KEY+type, "EXMAIL_QQ_LOCK", 10 * 60L);
            String urlString = TOKEN_URL + "?corpid=" + corpId + "&corpsecret=" + corpSecret;
            URL url = new URL(urlString);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();

            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);

            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(), "UTF-8")
            );
            StringBuilder responseBuilder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                responseBuilder.append(line);
            }
            reader.close();
            conn.disconnect();

            parseTokenResponse(responseBuilder.toString());
        } finally {
            RedisUtil.del(EXMAIL_QQ_LOCK_KEY+type);
        }
    }
    private void parseTokenResponse(String response) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(response);

        if (root.has("access_token")) {
            String accessToken = root.get("access_token").asText();
            long expiresIn = root.get("expires_in").asLong();
            RedisUtil.set(EXMAIL_QQ_TOKEN_KEY+type, accessToken, expiresIn);
        } else {
            int errcode = root.has("errcode") ? root.get("errcode").asInt() : -1;
            String errmsg = root.has("errmsg") ? root.get("errmsg").asText() : "Unknown error";
            throw new Exception("Failed to get access token: " + errcode + " - " + errmsg);
        }
    }

    public String getAccessToken(boolean force) {
        String token =  RedisUtil.get(EXMAIL_QQ_TOKEN_KEY+type);
        if (force || token == null || token.isEmpty()) {
            try {
                fetchAccessToken();
                token =  RedisUtil.get(EXMAIL_QQ_TOKEN_KEY+type);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return token;
    }

    public long getExpiresIn() {
        return RedisUtil.getExpire(EXMAIL_QQ_TOKEN_KEY+type);
    }
}
