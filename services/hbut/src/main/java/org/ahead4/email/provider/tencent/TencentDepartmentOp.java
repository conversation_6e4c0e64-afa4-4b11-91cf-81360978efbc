package org.ahead4.email.provider.tencent;

import lombok.extern.slf4j.Slf4j;
import org.ahead4.email.operation.Department;
import org.ahead4.email.operation.DepartmentOp;
import org.ahead4.email.provider.tencent.internal.DepartmentDTO;
import org.ahead4.email.provider.tencent.internal.EmailClient;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class TencentDepartmentOp implements DepartmentOp {
    private EmailClient client;

    public TencentDepartmentOp(EmailClient client) {
        this.client = client;
    }
    @Override
    public List<Department> list(long parentId) {
        try {
            List<DepartmentDTO> departs = client.department().list(parentId);
            List<Department> results = departs.stream().map(DepartmentDTO::toDepartment).collect(Collectors.toList());
            return results;
        } catch (Exception e) {
            log.error("Failed to list departments", e);
        }
        return new ArrayList<>();
    }

    private void collectDepts(long parentId, List<Department> all) {
        log.info("Getting children of {}", parentId);
        List<Department> children = list(parentId);
        log.info("children size for {} is  {}", parentId,children.size());
        for (Department dept : children) {
            if(dept.getDepartmentId() == parentId) { // tencent depts children has itself
                continue;
            }
            all.add(dept);
            collectDepts(dept.getDepartmentId(), all);
        }
    }

    @Override
    public List<Department> listAll() {
        List<Department> all = new ArrayList<>();
        collectDepts(1L, all);
        return all;
    }
}
