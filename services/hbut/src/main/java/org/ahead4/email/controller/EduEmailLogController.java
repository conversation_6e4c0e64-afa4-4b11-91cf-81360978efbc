package org.ahead4.email.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.ahead4.web.presentate.HttpMsg;
import org.ahead4.common.dto.PageParam;
import org.ahead4.security.utils.AccessHolder;
import org.ahead4.email.entity.EduEmailLog;
import org.ahead4.email.service.EduEmailLogService;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.ahead4.common.dto.Page;
import static org.ahead4.jdbc.utils.MybatisPlusUtils.*;

/**
 * <p>
 * 教育邮箱操作审计日志 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@RestController
@RequestMapping("/edu-email-log")
@Api(value = "教育邮箱操作审计日志", description = "教育邮箱操作审计日志控制器")
public class EduEmailLogController {
    @Autowired
    EduEmailLogService service;
    /**
     * 详情
     * @return
     */
    @GetMapping("detail/{id}")
    @ApiOperation(value = "详情")
    public EduEmailLog get(@PathVariable String id) {
        return service.getById(id);
    }

    /**
     * 分页
     * @return
     */
    @PostMapping("page")
    @ApiOperation(value = "分页")
    public Page<EduEmailLog> page(@RequestBody PageParam param) {
        if (param.getSortMap().isEmpty()) {
            param.getSortMap().put("create_time", "DESC");
        }
        return plusPage2Page(service.page(param2PlusPage(param), param2PlusWrapper(param)));
    }

//    /**
//     * 删除
//     * @return
//     */
//    @DeleteMapping({"{id}", ""})
//    @ApiOperation(value = "删除")
//    public HttpMsg delete(@PathVariable(required = false) String id, @RequestParam(required = false) List<String> idlist) {
//        boolean r = false;
//        if (!CollectionUtils.isEmpty(idlist)) {
//            r = service.removeByIds(idlist);
//        } else if (!StringUtils.isEmpty(id)) {
//            r = service.removeById(id);
//        }
//        return r ? HttpMsg.ok("删除成功！") : HttpMsg.error("删除失败！");
//    }
}

