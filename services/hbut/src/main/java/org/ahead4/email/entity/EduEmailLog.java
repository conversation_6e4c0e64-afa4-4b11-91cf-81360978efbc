package org.ahead4.email.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.ahead4.security.config.ClientInfoHandler;
import org.ahead4.security.dto.ClientInfo;

/**
 * <p>
 * 教育邮箱操作审计日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */

@ApiModel(value="EduEmailLog对象", description="教育邮箱操作审计日志")
@Builder
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class EduEmailLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "记录类型（系统日志、敏感操作）")
    private String type;

    @ApiModelProperty(value = "账号")
    private String username;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "接口请求方式")
    private String method;

    @ApiModelProperty(value = "地址")
    private String url;

    @ApiModelProperty(value = "请求IP")
    private String ip;

    @ApiModelProperty(value = "代理IP")
    private String ipproxy;

    @ApiModelProperty(value = "请求来源")
    @TableField(value = "SOURCE", typeHandler = ClientInfoHandler.class)
    private ClientInfo source;

    @ApiModelProperty(value = "参数")
    private String params;

    @ApiModelProperty(value = "结果")
    private String result;

    @ApiModelProperty(value = "说明")
    private String remark;

    @ApiModelProperty(value = "异常信息")
    private String ex;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String creator;


}
