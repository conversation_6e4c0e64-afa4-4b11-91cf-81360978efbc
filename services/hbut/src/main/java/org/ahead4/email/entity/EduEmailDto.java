package org.ahead4.email.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 教育邮箱用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="EduEmail注册表单对象", description="EduEmail注册表单对象")
public class EduEmailDto implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "注册邮箱域名（仅注册时有效，目前只支持mail.hbut.edu.cn）")
    private String domain;

    @ApiModelProperty(value = "学工号")
    @NotBlank(message = "用户名不能为空")
    private String id;

    @ApiModelProperty(value = "姓名")
    @NotBlank(message = "姓名不能为空")
    private String name;

    @ApiModelProperty(value = "身份证号")
//    @NotBlank(message = "身份证号不能为空")
    private String idCardNo;

    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "手机多因素认证验证码")
    @NotBlank(message = "手机验证码不能为空")
    private String phoneMfaCode;

    @ApiModelProperty(value = "新密码")
    @Size(min = 6, max = 20, message = "密码长度需在6-20位之间")
    private String password;

    @ApiModelProperty(value = "确认密码")
    private String confirmPassword;

    @AssertTrue(message = "两次输入的密码必须一致")
    public boolean isPasswordMatch() {
        return password != null && password.equals(confirmPassword);
    }

}
