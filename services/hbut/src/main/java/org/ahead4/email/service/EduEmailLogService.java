package org.ahead4.email.service;

import org.ahead4.email.entity.EduEmailLog;
import com.baomidou.mybatisplus.extension.service.IService;
import org.ahead4.logger.dto.ParamDto;
import org.springframework.scheduling.annotation.Async;

/**
 * <p>
 * 教育邮箱操作审计日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
public interface EduEmailLogService extends IService<EduEmailLog> {

    EduEmailLog push(String username, String type, ParamDto params, String remark, String result, Exception exception);

    EduEmailLog push(String type, String remark, String result);

    EduEmailLog push(String username, String type, String remark, String result, Exception exception);

    EduEmailLog push(EduEmailLog entity);
}
