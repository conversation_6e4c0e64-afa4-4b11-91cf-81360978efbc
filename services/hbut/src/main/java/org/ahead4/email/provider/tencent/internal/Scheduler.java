package org.ahead4.email.provider.tencent.internal;
import java.time.*;
import java.util.concurrent.*;

public class Scheduler {

    public static void scheduleDailyAt1AM(Runnable task) {
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

        long delay = computeInitialDelay();
        long period = TimeUnit.DAYS.toSeconds(1);

        scheduler.scheduleAtFixedRate(task, delay, period, TimeUnit.SECONDS);
    }

    private static long computeInitialDelay() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextRun = now.withHour(1).withMinute(0).withSecond(0);

        if (now.compareTo(nextRun) >= 0) {
            nextRun = nextRun.plusDays(1);
        }

        return Duration.between(now, nextRun).getSeconds();
    }
}