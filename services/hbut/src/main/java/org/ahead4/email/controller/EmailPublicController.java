package org.ahead4.email.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.cdes.service.SmsService;
import org.ahead4.cdes.utils.ValidatorUtils;
import org.ahead4.email.entity.EduEmail;
import org.ahead4.email.entity.EduEmailDto;
import org.ahead4.email.service.EduEmailLogService;
import org.ahead4.email.service.EduEmailService;
import org.ahead4.hbut.service.DockingService;
import org.ahead4.security.dto.IUserDetail;
import org.ahead4.security.utils.AccessHolder;
import org.ahead4.web.presentate.HttpMsg;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 邮箱注册系列接口
 *
 * */
@Slf4j
@RestController
@RequestMapping("/email")
@Api(tags = "邮箱注册系列接口（公众部分）")
public class EmailPublicController {
    @Autowired
    private Validator validator;
    @Autowired
    EduEmailService eduEmailService;
    @Autowired
    SmsService smsService;
    @Autowired
    EduEmailLogService eduEmailLogService;

    @ApiOperation(value = "获取当前用户的邮箱信息")
    @GetMapping("me")
    public HttpMsg me() {
        String id = AccessHolder.username();
        IUserDetail user = AccessHolder.user();
        EduEmail eduEmail = eduEmailService.getById(id);
        if (eduEmail == null) {
            eduEmail = new EduEmail();
            eduEmail.setId(user.getUsername());
            eduEmail.setName(user.getDisplayname());
            eduEmail.setPhone(user.getMobile());
            eduEmail.setGender(user.getGender());
            if (user.getGender() == null) {
                eduEmail.setGender("0");
            } else {
                eduEmail.setGender(Objects.equals(user.getGender(), "男") ? "1" : "2");
            }
            eduEmail.setEnable("1");
            eduEmail.setCreator(user.getUsername());
            eduEmail.setCreateTime(new Date());
            eduEmailService.saveOrUpdate(eduEmail);
        } else {
            if (eduEmail.getPhone() != null &&
                    !(eduEmail.getPhone().equals(user.getMobile()))) {
                eduEmail.setPhone(user.getMobile());
                eduEmailService.saveOrUpdate(eduEmail);
            }
        }
        return HttpMsg.ok().data(eduEmail);
    }

    @ApiOperation(value = "注册邮箱")
    @PostMapping("signup")
    public HttpMsg signup(@RequestBody EduEmailDto param) {
        // 主动调用校验
        List<Map<String, String>> errors = ValidatorUtils.validateEntity(param);
        if (!errors.isEmpty()) {
            // 处理校验错误
            return HttpMsg.error(HttpStatus.BAD_REQUEST, "提交内容未通过校验").data(errors);
        }
        // TODO 目前不支持其他域名，以后可以通过枚举检查后通过别名机制支持多个域名
        param.setDomain("hbut.edu.cn");
        String domain = StringUtils.defaultString(param.getDomain(), "hbut.edu.cn");
        if (!smsService.verifyCode(param.getPhone(), param.getPhoneMfaCode(), "EDU_EMAIL")) {
            eduEmailLogService.push("注册邮箱",
                    "用户"+AccessHolder.username()+
                            "进行"+param.getId()+"@"+domain
                            +"注册操作失败原因：手机验证码错误"
                    , "失败");
            // 处理校验错误
            return HttpMsg.error(HttpStatus.BAD_REQUEST, "手机验证码错误！");
        }
        EduEmail emailUser = new EduEmail();
        emailUser.setEmailAddr(AccessHolder.username() +"@"+ domain);
        // 检查邮箱是否被注册
        if (eduEmailService.checkEmailExist(emailUser.getEmailAddr())) {
            eduEmailLogService.push("注册邮箱",
                    "用户"+AccessHolder.username()+
                            "进行"+emailUser.getEmailAddr()
                            +"注册操作失败原因：邮箱地址已经被注册"
                    , "失败");
            // 处理校验错误
            return HttpMsg.error(HttpStatus.BAD_REQUEST, "邮箱地址"+emailUser.getEmailAddr()+"已经被注册！");
        }

        boolean isStudent = false;
        if (AccessHolder.user().auth("ROLE_Student")) {
            isStudent = true;
        }

        if (isStudent) {
            StringBuilder errorMsg = new StringBuilder();
            if (!eduEmailService.checkStudent(param, errorMsg)) {
                eduEmailLogService.push("注册邮箱",
                        "用户" + AccessHolder.username() +
                                "进行" + param.getId() + "@" + domain
                                + "注册操作失败原因：用户信息不匹配： " + errorMsg
                        , "失败");
                // 处理校验错误
                return HttpMsg.error(HttpStatus.BAD_REQUEST, "提供的用户信息与学校预留不一致！");
            }
        } else {
            if (!param.getId().equals(AccessHolder.user().getUsername()) ||
            !param.getPhone().equals(AccessHolder.user().getMobile())) {
                // logging param and accessholder
                log.info("param id: {}, accessholder id: {};; param phone: {}, accessholder phone: {}",
                        param.getId(), AccessHolder.user().getUsername(),
                        param.getPhone(), AccessHolder.user().getMobile());
                eduEmailLogService.push("注册邮箱",
                        "用户" + AccessHolder.username() +
                                "进行" + param.getId() + "@" + domain
                                + "注册操作失败原因：用户信息不匹配： " + param.getPhone() + " VS " + AccessHolder.user().getMobile()
                        , "失败");
                // 处理校验错误
                return HttpMsg.error(HttpStatus.BAD_REQUEST, "提供的用户信息与学校预留不一致！");
            }
        }
        emailUser.setId(AccessHolder.username()); // 用户ID取当前用户
        emailUser.setName(AccessHolder.displayname());
        emailUser.setPhone(param.getPhone());
        emailUser.setEnable("1");
        emailUser.setIdCardNo(param.getIdCardNo());
        // 注册
        String msg = eduEmailService.signup(emailUser, param.getPassword(), isStudent);
        return StringUtils.isEmpty(msg) ? HttpMsg.ok() : HttpMsg.error(msg);
    }
    @ApiOperation(value = "重置密码")
    @PostMapping("reset-password")
    public HttpMsg resetPassword(@RequestBody EduEmailDto param) {
        // 主动调用校验
//        List<Map<String, String>> errors = ValidatorUtils.validateEntity(param);
//        if (!errors.isEmpty()) {
//            // 处理校验错误
//            return HttpMsg.error(HttpStatus.BAD_REQUEST, "提交内容未通过校验").data(errors);
//        }
        EduEmail emailUser = eduEmailService.getById(param.getId());
        if (emailUser == null) {
            eduEmailLogService.push("重置密码",
                    "用户"+AccessHolder.username()+
                            "进行"+param.getId()+"@"+param.getDomain()
                            +"重置密码失败原因：邮箱信息不存在"
                    , "失败");
            return HttpMsg.error(HttpStatus.BAD_REQUEST, "邮箱信息不存在！");
        }
        // 验证手机验证码
        if (!smsService.verifyCode(param.getPhone(), param.getPhoneMfaCode(), "EDU_EMAIL")) {
            eduEmailLogService.push("重置密码",
                    "用户"+AccessHolder.username()+
                            "进行"+emailUser.getEmailAddr()
                            +"重置密码失败原因：手机验证码错误"
                    , "失败");
            // 处理校验错误
            return HttpMsg.error(HttpStatus.BAD_REQUEST, "手机验证码错误！");
        }
        // 验证用户信息
        if (!StringUtils.equals(param.getPhone(), AccessHolder.user().getMobile())) {
            eduEmailLogService.push("重置密码",
                    "用户"+AccessHolder.username()+
                            "进行"+emailUser.getEmailAddr()
                            +"重置密码失败原因：用户信息不匹配:" + param.getPhone() + " VS "+ AccessHolder.user().getMobile()
                    , "失败");
            // 处理校验错误
            return HttpMsg.error(HttpStatus.BAD_REQUEST, "手机号与学校预留不一致！");
        }
//        // 验证用户信息
//        if (eduEmailService.checkStudent(param)) {
//            // 处理校验错误
//            return HttpMsg.error(HttpStatus.BAD_REQUEST, "提供的用户信息与学校预留不一致！");
//        }
        String msg = eduEmailService.changePassword(param.getId(), emailUser.getEmailAddr(), param.getPassword());
        // 重置
        return StringUtils.isEmpty(msg) ? HttpMsg.ok() : HttpMsg.error(msg);
    }


}
