package org.ahead4.email.provider.tencent.internal;

public class EmailClient {
    private final DepartmentService departmentService;
    private final UserService userService;
    private final MailLogService mailLogService;
    private final EmailTokenProvider emailTokenProvider;
    private final EmailTokenProvider logTokenProvider;

    /** *

     @param corpId        企业id
     @param emailSecret   通讯录的secret
     @param logSecret     日志的secret
     @throws Exception   获取token的网络错误 */
    public EmailClient(String corpId, String emailSecret, String logSecret) throws Exception {
        this.emailTokenProvider = new EmailTokenProvider(corpId, emailSecret, "ORG");
        this.logTokenProvider = new EmailTokenProvider(corpId, logSecret, "LOG");
        this.departmentService = new DepartmentService(emailTokenProvider);
        this.userService = new UserService(emailTokenProvider);
        this.mailLogService = new MailLogService(logTokenProvider);
        emailTokenProvider.fetchAccessToken();
    }

    public DepartmentService department() {
        return departmentService;
    }

    public UserService user() {
        return userService;
    }

    public MailLogService Log() {
        return mailLogService;
    }

}

