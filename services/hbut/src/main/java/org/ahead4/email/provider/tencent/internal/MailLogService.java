package org.ahead4.email.provider.tencent.internal;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.ahead4.email.operation.MailStats;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.support.RetrySynchronizationManager;
import org.springframework.retry.RetryContext;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Date;

@Retryable(
        value = TokenInvailidException.class,
        maxAttempts = 2,
        backoff = @Backoff(delay = 100)
)
public class MailLogService {

    @Setter
    private EmailTokenProvider accessTokenProvider;
    private final ApiClient apiClient;

    private static final String USER_MAIL_STATUS_API = "https://api.exmail.qq.com/cgi-bin/log/mail";
    private static final String MAIL_STATUS_API = "https://api.exmail.qq.com/cgi-bin/log/mailstatus";
    private static final String LOGIN_LOG_API = "https://api.exmail.qq.com/cgi-bin/log/login";

    public MailLogService(EmailTokenProvider logTokenProvider) {
        this.apiClient = new ApiClient();
        accessTokenProvider = logTokenProvider;
    }
    public MailStatus queryMailStatus(MailStatusQueryRequest request) throws Exception {
        boolean forceRefresh = false;
        RetryContext context = RetrySynchronizationManager.getContext();
        if (context != null && context.getRetryCount() > 0) {
            forceRefresh = true;
        }
        String url = MAIL_STATUS_API + "?access_token=" + accessTokenProvider.getAccessToken(forceRefresh);

        // Send POST request with the request object
        JsonNode response = apiClient.post(url, request);
        apiClient.checkResponse(response, "Query mail status");

        return apiClient.toObject(response.toString(), MailStatus.class);
    }
    public List<LoginLogEntry> queryLoginLogs( LoginLogQueryRequest request) throws Exception {
        boolean forceRefresh = false;
        RetryContext context = RetrySynchronizationManager.getContext();
        if (context != null && context.getRetryCount() > 0) {
            forceRefresh = true;
        }
        String url = LOGIN_LOG_API + "?access_token=" + accessTokenProvider.getAccessToken(forceRefresh);
        JsonNode response = apiClient.post(url, request);


        apiClient.checkResponse(response, "Query login logs");
        return apiClient.toList(response.path("list"), LoginLogEntry.class);
    }

    public UserMailLogResponse queryUserMailStatus(UserMailLogRequest request) throws Exception {
        boolean forceRefresh = false;
        RetryContext context = RetrySynchronizationManager.getContext();
        if (context != null && context.getRetryCount() > 0) {
            forceRefresh = true;
        }
        String url = USER_MAIL_STATUS_API + "?access_token=" + accessTokenProvider.getAccessToken(forceRefresh);

        // only support sent
        request.setMailType(1);
        // Send POST request with the request object
        JsonNode response = apiClient.post(url, request);
        apiClient.checkResponse(response, "Query mail status");
        return apiClient.toObject(response.toString(), UserMailLogResponse.class);
    }

    @Data
    public static class LoginLogQueryRequest {
        private String userid;
        private String begin_date;
        private String end_date;
    }

    @Data
    public static class LoginLogEntry {
        private Long time;
        private String ip;
        private int type;
    }

    @Data
    public static class UserMailLogRequest {

        @JsonProperty("begin_date")
        private String beginDate;
        @JsonProperty("end_date")
        private String endDate;
        @JsonProperty("mailtype")
        private int mailType;
        @JsonProperty("userid")
        private String userId;
    }

    @Data
    public static class MailEntry{
        @JsonProperty("mailtype")
        private int mailType;
        @JsonProperty("time")
        private Long time;
    }

    @Data
    public static class UserMailLogResponse{
        private int errcode;
        @JsonProperty("errmsg")
        private String errmsg;
        @JsonProperty("list")
        private List<MailEntry> list;
    }

    @Data
    public static class MailStatusQueryRequest {

        @JsonProperty("domain")
        private String domain;

        @JsonProperty("begin_date")
        private String beginDate;

        @JsonProperty("end_date")
        private String endDate;

        // Constructor
        public MailStatusQueryRequest(String domain, Date begin, Date end) {
            this.domain = domain;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            this.beginDate = sdf.format(begin);
            this.endDate = sdf.format(end);
        }
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MailStatus {
        private int sendsum;
        private int recvsum;

        public MailStats toStats() {
            return new MailStats(sendsum, recvsum);
        }
    }

}
