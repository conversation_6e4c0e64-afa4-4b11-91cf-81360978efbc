package org.ahead4.email.provider.tencent.internal;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Setter;
import org.springframework.retry.RetryContext;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.support.RetrySynchronizationManager;

import java.util.List;



public class DepartmentService {
    private static final String LIST_API = "https://api.exmail.qq.com/cgi-bin/department/list";

    @Setter
    private EmailTokenProvider accessTokenProvider;
    private final ApiClient apiClient;

    public DepartmentService(EmailTokenProvider emailTokenProvider) {
        this.apiClient = new ApiClient();
        accessTokenProvider = emailTokenProvider;
    }

    @Retryable(
            value = TokenInvailidException.class,
            maxAttempts = 2,
            backoff = @Backoff(delay = 100)
    )
    public List<DepartmentDTO> list(long parentId) throws Exception {
        boolean forceRefresh = false;
        RetryContext context = RetrySynchronizationManager.getContext();
        if (context != null && context.getRetryCount() > 0) {
            forceRefresh = true;
        }
        StringBuilder url = new StringBuilder(LIST_API)
                .append("?access_token=").append(accessTokenProvider.getAccessToken(forceRefresh)).append("&id=").append(parentId);

        JsonNode response = apiClient.get(url.toString());

        apiClient.checkResponse(response, "List departments");
        return apiClient.toList(response.path("department"), DepartmentDTO.class);
    }


}
