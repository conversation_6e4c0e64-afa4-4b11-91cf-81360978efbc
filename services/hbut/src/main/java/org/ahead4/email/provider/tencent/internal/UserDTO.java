package org.ahead4.email.provider.tencent.internal;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.ahead4.email.operation.UserEmailAccount;

import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
public class UserDTO {
    private String userid;
    private String name;
    private List<Long> department;
    private String position;
    private String mobile;
    private String tel;
    private String extid;
    private String gender;
    private List<String> slaves;
    private String password;
    private Integer cpwd_login;
    private Integer setvip;
    private Integer enable;
    private String active_code;


    public UserDTO(UserEmailAccount emailAccount) {
        this.userid = emailAccount.getUserId();
        this.name = emailAccount.getDisplayName();
        if (emailAccount.getDepartmentId() != null) {
            this.department =  Collections.singletonList(emailAccount.getDepartmentId());
        }
        this.mobile = emailAccount.getMobile();
        this.enable = emailAccount.getEnable();
        this.gender = emailAccount.getGender();
        this.password = emailAccount.getPassword();
        if (emailAccount.getAlias() != null) {
            this.slaves =  Collections.singletonList(emailAccount.getAlias());
        }
    }

    public UserEmailAccount toUserEmailAccount() {
        UserEmailAccount emailAccount = new UserEmailAccount();
        emailAccount.setUserId(this.userid);
        emailAccount.setDisplayName(this.name);
        emailAccount.setDepartmentId((this.department != null && !this.department.isEmpty()) ? this.department.get(0) : null);
        emailAccount.setMobile(this.mobile);
        emailAccount.setPassword(this.password);
        emailAccount.setEnable(this.enable);

        if (this.slaves != null && !this.slaves.isEmpty()) {
            emailAccount.setAlias(this.slaves.get(0));
        }
        return emailAccount;
    }

}
