package org.ahead4.email.provider.tencent.internal;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class ErrorCodeException extends Exception{
    private int code = 0;
    public static final Map<Integer, String> USER_AWARE_ERRORS;

    static {
        Map<Integer, String> map = new HashMap<>();
        map.put(60120, "密码和手机号不能同时为空");
        map.put(60128, "账户已绑定手机或微信，需员工修改密码");

        USER_AWARE_ERRORS = Collections.unmodifiableMap(map);
    }

    public ErrorCodeException(int code) {
        super(USER_AWARE_ERRORS.containsKey(code)?USER_AWARE_ERRORS.get(code):"错误代码： " + code);
        this.code = code;
    }
    public int getErrorCode() {
        return code;
    }
}

