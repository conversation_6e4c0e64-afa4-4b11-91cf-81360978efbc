package org.ahead4.email.provider.tencent.internal;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Setter;
import org.springframework.retry.RetryContext;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.support.RetrySynchronizationManager;

import java.util.List;

@Retryable(
        value = { TokenInvailidException.class },
        maxAttempts = 3,
        backoff = @Backoff(delay = 100)
)
public class UserService {
    private static final String CREATE_USER_API = "https://api.exmail.qq.com/cgi-bin/user/create";
    private static final String UPDATE_USER_API = "https://api.exmail.qq.com/cgi-bin/user/update";
    private static final String DELETE_USER_API = "https://api.exmail.qq.com/cgi-bin/user/delete";
    private static final String GET_USER_API = "https://api.exmail.qq.com/cgi-bin/user/get";
    private static final String LIST_USER_API = "https://api.exmail.qq.com/cgi-bin/user/list";

    @Setter
    private EmailTokenProvider accessTokenProvider;
    private final ApiClient apiClient;

    public UserService(EmailTokenProvider emailTokenProvider) {
        this.apiClient = new ApiClient();
        accessTokenProvider = emailTokenProvider;
    }

    public void createUser(UserDTO user) throws Exception {
        boolean forceRefresh = false;
        RetryContext context = RetrySynchronizationManager.getContext();
        if (context != null && context.getRetryCount() > 0) {
            forceRefresh = true;
        }
        String url = CREATE_USER_API + "?access_token=" + accessTokenProvider.getAccessToken(forceRefresh);
        JsonNode response = apiClient.post(url, user);

        apiClient.checkResponse(response, "Create user");
    }

    public void updateUser(UserDTO user) throws Exception {
        boolean forceRefresh = false;
        RetryContext context = RetrySynchronizationManager.getContext();
        if (context != null && context.getRetryCount() > 0) {
            forceRefresh = true;
        }
        String url = UPDATE_USER_API + "?access_token=" + accessTokenProvider.getAccessToken(forceRefresh);
        JsonNode response = apiClient.post(url, user);

        apiClient.checkResponse(response, "Update user");
    }

    public void deleteUser(String userId) throws Exception {
        boolean forceRefresh = false;
        RetryContext context = RetrySynchronizationManager.getContext();
        if (context != null && context.getRetryCount() > 0) {
            forceRefresh = true;
        }
        String url = DELETE_USER_API + "?access_token=" + accessTokenProvider.getAccessToken(forceRefresh) + "&userid=" + userId;;
        JsonNode response = apiClient.get(url);

        apiClient.checkResponse(response, "Delete user");
    }

    public UserDTO getUser(String userId) throws Exception {
        boolean forceRefresh = false;
        RetryContext context = RetrySynchronizationManager.getContext();
        if (context != null && context.getRetryCount() > 0) {
            forceRefresh = true;
        }
        String url = GET_USER_API + "?access_token=" + accessTokenProvider.getAccessToken(forceRefresh) + "&userid=" + userId;
        JsonNode response = apiClient.get(url);

        int errcode = response.path("errcode").asInt();
        if (errcode != 0) {
            if (errcode == 40001 || errcode == 40014) {
                throw new TokenInvailidException();
            }
            // user not found is not an exception
            if (errcode == 60111) {
                return null;
            }
            throw new Exception("Get user" + " failed: " + response.path("errmsg").asText());
        }

        return apiClient.toObject(response.toString(), UserDTO.class);
    }

    public List<UserDTO> listUsersByDepartment(long departmentId, boolean fetchChild) throws Exception {
        boolean forceRefresh = false;
        RetryContext context = RetrySynchronizationManager.getContext();
        if (context != null && context.getRetryCount() > 0) {
            forceRefresh = true;
        }
        String url = LIST_USER_API + "?access_token=" + accessTokenProvider.getAccessToken(forceRefresh) +
                  "&department_id=" + departmentId + "&fetch_child=" + (fetchChild ? 1 : 0);

        JsonNode response = apiClient.get(url);
        apiClient.checkResponse(response, "List users by department");

        return apiClient.toList(response.path("userlist"), UserDTO.class);
    }

}
