package org.ahead4.email.provider.tencent;

import lombok.extern.slf4j.Slf4j;
import org.ahead4.email.operation.EmailAccountOp;
import org.ahead4.email.operation.OperationException;
import org.ahead4.email.operation.UserEmailAccount;
import org.ahead4.email.operation.UserMailStats;
import org.ahead4.email.provider.tencent.internal.EmailClient;
import org.ahead4.email.provider.tencent.internal.ErrorCodeException;
import org.ahead4.email.provider.tencent.internal.LoginAuditTask;
import org.ahead4.email.provider.tencent.internal.UserDTO;


/**
 * Implementation of {@link EmailAccountOp} for Tencent Cloud Email Service.
 *
 * NOTE 基于email统计没有直接的接口，通过{@link LoginAuditTask}进行统计后入库
 * <AUTHOR>
@Slf4j
public class TencentEmailAccountOp implements EmailAccountOp {
    private EmailClient client;
    public TencentEmailAccountOp(EmailClient client) {
        this.client = client;
    }
    @Override
    public void createEmailAccount(UserEmailAccount account) throws OperationException {
        UserDTO user = new UserDTO(account);
        try {
            this.client.user().createUser(user);
        } catch (Exception e) {
            throw new OperationException("Create user failed for user: " + user.getName(),e);
        }
    }

    @Override
    public void changePassword(String userId, String password) throws OperationException, ErrorCodeException {
        try {
            UserDTO user = this.client.user().getUser(userId);
            if (null != user) {
                UserDTO newUser = new UserDTO();
                newUser.setPassword(password);
                newUser.setUserid(userId);
                this.client.user().updateUser(newUser);
            } else {
                throw new OperationException("changePassword failed, email doesnt exists: " + userId);
            }
        } catch (ErrorCodeException e) {
            throw e;
        } catch (Exception e) {
            throw new OperationException("changePassword failed for user id: " + userId,e);
        }
    }

    @Override
    public void deleteEmailAccount(String userId) throws OperationException {
        try {
            UserDTO user = this.client.user().getUser(userId);
            if (null != user) {
                this.client.user().deleteUser(userId);
            }
        } catch (Exception e) {
            throw new OperationException("deleteUser failed for user id: " + userId,e);
        }
    }
    @Override
    public void updateEmailAccount(UserEmailAccount userEmailAccount) throws OperationException {
        try {
            UserDTO userDTO = new UserDTO(userEmailAccount);
            UserDTO user = this.client.user().getUser(userEmailAccount.getUserId());
            if (null != user) {
                userDTO.setPassword(null); // 不更新密码
                this.client.user().updateUser(userDTO);
            }
        } catch (Exception e) {
            throw new OperationException("update email failed for user id: " + userEmailAccount.getUserId(),e);
        }
    }

    @Override
    public UserEmailAccount getEmailAccount(String userId) throws OperationException {
        try {
            UserDTO user = this.client.user().getUser(userId);
            if (null != user) {
                return user.toUserEmailAccount();
            }
        } catch (Exception e) {
            throw new OperationException(" getEmailAccount failed for user id: " + userId,e);
        }
        return null;
    }


    /**
     *  基于{@link LoginAuditTask}, 这里查询数据库
     *
     */
    @Override
    public UserMailStats getEmailAccountStats(String username) throws OperationException {
        throw new OperationException("还没有实现");
    }
}
