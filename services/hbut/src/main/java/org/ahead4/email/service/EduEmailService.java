package org.ahead4.email.service;

import org.ahead4.email.entity.EduEmail;
import com.baomidou.mybatisplus.extension.service.IService;
import org.ahead4.email.entity.EduEmailDto;

/**
 * <p>
 * 教育邮箱用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
public interface EduEmailService extends IService<EduEmail> {


    boolean checkStudent(EduEmailDto param, StringBuilder errorMsg);

    boolean checkEmailExist(String email);

    boolean updateUserStatus(String userId, int status);

    void deleteUserEmailAccount(String id);

    String signup(EduEmail eduEmail, String password, boolean isStudent);

    String changePassword(String id, String email, String password);

    void startSyncData();

    EduEmail getWithUpdateStats(String userId);
}
