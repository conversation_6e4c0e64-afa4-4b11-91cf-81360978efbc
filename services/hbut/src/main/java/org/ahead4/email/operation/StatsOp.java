package org.ahead4.email.operation;

import org.ahead4.email.provider.tencent.internal.MailLogService;
import org.ahead4.email.provider.tencent.internal.UserDTO;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface StatsOp {

    Map<String, MailStats> getEmailStats(Date startDate, Date endDate);

    MailLogService.LoginLogEntry getLastLoginLog(LocalDate start, LocalDate end, String userid);

    MailLogService.MailEntry getUserMailStats(LocalDate start, LocalDate end, String userid);
}
