package org.ahead4.email.provider.tencent.internal;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;

import static org.ahead4.email.provider.tencent.internal.ErrorCodeException.USER_AWARE_ERRORS;

@Slf4j
public class ApiClient {
    private final ObjectMapper objectMapper ;

    public ApiClient() {
        this.objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    public JsonNode get(String url) throws IOException {
        HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
        conn.setRequestMethod("GET");

        try (InputStream inputStream = conn.getInputStream()) {
            return objectMapper.readTree(inputStream);
        }
    }

    public JsonNode post(String url, Object requestBody) throws IOException {
        HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
        conn.setRequestMethod("POST");
        conn.setDoOutput(true);
        conn.setRequestProperty("Content-Type", "application/json; charset=UTF-8");

        String json = objectMapper.writeValueAsString(requestBody);
        try (OutputStream os = conn.getOutputStream()) {
            os.write(json.getBytes("UTF-8"));
        }

        try (InputStream inputStream = conn.getInputStream()) {
            JsonNode result =  objectMapper.readTree(inputStream);

            log.debug("Url {} with body: {}", url, json);
            log.debug("Url {} with result: {}", url, result.toString());
            return result;
        }
    }

    public <T> T toObject(String node, Class<T> clazz) throws IOException {
        return objectMapper.readValue(node, clazz);
    }

    public <T> List<T> toList(JsonNode arrayNode, Class<T> clazz) throws IOException {
        return objectMapper.readerForListOf(clazz).readValue(arrayNode);
    }

    public void checkResponse(JsonNode response, String function) throws Exception {

        int errcode = response.path("errcode").asInt();
        if (errcode != 0) {
            if (errcode == 40001 || errcode == 40014) {
                throw new TokenInvailidException();
            } else if ( USER_AWARE_ERRORS.containsKey(errcode)) {
                throw new ErrorCodeException(errcode);
            }
            throw new Exception(function + " failed: " + response.path("errmsg").asText());
        }
    }
}
