package org.ahead4.hbut.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.common.service.CacheService;
import org.ahead4.common.utils.IdentitiesUtils;
import org.ahead4.hbut.dto.DockingStaff;
import org.ahead4.hbut.dto.DockingStudent;
import org.ahead4.hbut.service.DockingService;
import org.ahead4.hbut.service.SyncService;
import org.ahead4.logger.dto.ParamDto;
import org.ahead4.logger.service.entity.OperLog;
import org.ahead4.permission.entity.User;
import org.ahead4.permission.service.UserService;
import org.ahead4.security.dto.IUserDetail;
import org.ahead4.security.utils.AccessHolder;
import org.apache.commons.lang3.StringUtils;
import org.jasig.cas.client.validation.Assertion;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Arrays;
import java.util.Map;

import static org.ahead4.hbut.DockingUtil.buildUser;


/**
 * <AUTHOR>
 * <p>
 * 登录服务
 */

@Controller
@RequestMapping("cas-auth")
@Api(value = "湖工CAS登录服务API", tags = {"登录服务"})
@Slf4j
public class HbutCASAuthController {
    @Autowired
    private UserService userService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    DockingService dockingService;
    @Autowired
    SyncService syncService;
    @GetMapping("/login")
    public String callback(HttpServletResponse res, ModelMap modelMap) {
        try {
            AccessHolder.token(AccessHolder.token());
            modelMap.put("access_token", AccessHolder.token());
            modelMap.put("username", AccessHolder.username());
            return "login/success.html";
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            AccessHolder.clean();
        }
        return "redirect:../../#/403";
    }

    @Value("${allow.sso.cas:false}")
    boolean allowSSObyCAS;
    @GetMapping("/login/{sysType}")
    public String callback(HttpServletRequest request, @PathVariable("sysType") String sysType, HttpServletResponse response, ModelMap modelMap){
        try {
            log.info("CAS 登录类型 " + sysType);
            // 获取租户信息
            Assertion assertion =  (Assertion)request.getSession().getAttribute("_const_cas_assertion_");
            log.info("CAS 登录用户 " + JSON.toJSONString(assertion));
            String username = assertion.getPrincipal().getName();
            IUserDetail user;
            if (StringUtils.equals(sysType, "doc")) {
                // 检查是否位干部
                user = userService.findByUsername(username);
            } else if (StringUtils.equals(sysType, "email")) {
                // 检查是否是学生
                DockingStudent student = dockingService.getStudent(username);
                log.info("对接接口查询学生信息：" + student);
                if(student != null) {
                    // 使学生创建临时账号
                    user = new IUserDetail();
//                    user.setId(student.getString("XH"));
                    user.setUsername(username);
                    user.setDisplayname(student.getName());
                    user.setMobile(student.getPhone());
                    user.setId(student.getIdCardNo());
                    user.addAuthorities("ROLE_Student");
                    user.setRoles(Arrays.asList(new IUserDetail.NameCodeDto("Student", "学生")));
                } else {
                    // 检查如果是职工
                    user = userService.findByUsername(username);
                    DockingStaff employee = dockingService.getStaff(username);
                    if (user == null) {
                        log.info("未找到本地信息，尝试同步教职工信息： {}", username);
                        if (employee != null) {
                            syncService.syncUser(employee);
                            log.info("同步完成，再次获取：{}", username);
                            user = userService.findByUsername(username);
                        }
                    } else {
                        if (employee != null) {
                            User temp = buildUser(employee);
                            if (null != temp && temp.getMobile() != null &&  !temp.getMobile().equals(user.getMobile())) {
                                user.setMobile(temp.getMobile());

                                // 只需要更新电话，所以源用历史数据库的
                                BeanUtils.copyProperties(user, temp);

                                //邮箱系统的手机号，在调用/me的时候更新，这里就不操作了，保持跟学生统一
                                userService.saveOrUpdate(temp);
                            }
                        } else {
                            log.info("Warning: No staff in docking service: {}, but it exists in email user system", username);
                        }
                    }
                }
            } else if (StringUtils.equals(sysType, "email-manage")) {
                user = userService.findByUsername(username);
                // 如果不是管理员或者超级管理员就无法登录
                if (!user.auth(Arrays.asList("ROLE_EduEmailAdmin", "ROLE_admin"))) {
                    log.info("未找到职工信息，清理账号信息");
                    user = null;
                };
            } else {
                log.info(username+"登录失败，重定向到403");
                return "redirect:../../../doc/#/403";
            }

            if (user == null || StringUtils.isEmpty(username) || !allowSSObyCAS) {
                OperLog.actionPush(username,
                        "登录",
                        ParamDto.args(username),
                        "用户登录失败，无效用户或不允许使用单点登录",
                        "失败", null);
                log.info(username+"用户登录失败，无效用户或不允许使用单点登录");
                return "redirect:../../../doc/#/403";
            }
//            if (!user.getIsActive()) {
//                OperLog.actionPush(username,
//                        "登录",
//                        ParamDto.args(username),
//                        "用户登录失败，用户账户禁用！",
//                        "失败", null);
//                log.info(username+"用户登录失败，用户账户禁用！");
//                return "redirect:../../../doc/#/403";
//            }
            String token = IdentitiesUtils.uuid();
            AccessHolder.token(token);
            AccessHolder.user(user);


            log.info(username+"用户使用CAS登录成功");
            OperLog.actionPush(username,
                    "登录成功",
                    ParamDto.args(username),
                    "使用CAS单点登录成功",
                    "成功", null);
            modelMap.put("access_token", token);
            modelMap.put("username", username);
            return "redirect:../../../"+sysType;

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            AccessHolder.clean();
        }
        return "redirect:../../../doc/#/403";
    }
    @GetMapping("/logout")
    public void logout(HttpServletRequest request,  HttpServletResponse response) throws IOException{
        try {
            String username = AccessHolder.username();


            OperLog.actionPush(username,
                    "用户退出",
                    ParamDto.args(username),
                    "使用CAS退出成功",
                    "成功", null);
        }catch (Exception e){}
        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                session.invalidate();
            }
            AccessHolder.logout(false);

            response.sendRedirect("https://auth.hbut.edu.cn/authserver/logout");

        } finally {
            AccessHolder.clean();
        }
    }

}
