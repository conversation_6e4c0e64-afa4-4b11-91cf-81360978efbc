package org.ahead4.hbut.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import groovy.util.logging.Slf4j;
import org.ahead4.hbut.dto.DockingDept;
import org.ahead4.hbut.dto.DockingStaff;
import org.ahead4.hbut.dto.DockingStudent;
import org.ahead4.hbut.service.DockingService;
import org.ahead4.permission.entity.*;
import org.ahead4.permission.service.*;
import org.ahead4.web.presentate.HttpMsg;
import org.ahead4.workflow.utils.StringUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.IOException;
import java.util.*;

@Service
@RestController
@RequestMapping("sync-service")
public class SyncServiceController {
    @Autowired
    UserService userService;
    @Autowired
    DeptService deptService;
    @Autowired
    UserRoleService userRoleService;
    @Autowired
    UserDeptService userDeptService;
    @Autowired
    UserPositionService userPositionService;

    @Autowired
    DockingService dockingService;
    @RequestMapping("sync-staff")
    HttpMsg syncStaff () throws IOException {

//        JSONArray staffList = JSONArray.parseArray(FileUtils.readFileToString(new File("./StaffList.20250518.json"), "utf-8"));
//        JSONArray deptList = JSONArray.parseArray(FileUtils.readFileToString(new File("./DeptList.20250518.json"), "utf-8"));
//        JSONArray studentList = JSONArray.parseArray(FileUtils.readFileToString(new File("./StudentList.20250518.json"), "utf-8"));
        List<DockingStaff> staffList = dockingService.getStaffList();
        List<DockingDept>  deptList = dockingService.getDeptList();
        List<User> users = new ArrayList<>();
        List<Dept> depts = new ArrayList<>();
        List<UserPosition> postions = new ArrayList<>();
        List<UserDept> userDepts = new ArrayList<>();
        // 用户可用角色： 校办（SOA）  审批人员（Approver） 学生（Student）
        List<UserRole> userRoles = new ArrayList<>();

        // 1、遍历部门。创建部门对象
        for (int i = 0; i < deptList.size(); i++) {

//            "系统ID": "1346",
//            "上级机构名称": "机关单位",
//            "机构名称": "人才交流中心",
//            "是否撤销": "0",
//            "部门分类": "A类",
//            "机构编号": "10127",
//            "上级机构编号": "101"
            DockingDept dockingDept = deptList.get(i);
            String 系统ID = dockingDept.getCode();
            String 机构名称 = dockingDept.getName();
            String 机构编号 = dockingDept.getCode();
            String 上级机构编号 = dockingDept.getPcode();
            Dept dept = new Dept();
            dept.setCode(机构编号);
            dept.setName(机构名称);
            dept.setId(系统ID);
            dept.setPcode(上级机构编号);
            if(StringUtils.equalsAny(机构编号, "1"))
                dept.setPcode("root");
            dept.setCreator("docking");
            dept.setUpdator("docking");
            dept.setCreateTime(new Date());
            dept.setUpdateTime(new Date());
            depts.add(dept);
        }
        // 遍历用户。创建用户对象、创建职务对象、创建用户部门关联对象、创建用户角色绑定对象
        for (int i = 0; i < staffList.size(); i++) {

//            "姓名": "冯昭昭",
//            "工作证号": "19970009",
//            "部门职务": "部门正职",
//            "手机号码": "15827279019",
//            "机构名称": "党委宣传部",
//            "用户职级": "管理五级",
//            "机构编号": "10104"
            DockingStaff staff = staffList.get(i);
            String 姓名 = staff.getName();
            String 工作证号 = staff.getCode();
            String 部门职务 = staff.getPosition(); // null, 部门副职, 主持工作, 部门正职, 分管领导
            String 手机号码 = staff.getPhone();
            String 用户职级 = staff.getLevel();
            String 机构编号 = staff.getDeptCode();
            String 机构名称 = staff.getDeptName();
            if (工作证号 == null) continue;
            User user = new User();
            user.setId(工作证号);
            user.setDisplayname(姓名);
            user.setUsername(工作证号);
            user.setMobile(手机号码);
            user.setJobGrade(用户职级);
            user.setCreator("docking");
            user.setUpdator("docking");
            user.setCreateTime(new Date());
            user.setUpdateTime(new Date());
            if (StringUtils.isNotEmpty(部门职务)) user.setIsActive(true);
            users.add(user);
            if (StringUtils.isNotEmpty(部门职务)) {
                UserPosition position = new UserPosition();
                position.setId(机构名称+部门职务);
                position.setDeptCode(机构编号);
                switch (部门职务) {
                    case "部门副职":
                        position.setPositionCode("DeputyHead");
                        break;
                    case "部门正职":
                    case "主持工作":
                        position.setPositionCode("HeadOfDepartment");
                        break;
                    case "分管领导":
                        position.setPositionCode("VicePresident");
                        break;
                    default:
                        position.setPositionCode(部门职务);

                }
                // Z:主职（部门正职）; F:分管职（分管领导）; J:兼职（主持工作）;
                if (StringUtils.equalsAny(部门职务,"部门正职")) position.setPositionType("Z");
                if (StringUtils.equalsAny(部门职务,"分管领导")) position.setPositionType("F");
                if (StringUtils.equalsAny(部门职务,"主持工作")) position.setPositionType("J");
                position.setUsername(工作证号);
                position.setCreator("docking");
                position.setUpdator("docking");
                position.setCreateTime(new Date());
                position.setUpdateTime(new Date());
                postions.add(position);
            }
            UserDept userDept = new UserDept();
            userDept.setDeptCode(机构编号);
            userDept.setUsername(工作证号);
            userDept.setCreator("docking");
            userDept.setUpdator("docking");
            userDept.setCreateTime(new Date());
            userDept.setUpdateTime(new Date());
            userDepts.add(userDept);
            UserRole userRole = new UserRole();
            userRole.setRoleCode("user");
            // 用户可用角色： 校办（SOA）  审批人员（Approver） 用户（user）
            if (StringUtils.equalsAny(部门职务, "部门正职", "分管领导", "主持工作")) {
                userRole.setRoleCode("Approver"); // 审核人
                if (StringUtils.equalsAny(机构编号, "10101")) {
                    userRole.setRoleCode("SOA"); // 校办审核人特殊处理
                }
            }
            userRole.setUsername(工作证号);
            userRole.setCreator("docking");
            userRole.setUpdator("docking");
            userRole.setCreateTime(new Date());
            userRole.setUpdateTime(new Date());
            if (StringUtils.isNotEmpty(工作证号))
                userRoles.add(userRole);
        }
        userService.saveOrUpdateBatch(users);
        deptService.saveOrUpdateBatch(depts);
        userDeptService.saveOrUpdateBatch(userDepts);
        userPositionService.saveOrUpdateBatch(postions);
        userRoleService.saveOrUpdateBatch(userRoles);
        // 2、遍历用户。创建用户对象、创建职务对象、创建用户部门关联对象、创建用户角色绑定对象
        return HttpMsg.ok();
    }

//    @RequestMapping("sync-student")
    HttpMsg syncStudent () throws IOException {

        List<DockingStudent> studentList = dockingService.getStudentList();
        List<User> users = new ArrayList<>();
        List<Dept> depts = new ArrayList<>();
        List<UserDept> userDepts = new ArrayList<>();
        // 用户可用角色： 校办（SOA）  审批人员（Approver） 学生（Student）
        List<UserRole> userRoles = new ArrayList<>();

        // 2、遍历用户。创建用户对象、创建职务对象、创建用户部门关联对象、创建用户角色绑定对象
        for (int i = 0; i < studentList.size(); i++) {

            DockingStudent student = studentList.get(i);
            String 姓名 = student.getName();
            String 工作证号 = student.getCode();
            String 手机号码 = student.getPhone();
            String 身份证号 = student.getIdCardNo();
            if (工作证号 == null) continue;
            User user = new User();
            user.setId(身份证号);
            user.setDisplayname(姓名);
            user.setUsername(工作证号);
            user.setMobile(手机号码);
            user.setCreator("docking");
            user.setUpdator("docking");
            user.setCreateTime(new Date());
            user.setUpdateTime(new Date());
            users.add(user);

            UserRole userRole = new UserRole();
            userRole.setRoleCode("Student");
            userRole.setUsername(工作证号);
            userRole.setCreator("docking");
            userRole.setUpdator("docking");
            userRole.setCreateTime(new Date());
            userRole.setUpdateTime(new Date());
            if (StringUtils.isNotEmpty(工作证号))
                userRoles.add(userRole);
        }
//        userService.saveOrUpdateBatch(users);
        deptService.saveOrUpdateBatch(depts);
        userDeptService.saveOrUpdateBatch(userDepts);
        userRoleService.saveOrUpdateBatch(userRoles);
        // 2、遍历用户。创建用户对象、创建职务对象、创建用户部门关联对象、创建用户角色绑定对象
        return HttpMsg.ok();
    }
}
