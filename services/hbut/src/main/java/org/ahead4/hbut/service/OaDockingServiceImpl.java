package org.ahead4.hbut.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.ahead4.hbut.dto.DockingDept;
import org.ahead4.hbut.dto.DockingStaff;
import org.ahead4.hbut.dto.DockingStudent;
import org.ahead4.workflow.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 和学校办公系统（Office Automation）对接用的服务层
 */
@Slf4j
//@Service
public class OaDockingServiceImpl implements DockingService {


    public static void main(String[] args) throws InterruptedException {
        OaDockingServiceImpl service = new OaDockingServiceImpl();
        service.appid = System.getenv("HBUT_DOCKING_OA_APPID");
        service.secret = System.getenv("HBUT_DOCKING_OA_SECRET");
        service.baseUrl = System.getenv("HBUT_DOCKING_OA_BASE_URL");
        List<DockingStaff> list = service.getStaffList();
//        log.info(list + "");
//        for (int i = 0; i < list.size(); i++) {
//            if (StringUtils.equals(list.getJSONObject(i).getString("工作证号"), "test2025jzg"))
//                log.info(list.getJSONObject(i) + "");
//
//        }
//        Thread.sleep(100);
        log.info(service.getStudent("1910651222")+"");
        log.info(service.getStaff("19990010")+"");
//        log.info(service.getDeptList() + "");
    }

    public static final OkHttpClient okHttpClient = new OkHttpClient.Builder()
            .connectTimeout(8000, TimeUnit.MILLISECONDS)
            .readTimeout(8000, TimeUnit.MILLISECONDS)
            .build();

    @Value("${hbut.docking.oa.base-url:}")
    public String baseUrl;
    @Value("${hbut.docking.oa.appid:}")
    public String appid;
    @Value("${hbut.docking.oa.secret:}")
    public String secret;
    /**
     * 获取职工信息的接口地址
     */
    public final static String GET_STAFF_PATH = "/cdsp/data-api/v2/ry2025002";
    public final static String GET_STUDENT_PATH = "/cdsp/data-api/v2/ry2025001";
    public final static String GET_DEPT_PATH = "/cdsp/data-api/v2/dw2025001";
    public String generateSignature(String timestamp) {
        try {
            // 1. 拼接字符串
            String concatenatedString = appid + secret + timestamp;

            // 2. 计算MD5哈希
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] md5Bytes = md.digest(concatenatedString.getBytes());
            // 将字节数组转换为32位小写字符串
            BigInteger bigInt = new BigInteger(1, md5Bytes);
            String md5Hex = bigInt.toString(16);
            // 3. 进行Base64编码
            byte[] base64Bytes = Base64.getEncoder().encode(md5Hex.getBytes());

            return new String(base64Bytes);
        } catch (NoSuchAlgorithmException e) {
            // MD5算法不可用时抛出异常
            throw new RuntimeException("MD5 algorithm not available", e);
        }
    }

    Request cmmonReq(String json, String  path) {
        try {
            Thread.sleep(100); //
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        String timestamp = System.currentTimeMillis() + 5000+ "";
        log.info(baseUrl+path+"::"+json);
        Request.Builder builder = new Request.Builder().url(baseUrl+path)
                .addHeader("appId", appid)
                .addHeader("timestamp", timestamp)
                .addHeader("sign", generateSignature(timestamp))
                .addHeader("Host", "cube.hbut.edu.cn")
                .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json));
        return builder.build();
    }
    JSONArray execAllListReq(String path) {
        JSONArray result = new JSONArray();
        for (int $skip = 0; $skip < 10000; $skip++) {
            JSONObject params = new JSONObject();
            params.put("$count", "true");
            params.put("$top", 1000);
            params.put("$skip", $skip * 1000);
            try (Response response = okHttpClient
                    .newCall(cmmonReq(params.toJSONString(), path))
                    .execute()) {
                if (response.body() != null) {
                    JSONObject json = JSON.parseObject(response.body().string());
                    if (StringUtils.equalsIgnoreCase(json.getString("errcode"), "999")) {
                        throw new RuntimeException(json.toJSONString());
                    }
                    if (json.getJSONObject("error") != null) {
                        throw new RuntimeException(json.getJSONObject("error").toJSONString());
                    }
                    JSONArray arr = json.getJSONArray("value");
                    if (arr != null)
                        result.addAll(arr);
                    Integer count = json.getInteger("@odata.count");
                    if (json.getInteger("@odata.count") == null || result.size()>=count) {
                        break;
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }
        return result;

    }
    /**
     *
     */
    @Override
    public List<DockingDept> getDeptList() {
        JSONArray arr = execAllListReq(GET_DEPT_PATH);
        log.info(arr.toJSONString());
        return arr.stream().map(el -> json2Dept((JSONObject) el)).collect(Collectors.toList());
    }
    /**
     *
     */
    @Override
    public List<DockingStaff> getStaffList() {
        JSONArray arr =  execAllListReq(GET_STAFF_PATH);
        log.info(arr.toJSONString());
        return arr.stream().map(el -> json2Staff((JSONObject) el)).collect(Collectors.toList());
    }

    /**
     * 获取
     */
    @Override
    public List<DockingStudent> getStudentList() {
        JSONArray arr =  execAllListReq(GET_STUDENT_PATH);
        log.info(arr.toJSONString());
        return arr.stream().map(el -> json2Student((JSONObject) el)).collect(Collectors.toList());
    }
    JSONObject execOneReq(JSONObject params, String path) {

        params.put("$count",true);
        params.put("$top","1");
        try (Response response = okHttpClient
                .newCall(cmmonReq(params.toJSONString(), path))
                .execute()) {
            if (response.body() != null) {
                String jsonStr = response.body().string();
                log.info(jsonStr);
                JSONObject json = JSON.parseObject(jsonStr);
                if (StringUtils.equalsIgnoreCase(json.getString("errcode"), "999")) {
                    throw new RuntimeException(json.toJSONString());
                }
                if (json.getJSONObject("error") != null) {
                    throw new RuntimeException(json.getJSONObject("error").toJSONString());
                }
                if (json.getJSONArray("value").isEmpty()) return null;
                JSONObject obj = json.getJSONArray("value").getJSONObject(0);
                log.info(obj.toJSONString());
                return obj;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }
    /**
     * 根据id查询教职工
     * @return
     */
    @Override
    public DockingStaff getStaff(String id) {
        JSONObject params = new JSONObject();
        params.put("$filter", "工作证号 eq '"+id+"'");
//        params.put("$beauty", true);
        return json2Staff(execOneReq(params, GET_STAFF_PATH));
    }

    /**
     * 根据id查询学生
     * @return
     */
    @Override
    public DockingStudent getStudent(String id) {
        JSONObject params = new JSONObject();

        params.put("$filter","XH eq '"+id+"'");
        return json2Student(execOneReq(params, GET_STUDENT_PATH));
    }



    DockingStaff json2Staff(JSONObject jsonObject) {
        return DockingStaff.builder()
                .name(jsonObject.getString("姓名"))
                .code(jsonObject.getString("工作证号"))
                .phone(jsonObject.getString("手机号码"))
                .position(jsonObject.getString("部门职务"))
                .deptName(jsonObject.getString("机构名称"))
                .deptCode(jsonObject.getString("机构编号"))
                .level(jsonObject.getString("用户职级"))
                .build();
    }
    DockingStudent json2Student(JSONObject jsonObject) {
        return DockingStudent.builder()
                .name(jsonObject.getString("XM"))
                .code(jsonObject.getString("XH"))
                .idCardNo(jsonObject.getString("SFZJH"))
                .phone(jsonObject.getString("LXDH"))
                .build();
    }
    DockingDept json2Dept(JSONObject jsonObject) {
        return DockingDept.builder()
                .name(jsonObject.getString("机构名称"))
                .code(jsonObject.getString("机构编号"))
                .pcode(jsonObject.getString("上级机构编号"))
                .build();
    }
}
