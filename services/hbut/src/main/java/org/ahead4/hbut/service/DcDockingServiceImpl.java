package org.ahead4.hbut.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.ahead4.hbut.dto.DockingDept;
import org.ahead4.hbut.dto.DockingStaff;
import org.ahead4.hbut.dto.DockingStudent;
import org.ahead4.workflow.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 和学校数据中台（Data Center）对接用的服务层
 */
@Slf4j
@Service
public class DcDockingServiceImpl implements DockingService {


    public static void main(String[] args) throws InterruptedException {
        DcDockingServiceImpl service = new DcDockingServiceImpl();
        service.decryptSecret = System.getenv("HBUT_DOCKING_DC_DECRYPT_SECRET");
        service.secret = System.getenv("HBUT_DOCKING_DC_SECRET");
        service.baseUrl = System.getenv("HBUT_DOCKING_DC_BASE_URL");
//        JSONArray list = service.getStaffList();
//        log.info(list + "");
//        for (int i = 0; i < list.size(); i++) {
//            if (StringUtils.equals(list.getJSONObject(i).getString("工作证号"), "test2025jzg"))
//                log.info(list.getJSONObject(i) + "");
//
//        }
//        Thread.sleep(100);;
//        log.info(service.getStudent("2410522110")+"");
//        log.info(service.getStaff("19990010")+"");
//        log.info(service.getDeptList() + "");
//        log.info(service.getStudentList() + "");
        log.info(service.getStaffList() + "");
    }

    public static final OkHttpClient okHttpClient = new OkHttpClient.Builder()
            .connectTimeout(8000, TimeUnit.MILLISECONDS)
            .readTimeout(8000, TimeUnit.MILLISECONDS)
            .build();

    @Value("${hbut.docking.dc.base-url:}")
    public String baseUrl;
    @Value("${hbut.docking.dc.decrypt-secret:}")
    public String decryptSecret;
    @Value("${hbut.docking.dc.secret:}")
    public String secret;
    /**
     * 获取职工信息的接口地址
     */
    public final static String GET_STAFF_PATH = "/data-service-api/api/6000c9ac7a0a44d291c4ce5413a7c7fb/dataPageList";
    public final static String GET_STUDENT_PATH = "/data-service-api/api/7f3c7b32db64482a98801923170cfe94/dataPageList";
    public final static String GET_DEPT_PATH = "/data-service-api/api/c61e71af008c4f56ba6b1d1270116076/dataPageList";
    public final static String DECRYPT_DATA_PATH = "/data-service-api/api/decrypt";
    private JSONObject decryptData(String data) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("secret", decryptSecret);
        jsonObject.put("data", data);
        try (Response response = okHttpClient
                .newCall(cmmonReq(jsonObject, DECRYPT_DATA_PATH))
                .execute()) {
            if (response.body() != null) {
                String jsonStr = response.body().string();
                log.info(jsonStr);
                JSONObject json = JSON.parseObject(jsonStr);
                if(json == null) {
                    throw new RuntimeException("解密响应异常：" + jsonStr);
                }
                if (!json.getBooleanValue("success")) {
                    throw new RuntimeException("解密错误：" + json.getString("message"));
                }
                return JSON.parseObject(json.getString("result"));
            }
            throw new RuntimeException("解密失败："+data);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    Request cmmonReq(JSONObject params, String  path) {
        try {
            Thread.sleep(100); //
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        String jsonParam = params.toJSONString();
        Request request = new Request.Builder()
                .url(baseUrl + path)
                .method("POST", RequestBody.create(MediaType.parse("application/json; charset=utf-8"), jsonParam))
                .addHeader("X-Access-Secret", secret)
                .build();
        log.debug(baseUrl + path + "::" + jsonParam);
        return request;
    }
    JSONArray execAllListReq(String path) {
        JSONArray result = new JSONArray();
        for (int $skip = 0; $skip < 10000; $skip++) {
            JSONObject params = new JSONObject();
            params.put("gh", "");
            params.put("xh", "");
            params.put("pageSize", 500);
            params.put("pageNo", $skip + 1);
            try (Response response = okHttpClient
                    .newCall(cmmonReq(params, path))
                    .execute()) {
                if (response.body() != null) {
                    String jsonStr = response.body().string();
                    log.info(jsonStr);
                    JSONObject json = JSON.parseObject(jsonStr);
                    if (!json.getBooleanValue("success")) {
                        throw new RuntimeException("请求结果错误：" + json.getString("message"));
                    }
                    json = decryptData(json.getString("result"));
                    JSONArray arr = json.getJSONArray("records");
                    if (arr == null || arr.isEmpty()) continue;
                    result.addAll(arr);
                    Integer count = json.getInteger("total");
                    if (count == null || result.size()>=count) {
                        break;
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }
        return result;

    }
    /**
     *
     */
    @Override
    public List<DockingDept>  getDeptList() {
        JSONArray arr = execAllListReq(GET_DEPT_PATH);
        log.info(arr.toJSONString());
        return arr.stream().map(el -> json2Dept((JSONObject) el)).collect(Collectors.toList());
    }
    /**
     *
     */
    @Override
    public List<DockingStaff> getStaffList() {
        JSONArray arr =  execAllListReq(GET_STAFF_PATH);
        log.info(arr.toJSONString());
        return arr.stream().map(el -> json2Staff((JSONObject) el)).collect(Collectors.toList());
    }

    /**
     * 获取
     */
    @Override
    public List<DockingStudent> getStudentList() {
        JSONArray arr =  execAllListReq(GET_STUDENT_PATH);
        log.info(arr.toJSONString());
        return arr.stream().map(el -> json2Student((JSONObject) el)).collect(Collectors.toList());
    }
    JSONObject execOneReq(JSONObject params, String path) {

        params.put("pageSize", 1);
        params.put("pageNo", 1);
        try (Response response = okHttpClient
                .newCall(cmmonReq(params, path))
                .execute()) {
            if (response.body() != null) {
                String jsonStr = response.body().string();
                log.info(jsonStr);
                JSONObject json = JSON.parseObject(jsonStr);
                if (!json.getBooleanValue("success")) {
                    throw new RuntimeException(json.getString("message"));
                }
                json = decryptData(json.getString("result"));
                JSONArray result = json.getJSONArray("records");
                if (result == null || result.isEmpty()) return null;
                log.info(result.getJSONObject(0).toJSONString());
                return result.getJSONObject(0);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }
    /**
     * 根据id查询教职工
     * @return
     */
    @Override
    public DockingStaff getStaff(String id) {
        JSONObject params = new JSONObject();
        params.put("gh",id);
        return json2Staff(execOneReq(params, GET_STAFF_PATH));
    }

    /**
     * 根据id查询学生
     * @return
     */
    @Override
    public DockingStudent getStudent(String id) {
        JSONObject params = new JSONObject();
        params.put("xh",id);
        return json2Student(execOneReq(params, GET_STUDENT_PATH));
    }


    DockingStaff json2Staff(JSONObject jsonObject) {
        return DockingStaff.builder()
                .name(jsonObject.getString("XM"))
                .code(jsonObject.getString("GH"))
                .phone(jsonObject.getString("SJH"))
                .position(jsonObject.getString("ZWMC"))
                .deptName(jsonObject.getString("DWMC"))
                .deptCode(jsonObject.getString("DWH"))
                .level(jsonObject.getString("ZWJB"))
                .build();
    }
    DockingStudent json2Student(JSONObject jsonObject) {
        return DockingStudent.builder()
                .name(jsonObject.getString("XM"))
                .code(jsonObject.getString("GH"))
                .idCardNo(jsonObject.getString("SFZJH"))
                .phone(jsonObject.getString("SJH"))
                .deptName(jsonObject.getString("DWMC"))
                .deptCode(jsonObject.getString("DWH"))
                .build();
    }
    DockingDept json2Dept(JSONObject jsonObject) {
        return DockingDept.builder()
                .name(jsonObject.getString("DWMC"))
                .code(jsonObject.getString("DWH"))
                .pcode(jsonObject.getString("GH"))
                .build();
    }
}
