package org.ahead4.hbut.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class DockingStudent {
    /**
     * 姓名
     */
    String name;
    /**
     * 身份证号
     */
    String idCardNo;
    /**
     * 工作证号
     */
    String code;
    /**
     * 手机号码
     */
    String phone;
    /**
     * 机构编号
     */
    String deptCode;
    /**
     * 机构名称
     */
    String deptName;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
