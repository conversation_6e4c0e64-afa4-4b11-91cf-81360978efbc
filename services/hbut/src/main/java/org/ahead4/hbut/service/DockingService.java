package org.ahead4.hbut.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.ahead4.hbut.dto.DockingDept;
import org.ahead4.hbut.dto.DockingStaff;
import org.ahead4.hbut.dto.DockingStudent;

import java.util.List;

public interface DockingService {
    /**
     *
     */
    List<DockingDept> getDeptList();

    /**
     *
     */
    List<DockingStaff> getStaffList();

    /**
     * 获取
     */
    List<DockingStudent> getStudentList();

    /**
     * 根据id查询教职工
     *
     * @return
     */
    DockingStaff getStaff(String id);

    /**
     * 根据id查询学生
     *
     * @return
     */
    DockingStudent getStudent(String id);
}
