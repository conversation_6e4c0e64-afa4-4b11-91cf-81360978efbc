package org.ahead4.hbut.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.hbut.dto.DockingStaff;
import org.ahead4.permission.entity.UserDept;
import org.ahead4.permission.entity.UserPosition;
import org.ahead4.permission.entity.UserRole;
import org.ahead4.permission.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ahead4.permission.entity.User;
import org.ahead4.workflow.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import static org.ahead4.hbut.DockingUtil.*;

@Slf4j
@Service
public class SyncService {
    @Autowired
    UserService userService;
    @Autowired
    DeptService deptService;
    @Autowired
    UserRoleService userRoleService;
    @Autowired
    UserDeptService userDeptService;
    @Autowired
    UserPositionService userPositionService;

    @Transactional(
            rollbackFor = {Exception.class}
    )
    public void syncUser(DockingStaff node) {
        User user = buildUser(node);
        UserRole userRole = buildUserRole(node);
        UserDept userDept = buildUserDept(node);
        UserPosition userPosition = buildUserPosition(node);

        if (user == null || userRole == null || userDept == null || userPosition == null) {
            return;
        }

        userService.save(user);
        userRoleService.save(userRole);
        userDeptService.save(userDept);
        userPositionService.save(userPosition);
    }


}
