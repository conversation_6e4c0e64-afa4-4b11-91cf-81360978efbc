package org.ahead4.hbut.dto;

import com.alibaba.fastjson.JSON;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class DockingStaff {
    /**
     * 姓名
     */
    String name;
    /**
     * 工作证号
     */
    String code;
    /**
     * 部门职务
     */
    String position;
    /**
     * 手机号码
     */
    String phone;
    /**
     * 用户职级
     */
    String level;
    /**
     * 机构编号
     */
    String deptCode;
    /**
     * 机构名称
     */
    String deptName;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
