package org.ahead4.hbut;

import com.alibaba.fastjson.JSONObject;
import org.ahead4.hbut.dto.DockingStaff;
import org.ahead4.permission.entity.User;
import org.ahead4.permission.entity.UserDept;
import org.ahead4.permission.entity.UserPosition;
import org.ahead4.permission.entity.UserRole;
import org.ahead4.workflow.utils.StringUtils;

import java.util.Date;

public class DockingUtil {
    public static User buildUser(DockingStaff staff) {
        String 姓名 = staff.getName();
        String 工作证号 = staff.getCode();
        String 手机号码 = staff.getPhone();
        String 用户职级 = staff.getLevel();
        String 部门职务 = staff.getPosition();

        if (StringUtils.isEmpty(工作证号)) return null;

        User user = new User();
        user.setId(工作证号);
        user.setDisplayname(姓名);
        user.setUsername(工作证号);
        user.setMobile(手机号码);
        user.setJobGrade(用户职级);
        user.setCreator("docking");
        user.setUpdator("docking");
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        if (StringUtils.isNotEmpty(部门职务)) {
            user.setIsActive(true);
        }
        return user;
    }

    public static  UserRole buildUserRole(DockingStaff staff) {
        String 工作证号 = staff.getCode();
        String 部门职务 = staff.getPosition();
        String 机构编号 = staff.getDeptCode();

        if (StringUtils.isEmpty(工作证号)) return null;

        UserRole userRole = new UserRole();
        userRole.setUsername(工作证号);
        userRole.setCreator("docking");
        userRole.setUpdator("docking");
        userRole.setCreateTime(new Date());
        userRole.setUpdateTime(new Date());

        String roleCode = "user";
        if (StringUtils.equalsAny(部门职务, "部门正职", "分管领导", "主持工作")) {
            roleCode = "Approver";
            if ("10101".equals(机构编号)) {
                roleCode = "SOA";
            }
        }
        userRole.setRoleCode(roleCode);
        return userRole;
    }

    public static  UserPosition buildUserPosition(DockingStaff staff) {
        String 工作证号 = staff.getCode();
        String 部门职务 = staff.getPosition();
        String 机构编号 = staff.getDeptCode();
        String 机构名称 = staff.getDeptName();

        if (StringUtils.isEmpty(工作证号) || StringUtils.isEmpty(部门职务)) return null;

        UserPosition position = new UserPosition();
        position.setId(机构名称 + 部门职务);
        position.setDeptCode(机构编号);
        position.setUsername(工作证号);
        position.setCreator("docking");
        position.setUpdator("docking");
        position.setCreateTime(new Date());
        position.setUpdateTime(new Date());

        switch (部门职务) {
            case "部门副职":
                position.setPositionCode("DeputyHead");
                break;
            case "部门正职":
            case "主持工作":
                position.setPositionCode("HeadOfDepartment");
                break;
            case "分管领导":
                position.setPositionCode("VicePresident");
                break;
            default:
                position.setPositionCode(部门职务);
        }

        if ("部门正职".equals(部门职务)) position.setPositionType("Z");
        else if ("分管领导".equals(部门职务)) position.setPositionType("F");
        else if ("主持工作".equals(部门职务)) position.setPositionType("J");

        return position;
    }

    public static  UserDept buildUserDept(DockingStaff staff) {
        String 工作证号 = staff.getCode();
        String 机构编号 = staff.getDeptCode();

        if (StringUtils.isEmpty(工作证号) || StringUtils.isEmpty(机构编号)) return null;

        UserDept userDept = new UserDept();
        userDept.setDeptCode(机构编号);
        userDept.setUsername(工作证号);
        userDept.setCreator("docking");
        userDept.setUpdator("docking");
        userDept.setCreateTime(new Date());
        userDept.setUpdateTime(new Date());
        return userDept;
    }

}
