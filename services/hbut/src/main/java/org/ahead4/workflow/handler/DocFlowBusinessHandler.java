package org.ahead4.workflow.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.cdes.entity.DocFlow;
import org.ahead4.cdes.entity.dto.CCDto;
import org.ahead4.cdes.entity.dto.SmsSendRequest;
import org.ahead4.cdes.service.DocFlowService;
import org.ahead4.cdes.service.SmsService;
import org.ahead4.permission.entity.User;
import org.ahead4.permission.service.UserService;
import org.ahead4.web.exception.RestException;
import org.ahead4.workflow.constant.ProcessConstants;
import org.ahead4.workflow.spi.BizSystemAdapter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.delegate.TaskListener;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("docFlowWfHandler")
@RequiredArgsConstructor
public class DocFlowBusinessHandler implements TaskListener {

    private final DocFlowService docFlowService;

    private final BizSystemAdapter adapter;

    private final UserService userService;

    private final SmsService smsService;

    private final RuntimeService runtimeService;

    public void updateStatus(DelegateExecution execution) {
        FlowElement flowElement = execution.getCurrentFlowElement();
        if (ObjectUtil.isNotEmpty(flowElement) && flowElement instanceof UserTask) {
            UserTask userTask = (UserTask) flowElement;
            final String businessKey = execution.getProcessInstanceBusinessKey();
            // final String id = (String) execution.getVariable("id");
            String status = userTask.getDocumentation();
            if (Objects.nonNull(status)) {
                log.info("更新业务状态：{}, status: {}", businessKey, status);
                // 修改流程参数
                execution.setVariable("status", status);
                // 根据status修改业务数据
                docFlowService.update(null,
                        Wrappers.<DocFlow>lambdaUpdate().set(DocFlow::getStatus, status).eq(DocFlow::getId, businessKey));
            }
        }
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        final String processInstanceId = delegateTask.getProcessInstanceId();
        final String businessKey = (String) runtimeService.getVariable(processInstanceId, "id");
        final String name = delegateTask.getName();
        final String assignee = delegateTask.getAssignee();
        log.info("监听到{}任务分配：{}-{}-{}", name, processInstanceId, businessKey, assignee);
        final DocFlow docFlow = docFlowService.getById(businessKey);
        if (docFlow.getIsRemind() == 0) {
            log.warn("公文 {} 未配置发送消息", docFlow.getDocTitle());
            return;
        }
        final User user = userService.getOne(Wrappers.<User>lambdaQuery().eq(User::getUsername, assignee)
                .eq(User::getIsDelete, false).eq(User::getIsActive, true));
        if (Objects.nonNull(user) && StringUtils.isNotBlank(user.getMobile())) {
            final String mobile = user.getMobile();
            final SmsSendRequest smsSendRequest = new SmsSendRequest();
            smsSendRequest.setBusinessId(businessKey);
            smsSendRequest.setBusinessType("docFlow-" + name);
            smsSendRequest.setPhoneNumbers(mobile);
            final String content = String.format("公文系统提醒您，您有一个待审核的公文《%s》，请您尽快前往公文系统处理。", docFlow.getDocTitle());
            smsSendRequest.setMessageContent(content);
            smsService.sendSms(smsSendRequest);
        }
    }

    public void sendMsg(DelegateExecution execution) {
        Set<String> candidateUserIds = new LinkedHashSet<>();
        FlowElement flowElement = execution.getCurrentFlowElement();
        if (ObjectUtil.isNotEmpty(flowElement) && flowElement instanceof UserTask) {
            UserTask userTask = (UserTask) flowElement;
            final String businessKey = execution.getProcessInstanceBusinessKey();
            final DocFlow docFlow = docFlowService.getById(businessKey);
            if (docFlow.getIsRemind() == 0) {
                log.warn("公文 {} 未配置发送消息", docFlow.getDocTitle());
                return ;
            }
            final String assignee  = (String) execution.getVariable("assignee");
            log.info("proc_inst_id:{},用户任务：{},处理人：{}, 发送消息", execution.getProcessInstanceId(), userTask.getName(),
                    assignee);

            // 其他逻辑...
        }
    }

    /**
     * 获取用户 ID
     * <p>
     *     USERS：用户ID集合
     *     ROLES：角色ID集合
     *     DEPTS：部门ID集合
     *     DEPT:AND:ROLE: 部门ID集合和角色ID集合取交集
     *     DEPT:OR:ROLE: 部门ID集合和角色ID集合取并集
     *     DEPT:NIN:ROLE: 部门ID集合和角色ID集合取差集
     *     ROLE:NIN:DEPT: 角色ID集合和部门ID集合取差集
     * </p>
     *
     * @param execution 执行
     * @return {@link Set }<{@link String }>
     */
    public Set<String> getUserIds(DelegateExecution execution) {
        Set<String> candidateUserIds = new LinkedHashSet<>();
        FlowElement flowElement = execution.getCurrentFlowElement();
        final String id = (String) execution.getVariable("id");

        if (ObjectUtil.isNotEmpty(flowElement) && flowElement instanceof UserTask) {
            UserTask userTask = (UserTask) flowElement;
            final String taskName = userTask.getName();
            final String  taskId = userTask.getId();
            String dataType = userTask.getAttributeValue(ProcessConstants.NAMASPASE, ProcessConstants.PROCESS_CUSTOM_DATA_TYPE);
            if ("USERS".equals(dataType) && CollUtil.isNotEmpty(userTask.getCandidateUsers())) {
                // 添加候选用户id
                candidateUserIds.addAll(userTask.getCandidateUsers());
            } else if ("CCDTO".equals(dataType) ) {
                final String documentation = userTask.getDocumentation();
                if (CollUtil.isNotEmpty(userTask.getCandidateGroups())) {
                    candidateUserIds.addAll(userTask.getCandidateGroups());
                } else {
                    final Object variable = execution.getVariable(documentation);
                    // 将jsonObject 转换成List<CcDto>
                    final List<CCDto> ccDtos = JSON.parseArray(variable.toString(), CCDto.class);
                    if (CollUtil.isNotEmpty(ccDtos)) {
                        candidateUserIds.addAll(ccDtos.stream().map(CCDto::getUsername).collect(Collectors.toList()));
                    } else {
                        throw new RestException("请选择下一节点处理人");
                    }
                }
            } else if (CollUtil.isNotEmpty(userTask.getCandidateGroups())) {
                if ("ROLE".equalsIgnoreCase(dataType) || "DEPT".equalsIgnoreCase(dataType) || "POST".equalsIgnoreCase(dataType)) {
                    // 获取组的ID，角色ID集合或部门ID集合
                    List<String> groups = userTask.getCandidateGroups().stream()
                            .map(item -> item.substring(4))
                            .collect(Collectors.toList());
                    List<String> userIds = new ArrayList<>();
                    if ("ROLE".equals(dataType)) {
                        // 通过角色id，获取所有用户id集合
                        userIds = adapter.getUserIdsByRoleIds(groups);
                    } else if ("DEPT".equals(dataType)) {
                        userIds = adapter.getUserIdsByDeptIds(groups);
                    } else if ("POST".equals(dataType)) {
                        // 通过岗位id，获取所有用户id集合
                        userIds = adapter.getUserIdsByDeptAndPosition(Collections.emptyList(), groups);
                    }
                    if (CollectionUtils.isEmpty(userIds)) {
                        throw new RestException("流程节点" + taskName + "未获取到具体用户, 请进行人员调配后重新提交");
                    }
                    candidateUserIds.addAll(userIds);
                }
                if ("DEPT:AND:POST".equals(dataType)) {
                    // 通过adapter从业务表单获取部门信息
                    // 按dept,role分组
                    final List<String> candidateGroups = userTask.getCandidateGroups();
                    List<String> deptCodes = candidateGroups.stream().filter(o -> o.startsWith("DEPT")).map(item -> item.substring(4)).collect(Collectors.toList());
                    final List<String> postCodes =
                            candidateGroups.stream().filter(o -> o.startsWith("POST")).map(item -> item.substring(4)).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(deptCodes)) {
                        deptCodes = adapter.getDeptsInBusinessFormData(id, userTask.getId());
                    }
                    if (CollectionUtils.isEmpty(deptCodes)) {
                        throw new RestException("流程节点" + taskName + "未获取到具体用户, 请进行人员调配后重新提交");
                    }
                    List<String> deptAndPositionUsers = adapter.getUserIdsByDeptAndPosition(deptCodes, postCodes);
                    if (CollectionUtils.isEmpty(deptAndPositionUsers)) {
                        throw new RestException("流程节点" + taskName + "通过部门及职务未获取到具体用户, 请进行人员调配后重新提交");
                    }
                    candidateUserIds.addAll(deptAndPositionUsers);

                }
                if ("DEPT:AND:ROLE".equals(dataType) || "DEPT:OR:ROLE".equals(dataType) || "DEPT:NIN:ROLE".equals(dataType) || "ROLE:NIN:DEPT".equals(dataType)) {
                    // 通过adapter从业务表单获取部门信息
                    // 按dept,role分组
                    final List<String> candidateGroups = userTask.getCandidateGroups();
                    List<String> deptCodes = candidateGroups.stream().filter(o -> o.startsWith("DEPT")).map(item -> item.substring(4)).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(deptCodes)) {
                        deptCodes = adapter.getDeptsInBusinessFormData(id, userTask.getId());
                    }
                    // todo 未获取到用户ID集合，抛出异常
                    if (CollectionUtils.isEmpty(deptCodes)) {
                        throw new RestException("流程节点" + taskName + "未获取到具体用户, 请进行人员调配后重新提交");
                    }
                    final List<String> roleCodes = candidateGroups.stream().filter(o -> o.startsWith("ROLE")).map(item -> item.substring(4)).collect(Collectors.toList());
                    // 获取部门ID集合
                    final List<String> deptIds = adapter.getUserIdsByDeptIds(deptCodes);
                    // 获取角色ID集合
                    final List<String> roleIds = adapter.getUserIdsByRoleIds(roleCodes);
                    // 交集
                    if ("DEPT:AND:ROLE".equals(dataType)) {
                        candidateUserIds.addAll(deptIds.stream().filter(roleIds::contains).collect(Collectors.toList()));
                    }
                    // 并集
                    if ("DEPT:OR:ROLE".equals(dataType)) {
                        candidateUserIds.addAll(deptIds);
                    }
                    // 差集
                    if ("DEPT:NIN:ROLE".equals(dataType)) {
                        candidateUserIds.addAll(deptIds.stream().filter(o -> !roleIds.contains(o)).collect(Collectors.toList()));
                    }
                    if ("ROLE:NIN:DEPT".equals(dataType)) {
                        candidateUserIds.addAll(roleIds.stream().filter(o -> !deptIds.contains(o)).collect(Collectors.toList()));
                    }

                }
            }
        }
        return candidateUserIds;


    }

}
