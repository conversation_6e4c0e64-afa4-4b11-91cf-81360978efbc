package org.ahead4.workflow.controller;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.web.presentate.HttpMsg;
import org.ahead4.workflow.core.domain.ProcessQuery;
import org.ahead4.workflow.domain.bo.WfCopyBo;
import org.ahead4.workflow.domain.common.PageQuery;
import org.ahead4.workflow.domain.page.TableDataInfo;
import org.ahead4.workflow.domain.vo.WfCopyVo;
import org.ahead4.workflow.domain.vo.WfDefinitionVo;
import org.ahead4.workflow.domain.vo.WfSubProcessInstanceVo;
import org.ahead4.workflow.domain.vo.WfTaskVo;
import org.ahead4.workflow.service.IWfCopyService;
import org.ahead4.workflow.service.IWfProcessService;
import org.ahead4.workflow.spi.BizSystemAdapter;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**
 * 工作流流程管理
 *
 * <AUTHOR>
 * @createTime 2022/3/24 18:54
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/workflow/process")
@Api(tags = {"流程管理"})
public class WfProcessController extends BaseController {

    private final IWfProcessService processService;
    private final IWfCopyService copyService;
    private final BizSystemAdapter adapter;

    /**
     * 查询可发起流程列表
     *
     * @param pageQuery 分页参数
     */
    @GetMapping(value = "/list")
    @ApiOperation(value = "查询可发起流程列表")
    public TableDataInfo<WfDefinitionVo> startProcessList(ProcessQuery processQuery, PageQuery pageQuery) {
        return processService.selectPageStartProcessList(processQuery, pageQuery);
    }

    /**
     * 我拥有的流程
     */
    // @ApiOperation(value = "我拥有的流程")
    @GetMapping(value = "/ownList")
    public TableDataInfo<WfTaskVo> ownProcessList(ProcessQuery processQuery, PageQuery pageQuery) {
        return processService.selectPageOwnProcessList(processQuery, pageQuery);
    }

    /**
     * 获取待办列表
     */
    @PreAuthorize("isAuthenticated()")
    @ApiOperation(value = "获取待办列表")
    @GetMapping(value = "/todoList")
    public TableDataInfo<WfTaskVo> todoProcessList(ProcessQuery processQuery, PageQuery pageQuery) {
        return processService.selectPageTodoProcessList(processQuery, pageQuery);
    }

    /**
     * 获取待签列表
     *
     * @param processQuery 流程业务对象
     * @param pageQuery 分页参数
     */
    @ApiOperation(value = "获取待签列表")
    @GetMapping(value = "/claimList")
    public TableDataInfo<WfTaskVo> claimProcessList(ProcessQuery processQuery, PageQuery pageQuery) {
        return processService.selectPageClaimProcessList(processQuery, pageQuery);
    }

    /**
     * 获取已办列表
     *
     * @param pageQuery 分页参数
     */
    @ApiOperation(value = "获取已办列表")
    @GetMapping(value = "/finishedList")
    public TableDataInfo<WfTaskVo> finishedProcessList(ProcessQuery processQuery, PageQuery pageQuery) {
        return processService.selectPageFinishedProcessList(processQuery, pageQuery);
    }

    /**
     * 获取抄送列表
     *
     * @param copyBo 流程抄送对象
     * @param pageQuery 分页参数
     */
    @ApiOperation(value = "获取抄送列表")
    @GetMapping(value = "/copyList")
    public TableDataInfo<WfCopyVo> copyProcessList(WfCopyBo copyBo, PageQuery pageQuery) {
        copyBo.setUserId(adapter.getLonginUserId());
        return copyService.selectPageList(copyBo, pageQuery);
    }

    // /**
    //  * 导出可发起流程列表
    //  */


    // @PostMapping("/startExport")
    // public void startExport(@Validated ProcessQuery processQuery, HttpServletResponse response) {
    //     List<WfDefinitionVo> list = processService.selectStartProcessList(processQuery);
    //     ExcelUtil.exportExcel(list, "可发起流程", WfDefinitionVo.class, response);
    // }

    // /**
    //  * 导出我拥有流程列表
    //  */


    // @PostMapping("/ownExport")
    // public void ownExport(@Validated ProcessQuery processQuery, HttpServletResponse response) {
    //     List<WfTaskVo> list = processService.selectOwnProcessList(processQuery);
    //     List<WfOwnTaskExportVo> listVo = BeanUtil.copyToList(list, WfOwnTaskExportVo.class);
    //     for (WfOwnTaskExportVo exportVo : listVo) {
    //         exportVo.setStatus(ObjectUtil.isNull(exportVo.getFinishTime()) ? "进行中" : "已完成");
    //     }
    //     ExcelUtil.exportExcel(listVo, "我拥有流程", WfOwnTaskExportVo.class, response);
    // }

    // /**
    //  * 导出待办流程列表
    //  */


    // @PostMapping("/todoExport")
    // public void todoExport(@Validated ProcessQuery processQuery, HttpServletResponse response) {
    //     List<WfTaskVo> list = processService.selectTodoProcessList(processQuery);
    //     List<WfTodoTaskExportVo> listVo = BeanUtil.copyToList(list, WfTodoTaskExportVo.class);
    //     ExcelUtil.exportExcel(listVo, "待办流程", WfTodoTaskExportVo.class, response);
    // }
    //
    // /**
    //  * 导出待签流程列表
    //  */
    //
    //
    // @PostMapping("/claimExport")
    // public void claimExport(@Validated ProcessQuery processQuery, HttpServletResponse response) {
    //     List<WfTaskVo> list = processService.selectClaimProcessList(processQuery);
    //     List<WfClaimTaskExportVo> listVo = BeanUtil.copyToList(list, WfClaimTaskExportVo.class);
    //     ExcelUtil.exportExcel(listVo, "待签流程", WfClaimTaskExportVo.class, response);
    // }
    //
    // /**
    //  * 导出已办流程列表
    //  */
    //
    //
    // @PostMapping("/finishedExport")
    // public void finishedExport(@Validated ProcessQuery processQuery, HttpServletResponse response) {
    //     List<WfTaskVo> list = processService.selectFinishedProcessList(processQuery);
    //     List<WfFinishedTaskExportVo> listVo = BeanUtil.copyToList(list, WfFinishedTaskExportVo.class);
    //     ExcelUtil.exportExcel(listVo, "已办流程", WfFinishedTaskExportVo.class, response);
    // }
    //
    // /**
    //  * 导出抄送流程列表
    //  */
    //
    //
    // @PostMapping("/copyExport")
    // public void copyExport(WfCopyBo copyBo, HttpServletResponse response) {
    //     copyBo.setUserId(bizSystemAdapter.getLoginUserId());
    //     List<WfCopyVo> list = copyService.selectList(copyBo);
    //     ExcelUtil.exportExcel(list, "抄送流程", WfCopyVo.class, response);
    // }

    /**
     * 查询流程部署关联表单信息
     *
     * @param definitionId 流程定义id
     * @param deployId 流程部署id
     */
    @GetMapping("/getProcessForm")

    public HttpMsg getForm(@RequestParam(value = "definitionId") String definitionId,
                        @RequestParam(value = "deployId") String deployId,
                        @RequestParam(value = "procInsId", required = false) String procInsId) {
        return HttpMsg.ok().data(processService.selectFormContent(definitionId, deployId, procInsId));
    }

    /**
     * 根据流程定义id启动流程实例
     *
     * @param processDefId 流程定义id
     * @param variables 变量集合,json对象
     */
    @PostMapping("/start/{processDefId}")
    public HttpMsg start(@PathVariable(value = "processDefId") String processDefId,
            @RequestBody Map<String, Object> variables) {
        return HttpMsg.ok().data(processService.startProcessByDefId(processDefId, null, variables));

    }

    /**
     * 根据流程定义id启动流程实例
     *
     * @param processDefKey 流程定义id
     * @param variables 变量集合,json对象
     */
    @PostMapping("/startByKey/{processDefKey}")
    public HttpMsg startByDefKey(@PathVariable(value = "processDefKey") String processDefKey,
            @RequestBody Map<String, Object> variables) {
        final String instanceId = processService.startProcessByDefKey(processDefKey, null, variables);
        return HttpMsg.ok().data(instanceId);

    }

    /**
     * 删除流程实例
     *
     * @param instanceIds 流程实例ID串
     */
    @DeleteMapping("/instance/{instanceIds}")
    public HttpMsg delete(@PathVariable String[] instanceIds) {
        processService.deleteProcessByIds(instanceIds);
        return HttpMsg.ok();
    }

    /**
     * 读取xml文件
     * @param processDefId 流程定义ID
     */
    @GetMapping("/bpmnXml/{processDefId}")
    public HttpMsg getBpmnXml(@PathVariable(value = "processDefId") String processDefId) {
        return HttpMsg.ok().data(processService.queryBpmnXmlById(processDefId));
    }

    /**
     * 查询流程详情信息
     *
     * @param procInsId 流程实例ID
     * @param taskId 任务ID
     */
    @GetMapping("/detail")
    public HttpMsg detail(String procInsId, String taskId) {
        return HttpMsg.ok().data(processService.queryProcessDetail(procInsId, taskId));
    }

    /**
     * 根据流程实例ID查询流程详情信息
     * 包括流程定义和按执行顺序获取的所有用户任务及处理详情
     *
     * @param procInstId 流程实例ID
     */
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/detail/{procInstId}")
    @ApiOperation("根据流程实例ID查询流程详情信息")
    public HttpMsg detail(@PathVariable(value = "procInstId") String procInstId) {
//        return HttpMsg.ok().data(processService.queryProcessDetailByProcInstId(procInstId));
        return HttpMsg.ok().data(processService.queryProcessDetailsWithOptimizedSubProcessHandling(procInstId));
    }

    /**
     * 根据流程实例ID查询流程详情信息（兼容子流程）
     * 只返回主流程中的用户任务，子流程作为整体节点处理，避免子流程任务与主流程任务混乱
     *
     * @param procInstId 流程实例ID
     */
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/detail-with-subprocess/{procInstId}")
    @ApiOperation("根据流程实例ID查询流程详情信息（兼容子流程）")
    public HttpMsg detailWithSubProcess(@PathVariable(value = "procInstId") String procInstId) {
//        return HttpMsg.ok().data(processService.queryProcessDetailWithSubProcessByProcInstId(procInstId));
        return HttpMsg.ok().data(processService.queryProcessDetailsWithOptimizedSubProcessHandling(procInstId));
    }

    /**
     * 分页查询子流程实例列表
     *
     * @param procInstId 主流程实例ID
     * @param activityId 子流程执行ID
     * @param pageQuery 分页参数
     */
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/subprocess-instances/{procInstId}")
    @ApiOperation("分页查询子流程实例列表")
    public TableDataInfo<WfSubProcessInstanceVo> selectPageSubProcessInstances(
            @PathVariable(value = "procInstId") String procInstId,
            @RequestParam(value = "activityId") String activityId, PageQuery pageQuery) {
        return processService.selectPageSubProcessInstances(procInstId, activityId, pageQuery);
    }

    /**
     * 查询子流程实例详情
     *
     * @param procInstId 主流程实例ID
     * @param subProcessInstanceId 子流程实例ID
     */
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/subprocess-detail/{procInstId}/{subProcessInstanceId}")
    @ApiOperation("查询子流程实例详情")
    public HttpMsg querySubProcessInstanceDetails(
            @PathVariable(value = "procInstId") String procInstId,
            @PathVariable(value = "subProcessInstanceId") String subProcessInstanceId) {
        return HttpMsg.ok().data(processService.querySubProcessInstanceDetails(procInstId, subProcessInstanceId));
    }
}
