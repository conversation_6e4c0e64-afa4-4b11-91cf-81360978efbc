package org.ahead4.workflow.listener;


import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * 任务驳回事件
 *
 * <AUTHOR>
 * @date 2025/05/10
 */
@Setter
@Getter
public class TaskRejectEvent extends ApplicationEvent {

    /**
     * 任务id
     */
    private final String taskId;

    /**
     * 进程实例 ID
     */
    private final String processInstanceId;

    /**
     * 业务id
     */
    private final String businessKey;

    /**
     * 活动id
     */
    private final String activityId;

    /**
     * 目标任务 ID
     */
    private final String targetTaskId;

    public TaskRejectEvent(String taskId, String processInstanceId, String businessKey, String activityId,
                           String targetTaskId) {
        super(activityId);
        this.taskId = taskId;
        this.processInstanceId = processInstanceId;
        this.businessKey = businessKey;
        this.activityId = activityId;
        this.targetTaskId = targetTaskId;
    }
}
