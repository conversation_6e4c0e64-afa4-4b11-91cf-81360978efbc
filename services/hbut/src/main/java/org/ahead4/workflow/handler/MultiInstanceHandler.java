package org.ahead4.workflow.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;

import org.ahead4.workflow.constant.ProcessConstants;
import org.ahead4.workflow.spi.BizSystemAdapter;
import org.ahead4.workflow.utils.SpringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
/**
 * 多实例处理类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Component("multiInstanceHandler")
public class MultiInstanceHandler {

    private final BizSystemAdapter adapter;

    /**
     * 获取用户 ID
     * <p>
     *     USERS：用户ID集合
     *     ROLES：角色ID集合
     *     DEPTS：部门ID集合
     *     DEPT:AND:ROLE: 部门ID集合和角色ID集合取交集
     *     DEPT:OR:ROLE: 部门ID集合和角色ID集合取并集
     *     DEPT:NIN:ROLE: 部门ID集合和角色ID集合取差集
     *     ROLE:NIN:DEPT: 角色ID集合和部门ID集合取差集
     * </p>
     *
     * @param execution 执行
     * @return {@link Set }<{@link String }>
     */
    public Set<String> getUserIds(DelegateExecution execution) {
        Set<String> candidateUserIds = new LinkedHashSet<>();
        FlowElement flowElement = execution.getCurrentFlowElement();
        if (ObjectUtil.isNotEmpty(flowElement) && flowElement instanceof UserTask) {
            UserTask userTask = (UserTask) flowElement;
            String dataType = userTask.getAttributeValue(ProcessConstants.NAMASPASE, ProcessConstants.PROCESS_CUSTOM_DATA_TYPE);
            if ("USERS".equals(dataType) && CollUtil.isNotEmpty(userTask.getCandidateUsers())) {
                // 添加候选用户id
                candidateUserIds.addAll(userTask.getCandidateUsers());
            } else if (CollUtil.isNotEmpty(userTask.getCandidateGroups())) {
                if ("ROLES".equals(dataType) || "DEPTS".equals(dataType)) {
                    // 获取组的ID，角色ID集合或部门ID集合
                    List<String> groups = userTask.getCandidateGroups().stream()
                                                .map(item -> item.substring(4))
                                                .collect(Collectors.toList());
                    List<String> userIds = new ArrayList<>();
                    if ("ROLES".equals(dataType)) {
                        // 通过角色id，获取所有用户id集合
                        userIds = adapter.getUserIdsByRoleIds(groups);
                    } else if ("DEPTS".equals(dataType)) {
                        userIds = adapter.getUserIdsByDeptIds(groups);
                    }
                    candidateUserIds.addAll(userIds);
                }
                if ("DEPT:AND:ROLE".equals(dataType) || "DEPT:OR:ROLE".equals(dataType) || "DEPT:NIN:ROLE".equals(dataType) || "ROLE:NIN:DEPT".equals(dataType)) {
                    // 按dept,role分组
                    final List<String> candidateGroups = userTask.getCandidateGroups();
                    final List<String> deptCodes = candidateGroups.stream().filter(o -> o.startsWith("DEPT")).map(item -> item.substring(4)).collect(Collectors.toList());
                    final List<String> roleCodes = candidateGroups.stream().filter(o -> o.startsWith("ROLE")).map(item -> item.substring(4)).collect(Collectors.toList());
                    // 获取部门ID集合
                    final List<String> deptIds = adapter.getUserIdsByDeptIds(deptCodes);
                    // 获取角色ID集合
                    final List<String> roleIds = adapter.getUserIdsByRoleIds(roleCodes);
                    // 交集
                    if ("DEPT:AND:ROLE".equals(dataType)) {
                        candidateUserIds.addAll(deptIds.stream().filter(roleIds::contains).collect(Collectors.toList()));
                    }
                    // 并集
                    if ("DEPT:OR:ROLE".equals(dataType)) {
                        candidateUserIds.addAll(deptIds);
                    }
                    // 差集
                    if ("DEPT:NIN:ROLE".equals(dataType)) {
                        candidateUserIds.addAll(deptIds.stream().filter(o -> !roleIds.contains(o)).collect(Collectors.toList()));
                    }
                    if ("ROLE:NIN:DEPT".equals(dataType)) {
                        candidateUserIds.addAll(roleIds.stream().filter(o -> !deptIds.contains(o)).collect(Collectors.toList()));
                    }

                }
            }
       }
        return candidateUserIds;


    }
    //
    // public Set<String> getUserIds(DelegateExecution execution) {
    //     Set<String> candidateUserIds = new LinkedHashSet<>();
    //     FlowElement flowElement = execution.getCurrentFlowElement();
    //     if (ObjectUtil.isNotEmpty(flowElement) && flowElement instanceof UserTask) {
    //         UserTask userTask = (UserTask) flowElement;
    //         String dataType = userTask.getAttributeValue(ProcessConstants.NAMASPASE, ProcessConstants.PROCESS_CUSTOM_DATA_TYPE);
    //         if ("USERS".equals(dataType) && CollUtil.isNotEmpty(userTask.getCandidateUsers())) {
    //             // 添加候选用户id
    //             candidateUserIds.addAll(userTask.getCandidateUsers());
    //         } else if (CollUtil.isNotEmpty(userTask.getCandidateGroups())) {
    //             // 获取组的ID，角色ID集合或部门ID集合
    //             List<Long> groups = userTask.getCandidateGroups().stream()
    //                 .map(item -> Long.parseLong(item.substring(4)))
    //                 .collect(Collectors.toList());
    //             List<String> userIds = new ArrayList<>();
    //             final BizSystemAdapter adapter = SpringUtils.getBean(BizSystemAdapter.class);
    //             if (ObjectUtil.isNull(adapter)) {
    //                 log.warn("未找到BizSystemAdapter实现类");
    //                 return candidateUserIds;
    //             }
    //             if ("ROLES".equals(dataType)) {
    //                 // 通过角色id，获取所有用户id集合
    //                 userIds = adapter.getUserIdsByRoleIds(groups);
    //                 // LambdaQueryWrapper<SysUserRole> lqw = Wrappers.lambdaQuery(SysUserRole.class).select(SysUserRole::getUserId).in(SysUserRole::getRoleId, groups);
    //                 // userIds = SimpleQuery.list(lqw, SysUserRole::getUserId);
    //             } else if ("DEPTS".equals(dataType)) {
    //                 userIds = adapter.getUserIdsByDeptIds(groups);
    //                 // 通过部门id，获取所有用户id集合
    //                 // LambdaQueryWrapper<SysUser> lqw = Wrappers.lambdaQuery(SysUser.class).select(SysUser::getUserId).in(SysUser::getDeptId, groups);
    //                 // userIds = SimpleQuery.list(lqw, SysUser::getUserId);
    //             }
    //             // 添加候选用户id
    //             candidateUserIds.addAll(userIds);
    //         }
    //     }
    //     return candidateUserIds;
    // }
}
