package org.ahead4.workflow.service;



import com.baomidou.mybatisplus.extension.service.IService;
import org.ahead4.workflow.domain.WfCopy;
import org.ahead4.workflow.domain.bo.WfCopyBo;
import org.ahead4.workflow.domain.bo.WfTaskBo;
import org.ahead4.workflow.domain.common.PageQuery;
import org.ahead4.workflow.domain.page.TableDataInfo;
import org.ahead4.workflow.domain.vo.WfCopyVo;

import java.util.List;

/**
 * 流程抄送Service接口
 *
 * <AUTHOR>
 * @date 2022-05-19
 */
public interface IWfCopyService extends IService<WfCopy> {

    /**
     * 查询流程抄送
     *
     * @param copyId 流程抄送主键
     * @return 流程抄送
     */
    WfCopyVo queryById(Long copyId);

    /**
     * 查询流程抄送列表
     *
     * @param wfCopy 流程抄送
     * @return 流程抄送集合
     */
    TableDataInfo<WfCopyVo> selectPageList(WfCopyBo wfCopy, PageQuery pageQuery);

    /**
     * 查询流程抄送列表
     *
     * @param wfCopy 流程抄送
     * @return 流程抄送集合
     */
    List<WfCopyVo> selectList(WfCopyBo wfCopy);

    /**
     * 抄送
     * @param taskBo
     * @return
     */
    Boolean makeCopy(WfTaskBo taskBo);
}
