package org.ahead4.workflow.utils;


import org.ahead4.workflow.core.domain.ProcessQuery;
import org.apache.commons.collections4.MapUtils;
import org.flowable.common.engine.api.query.Query;
import org.flowable.common.engine.impl.db.SuspensionState;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.engine.runtime.ProcessInstanceQuery;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.crypto.Data;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 流程工具类
 *
 * <AUTHOR>
 * @since 2022/12/11 03:35
 */
public class ProcessUtils {

    private static final Logger log = LoggerFactory.getLogger(ProcessUtils.class);

    public static void buildProcessSearch(Query<?, ?> query, ProcessQuery process) {
        if (query instanceof ProcessDefinitionQuery) {
            buildProcessDefinitionSearch((ProcessDefinitionQuery) query, process);
        } else if (query instanceof TaskQuery) {
            buildTaskSearch((TaskQuery) query, process);
        } else if (query instanceof HistoricTaskInstanceQuery) {
            buildHistoricTaskInstanceSearch((HistoricTaskInstanceQuery) query, process);
        } else if (query instanceof HistoricProcessInstanceQuery) {
            buildHistoricProcessInstanceSearch((HistoricProcessInstanceQuery) query, process);
        }
        // 移除beginTime和endTime参数
        // if (MapUtils.isNotEmpty(process.getParams())) {
        //     process.getParams().remove("startTime");
        //     process.getParams().remove("endTime");
        // }
        // 处理动态参数
        handleDynamicParams(query, process.getParams());
    }

    /**
     * 构建流程定义搜索
     */
    public static void buildProcessDefinitionSearch(ProcessDefinitionQuery query, ProcessQuery process) {
        // 流程标识
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            query.processDefinitionKeyLike("%" + process.getProcessKey() + "%");
        }
        // 流程名称
        if (StringUtils.isNotBlank(process.getProcessName())) {
            query.processDefinitionNameLike("%" + process.getProcessName() + "%");
        }
        // 流程分类
        if (StringUtils.isNotBlank(process.getCategory())) {
            query.processDefinitionCategory(process.getCategory());
        }
        // 流程状态
        if (StringUtils.isNotBlank(process.getState())) {
            if (SuspensionState.ACTIVE.toString().equals(process.getState())) {
                query.active();
            } else if (SuspensionState.SUSPENDED.toString().equals(process.getState())) {
                query.suspended();
            }
        }
    }

    /**
     * 构建任务搜索
     */
    public static void buildTaskSearch(TaskQuery query, ProcessQuery process) {
        Map<String, Object> params = process.getParams();
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            query.processDefinitionKeyLike("%" + process.getProcessKey() + "%");
        }
        if (StringUtils.isNotBlank(process.getProcessName())) {
            query.processDefinitionNameLike("%" + process.getProcessName() + "%");
        }
        // if (params.get("startTime") != null && params.get("endTime") != null) {
        //     query.taskCreatedAfter(DateUtils.parseDate(params.get("beginTime")));
        //     query.taskCreatedBefore(DateUtils.parseDate(params.get("endTime")));
        // }
        // if (Objects.nonNull(params.get("flowCode"))) {
        //     query.processVariableValueEquals("flowCode", String.valueOf(params.get("flowCode")));
        // }
        // if (Objects.nonNull(params.get("docTitle"))) {
        //     query.processVariableValueLike("docTitle", "%" + params.get("docTitle") + "%");
        // }
        // if (Objects.nonNull(params.get("docType"))) {
        //     query.processVariableValueEquals("docType", (Integer) params.get("docType"));
        // }
    }

    private static void buildHistoricTaskInstanceSearch(HistoricTaskInstanceQuery query, ProcessQuery process) {
        Map<String, Object> params = process.getParams();
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            query.processDefinitionKeyLike("%" + process.getProcessKey() + "%");
        }
        if (StringUtils.isNotBlank(process.getProcessName())) {
            query.processDefinitionNameLike("%" + process.getProcessName() + "%");
        }
        if (params.get("beginTime") != null && params.get("endTime") != null) {
            query.taskCompletedAfter(DateUtils.parseDate(params.get("beginTime")));
            query.taskCompletedBefore(DateUtils.parseDate(params.get("endTime")));
        }
    }

    /**
     * 构建历史流程实例搜索
     */
    public static void buildHistoricProcessInstanceSearch(HistoricProcessInstanceQuery query, ProcessQuery process) {
        Map<String, Object> params = process.getParams();
        // 流程标识
        if (StringUtils.isNotBlank(process.getProcessKey())) {
            query.processDefinitionKey(process.getProcessKey());
        }
        // 流程名称
        if (StringUtils.isNotBlank(process.getProcessName())) {
            query.processDefinitionName(process.getProcessName());
        }
        // 流程名称
        if (StringUtils.isNotBlank(process.getCategory())) {
            query.processDefinitionCategory(process.getCategory());
        }
        if (params.get("beginTime") != null && params.get("endTime") != null) {
            query.startedAfter(DateUtils.parseDate(params.get("beginTime")));
            query.startedBefore(DateUtils.parseDate(params.get("endTime")));
        }

    }

    /**
     * 安全地调用变量相等查询方法
     *
     * @param query 查询对象
     * @param name 变量名
     * @param value 变量值
     */
    private static void safeVariableValueEquals(Query<?, ?> query, String name, Object value) {
        if (query instanceof TaskQuery) {
            ((TaskQuery) query).processVariableValueEquals(name, value);
        } else if (query instanceof HistoricTaskInstanceQuery) {
            ((HistoricTaskInstanceQuery) query).processVariableValueEquals(name, value);
        } else if (query instanceof ProcessInstanceQuery) {
            ((ProcessInstanceQuery) query).variableValueEquals(name, value);
        } else if (query instanceof HistoricProcessInstanceQuery) {
            ((HistoricProcessInstanceQuery) query).variableValueEquals(name, value);
        } else {
            log.warn("无法在查询类型 {} 上执行 variableValueEquals 操作", query.getClass().getName());
        }
    }

    /**
     * 安全地调用变量模糊查询方法
     *
     * @param query 查询对象
     * @param name 变量名
     * @param value 变量值
     */
    private static void safeVariableValueLike(Query<?, ?> query, String name, String value) {
        if (query instanceof TaskQuery) {
            ((TaskQuery) query).processVariableValueLike(name, value);
        } else if (query instanceof HistoricTaskInstanceQuery) {
            ((HistoricTaskInstanceQuery) query).processVariableValueLike(name, value);
        } else if (query instanceof ProcessInstanceQuery) {
            ((ProcessInstanceQuery) query).variableValueLike(name, value);
        } else if (query instanceof HistoricProcessInstanceQuery) {
            ((HistoricProcessInstanceQuery) query).variableValueLike(name, value);
        } else {
            log.warn("无法在查询类型 {} 上执行 variableValueLike 操作", query.getClass().getName());
        }
    }

    /**
     * 安全地调用变量小于等于查询方法
     *
     * @param query 查询对象
     * @param name 变量名
     * @param value 变量值
     */
    private static void safeVariableValueLessThanOrEqual(Query<?, ?> query, String name, Object value) {
        if (query instanceof TaskQuery) {
            ((TaskQuery) query).processVariableValueLessThanOrEqual(name, value);
        } else if (query instanceof HistoricTaskInstanceQuery) {
            ((HistoricTaskInstanceQuery) query).processVariableValueLessThanOrEqual(name, value);
        } else if (query instanceof ProcessInstanceQuery) {
            ((ProcessInstanceQuery) query).variableValueLessThanOrEqual(name, value);
        } else if (query instanceof HistoricProcessInstanceQuery) {
            ((HistoricProcessInstanceQuery) query).variableValueLessThanOrEqual(name, value);
        } else {
            log.warn("无法在查询类型 {} 上执行 variableValueLessThanOrEqual 操作", query.getClass().getName());
        }
    }

    /**
     * 安全地调用变量大于等于查询方法
     *
     * @param query 查询对象
     * @param name 变量名
     * @param value 变量值
     */
    private static void safeVariableValueGreaterThanOrEqual(Query<?, ?> query, String name, Object value) {
        if (query instanceof TaskQuery) {
            ((TaskQuery) query).processVariableValueGreaterThanOrEqual(name, value);
        } else if (query instanceof HistoricTaskInstanceQuery) {
            ((HistoricTaskInstanceQuery) query).processVariableValueGreaterThanOrEqual(name, value);
        } else if (query instanceof ProcessInstanceQuery) {
            ((ProcessInstanceQuery) query).variableValueGreaterThanOrEqual(name, value);
        } else if (query instanceof HistoricProcessInstanceQuery) {
            ((HistoricProcessInstanceQuery) query).variableValueGreaterThanOrEqual(name, value);
        } else {
            log.warn("无法在查询类型 {} 上执行 variableValueGreaterThanOrEqual 操作", query.getClass().getName());
        }
    }

    private static void handleDynamicParams(Query<?, ?> query, Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            return;
        }

        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value == null || value.equals("")) {
                continue;
            }
            // 处理时间范围查询
            if (key.equals("startTime") && value instanceof Data) {
                handleTimeQuery(query,key,value, true);
                continue;
            }
            if (key.equals("endTime") && value instanceof Date) {
                handleTimeQuery(query,key,value, false);
                continue;
            }


            // 处理模糊查询
            if (key.endsWith("Like") && value instanceof String) {
                String actualKey = key.substring(0, key.length() - 4);
                safeVariableValueLike(query, actualKey, "%" + value + "%");
                continue;
            }

            // 处理精确匹配
            safeVariableValueEquals(query, key, value);
        }
    }

    private static void handleTimeQuery(Query<?, ?> query, String baseKey, Object value, boolean isBefore) {
        switch (baseKey.toLowerCase()) {
            case "starttime":
                if (query instanceof TaskQuery) {
                    ((TaskQuery) query).taskCreatedBefore((Date) value);
                } else if (query instanceof HistoricTaskInstanceQuery) {
                    ((HistoricTaskInstanceQuery) query).taskCreatedBefore((Date) value);
                } else if (query instanceof ProcessInstanceQuery) {
                    ((ProcessInstanceQuery) query).startedBefore((Date) value);
                }
                break;
            case "endtime":
                if (query instanceof TaskQuery) {
                    ((TaskQuery) query).taskCreatedAfter((Date) value);
                } else if (query instanceof HistoricTaskInstanceQuery) {
                    ((HistoricTaskInstanceQuery) query).taskCreatedAfter((Date) value);
                } else if (query instanceof HistoricProcessInstanceQuery) {
                    ((HistoricProcessInstanceQuery) query).startedAfter((Date) value);
                }

            // case "created":
            //     if (isBefore) {
            //         if (query instanceof TaskQuery) {
            //             ((TaskQuery) query).taskCreatedBefore((Date) value);
            //         } else if (query instanceof HistoricTaskInstanceQuery) {
            //             ((HistoricTaskInstanceQuery) query).taskCreatedBefore((Date) value);
            //         }
            //     } else {
            //         if (query instanceof TaskQuery) {
            //             ((TaskQuery) query).taskCreatedAfter((Date) value);
            //         } else if (query instanceof HistoricTaskInstanceQuery) {
            //             ((HistoricTaskInstanceQuery) query).taskCreatedAfter((Date) value);
            //         }
            //     }
            //     break;
            // case "completed":
            //     if (query instanceof HistoricTaskInstanceQuery) {
            //         if (isBefore) {
            //             ((HistoricTaskInstanceQuery) query).taskCompletedBefore((Date) value);
            //         } else {
            //             ((HistoricTaskInstanceQuery) query).taskCompletedAfter((Date) value);
            //         }
            //     }
            //     break;
            // case "started":
            //     if (query instanceof HistoricProcessInstanceQuery) {
            //         if (isBefore) {
            //             ((HistoricProcessInstanceQuery) query).startedBefore((Date) value);
            //         } else {
            //             ((HistoricProcessInstanceQuery) query).startedAfter((Date) value);
            //         }
            //     }
            //     break;
            default:
                // 对于其他时间字段，使用变量查询
                if (isBefore) {
                    safeVariableValueLessThanOrEqual(query, baseKey, value);
                } else {
                    safeVariableValueGreaterThanOrEqual(query, baseKey, value);
                }
        }
    }
}
