package org.ahead4.workflow.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.ahead4.cdes.entity.dto.CCDto;
import org.ahead4.workflow.domain.WfCopy;
import org.ahead4.workflow.domain.bo.WfCopyBo;
import org.ahead4.workflow.domain.bo.WfTaskBo;
import org.ahead4.workflow.domain.common.PageQuery;
import org.ahead4.workflow.domain.page.TableDataInfo;
import org.ahead4.workflow.domain.vo.WfCopyVo;
import org.ahead4.workflow.mapper.WfCopyMapper;
import org.ahead4.workflow.service.IWfCopyService;
import org.ahead4.workflow.spi.BizSystemAdapter;
import org.ahead4.workflow.utils.BeanCopyUtils;
import org.ahead4.workflow.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 流程抄送Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-19
 */
@RequiredArgsConstructor
@Service
public class WfCopyServiceImpl extends ServiceImpl<WfCopyMapper, WfCopy> implements IWfCopyService {

    private final WfCopyMapper baseMapper;

    private final HistoryService historyService;

    private final BizSystemAdapter bizSystemAdapter;

    /**
     * 查询流程抄送
     *
     * @param copyId 流程抄送主键
     * @return 流程抄送
     */
    @Override
    public WfCopyVo queryById(Long copyId){
        final WfCopy wfCopy = this.getById(copyId);
        if (ObjectUtil.isNull(wfCopy)) {
            return null;
        }
        return BeanCopyUtils.copy(wfCopy, WfCopyVo.class);
    }

    /**
     * 查询流程抄送列表
     *
     * @param bo 流程抄送
     * @return 流程抄送
     */
    @Override
    public TableDataInfo<WfCopyVo> selectPageList(WfCopyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WfCopy> lqw = buildQueryWrapper(bo);
        final Page<WfCopy> page = this.page(pageQuery.build(), lqw);
        IPage<WfCopyVo> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return TableDataInfo.build(voPage);
        }
        voPage.setRecords(BeanCopyUtils.copyList(page.getRecords(), WfCopyVo.class));
        return TableDataInfo.build(voPage);
    }

    /**
     * 查询流程抄送列表
     *
     * @param bo 流程抄送
     * @return 流程抄送
     */
    @Override
    public List<WfCopyVo> selectList(WfCopyBo bo) {
        LambdaQueryWrapper<WfCopy> lqw = buildQueryWrapper(bo);
        final List<WfCopy> list = this.list(lqw);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyList(list, WfCopyVo.class);
    }

    private LambdaQueryWrapper<WfCopy> buildQueryWrapper(WfCopyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WfCopy> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, WfCopy::getUserId, bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getProcessName()), WfCopy::getProcessName, bo.getProcessName());
        lqw.like(StringUtils.isNotBlank(bo.getOriginatorName()), WfCopy::getOriginatorName, bo.getOriginatorName());
        return lqw;
    }

    @Override
    public Boolean makeCopy(WfTaskBo taskBo) {
        final List<CCDto> copyUsers = taskBo.getMakeCopy();
        if (CollectionUtils.isEmpty(copyUsers)) {
            // 若抄送用户为空，则不需要处理，返回成功
            return true;
        }
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
            .processInstanceId(taskBo.getProcInstId()).singleResult();
        final String businessKey = historicProcessInstance.getBusinessKey();
        List<WfCopy> copyList = new ArrayList<>(copyUsers.size());
        String originatorId = bizSystemAdapter.getLonginUserId();
        String originatorName = bizSystemAdapter.getNickNameById(originatorId);
        String title = historicProcessInstance.getProcessDefinitionName() + "-" + taskBo.getTaskName();
        for (CCDto cc : copyUsers) {
            WfCopy copy = new WfCopy();
            copy.setTitle(title);
            copy.setProcessId(historicProcessInstance.getProcessDefinitionId());
            copy.setProcessName(historicProcessInstance.getProcessDefinitionName());
            copy.setDeploymentId(historicProcessInstance.getDeploymentId());
            copy.setInstanceId(taskBo.getProcInstId());
            copy.setTaskId(taskBo.getTaskId());
            copy.setUserId(cc.getUsername());
            copy.setOriginatorId(originatorId);
            copy.setOriginatorName(originatorName);
            copy.setBusinessKey(businessKey);
            copyList.add(copy);
        }
        return saveBatch(copyList);
    }
}
