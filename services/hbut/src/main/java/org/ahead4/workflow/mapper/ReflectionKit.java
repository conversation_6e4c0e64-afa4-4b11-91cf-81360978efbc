// //
// // Source code recreated from a .class file by IntelliJ IDEA
// // (powered by FernFlower decompiler)
// //
// package org.ahead4.workflow.mapper;
//
// import com.baomidou.mybatisplus.core.toolkit.ClassUtils;
// import com.baomidou.mybatisplus.core.toolkit.Assert;
// import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
// import com.baomidou.mybatisplus.core.toolkit.ExceptionUtils;
// import com.baomidou.mybatisplus.core.toolkit.reflect.GenericTypeUtils
// import com.baomidou.mybatisplus.core.toolkit.reflect.GenericTypeUtils;
// import com.baomidou.mybatisplus.core.toolkit.reflect.TypeParameterResolver;
// import java.lang.reflect.AccessibleObject;
// import java.lang.reflect.Field;
// import java.lang.reflect.Modifier;
// import java.security.AccessController;
// import java.util.ArrayList;
// import java.util.Collections;
// import java.util.IdentityHashMap;
// import java.util.LinkedHashMap;
// import java.util.List;
// import java.util.Map;
// import java.util.Objects;
// import java.util.concurrent.ConcurrentHashMap;
// import java.util.function.Function;
// import java.util.stream.Collectors;
// import java.util.stream.Stream;
//
// public final class ReflectionKit {
//     private static final Map<Class<?>, List<Field>> CLASS_FIELD_CACHE = new ConcurrentHashMap();
//     /** @deprecated */
//     @Deprecated
//     private static final Map<Class<?>, Class<?>> PRIMITIVE_WRAPPER_TYPE_MAP = new IdentityHashMap(8);
//     private static final Map<Class<?>, Class<?>> PRIMITIVE_TYPE_TO_WRAPPER_MAP = new IdentityHashMap(8);
//
//     public ReflectionKit() {
//     }
//
//     /** @deprecated */
//     @Deprecated
//     public static Object getFieldValue(Object entity, String fieldName) {
//         Class<?> cls = entity.getClass();
//         Map<String, Field> fieldMaps = getFieldMap(cls);
//
//         try {
//             Field field = (Field)fieldMaps.get(fieldName);
//             Assert.notNull(field, "Error: NoSuchField in %s for %s.  Cause:", new Object[]{cls.getSimpleName(), fieldName});
//             field.setAccessible(true);
//             return field.get(entity);
//         } catch (ReflectiveOperationException e) {
//             throw ExceptionUtils.mpe("Error: Cannot read field in %s.  Cause:", e, new Object[]{cls.getSimpleName()});
//         }
//     }
//
//     public static Class<?> getSuperClassGenericType(final Class<?> clazz, final Class<?> genericIfc, final int index) {
//         Class<?> userClass = ClassUtils.getUserClass(clazz);
//         if (GenericTypeUtils.hasGenericTypeResolver()) {
//             Class<?>[] typeArguments = GenericTypeUtils.resolveTypeArguments(userClass, genericIfc);
//             return null == typeArguments ? null : typeArguments[index];
//         } else {
//             return (Class)TypeParameterResolver.resolveClassIndexedParameter(userClass, genericIfc, index);
//         }
//     }
//
//     public static Map<String, Field> getFieldMap(Class<?> clazz) {
//         List<Field> fieldList = getFieldList(clazz);
//         return CollectionUtils.isNotEmpty(fieldList) ? (Map)fieldList.stream().collect(Collectors.toMap(Field::getName, Function.identity())) : Collections.emptyMap();
//     }
//
//     public static List<Field> getFieldList(Class<?> clazz) {
//         return Objects.isNull(clazz) ? Collections.emptyList() : (List) CollectionUtils.computeIfAbsent(CLASS_FIELD_CACHE, clazz, (k) -> {
//             Field[] fields = k.getDeclaredFields();
//             List<Field> superFields = new ArrayList();
//
//             for(Class<?> currentClass = k.getSuperclass(); currentClass != null; currentClass = currentClass.getSuperclass()) {
//                 Field[] declaredFields = currentClass.getDeclaredFields();
//                 Collections.addAll(superFields, declaredFields);
//             }
//
//             Map<String, Field> fieldMap = excludeOverrideSuperField(fields, superFields);
//             return (List)fieldMap.values().stream().filter((f) -> !Modifier.isStatic(f.getModifiers())).filter((f) -> !Modifier.isTransient(f.getModifiers())).collect(Collectors.toList());
//         });
//     }
//
//     public static Map<String, Field> excludeOverrideSuperField(Field[] fields, List<Field> superFieldList) {
//         Map<String, Field> fieldMap = (Map)Stream.of(fields).collect(Collectors.toMap(Field::getName, Function.identity(), (u, v) -> {
//             throw new IllegalStateException(String.format("Duplicate key %s", u));
//         }, LinkedHashMap::new));
//         superFieldList.stream().filter((field) -> !fieldMap.containsKey(field.getName())).forEach((f) -> fieldMap.put(f.getName(), f));
//         return fieldMap;
//     }
//
//     /** @deprecated */
//     @Deprecated
//     public static boolean isPrimitiveOrWrapper(Class<?> clazz) {
//         Assert.notNull(clazz, "Class must not be null", new Object[0]);
//         return clazz.isPrimitive() || PRIMITIVE_WRAPPER_TYPE_MAP.containsKey(clazz);
//     }
//
//     public static Class<?> resolvePrimitiveIfNecessary(Class<?> clazz) {
//         return clazz.isPrimitive() && clazz != Void.TYPE ? (Class)PRIMITIVE_TYPE_TO_WRAPPER_MAP.get(clazz) : clazz;
//     }
//
//
//
//     static {
//         PRIMITIVE_WRAPPER_TYPE_MAP.put(Boolean.class, Boolean.TYPE);
//         PRIMITIVE_WRAPPER_TYPE_MAP.put(Byte.class, Byte.TYPE);
//         PRIMITIVE_WRAPPER_TYPE_MAP.put(Character.class, Character.TYPE);
//         PRIMITIVE_WRAPPER_TYPE_MAP.put(Double.class, Double.TYPE);
//         PRIMITIVE_WRAPPER_TYPE_MAP.put(Float.class, Float.TYPE);
//         PRIMITIVE_WRAPPER_TYPE_MAP.put(Integer.class, Integer.TYPE);
//         PRIMITIVE_WRAPPER_TYPE_MAP.put(Long.class, Long.TYPE);
//         PRIMITIVE_WRAPPER_TYPE_MAP.put(Short.class, Short.TYPE);
//
//         for(Map.Entry<Class<?>, Class<?>> entry : PRIMITIVE_WRAPPER_TYPE_MAP.entrySet()) {
//             PRIMITIVE_TYPE_TO_WRAPPER_MAP.put((Class)entry.getValue(), (Class)entry.getKey());
//         }
//
//     }
// }
