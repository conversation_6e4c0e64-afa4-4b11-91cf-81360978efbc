package org.ahead4.workflow.service;


import org.ahead4.workflow.domain.common.PageQuery;
import org.ahead4.workflow.domain.page.TableDataInfo;
import org.ahead4.workflow.domain.vo.WfProcNodeVo;
import org.ahead4.workflow.domain.vo.WfSubProcessInstanceVo;

import java.util.List;

/**
 * 工作流流程服务V3接口
 * 专门处理调用子流程模式的工作流
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
public interface IWfProcessV3Service {

    /**
     * 查询主流程详情（适配调用子流程）
     * 包括已完成、进行中、未开始的用户任务，对于调用子流程只展示简单的流程及实例信息
     *
     * @param procInstId 流程实例ID
     * @return 流程节点列表
     */
    List<WfProcNodeVo> queryProcessDetailsWithCallActivityHandling(String procInstId);

    /**
     * 分页查询调用子流程实例列表
     * 通过流程实例ID和调用活动ID来查询子流程实例的信息
     *
     * @param procInstId            主流程实例ID
     * @param callActivityId        调用活动ID
     * @param pageQuery             分页参数
     * @return 子流程实例分页列表
     */
    TableDataInfo<WfSubProcessInstanceVo> selectPageCallActivityInstances(String procInstId, String callActivityId, PageQuery pageQuery);

    /**
     * 查询调用子流程实例的详细任务信息
     * 通过主流程实例ID和子流程实例ID查询子流程实例的详细信息
     *
     * @param procInstId           主流程实例ID
     * @param subProcessInstanceId 子流程实例ID
     * @return 子流程实例详细信息
     */
    WfSubProcessInstanceVo queryCallActivityInstanceDetails(String procInstId, String subProcessInstanceId);

    /**
     * 动态加签部门
     *
     * @param procInstId 主流程实例ID
     * @param deptCodes  要加签的部门代码列表
     * @return 是否成功
     */
    boolean addSignDepts(String procInstId, List<String> deptCodes);

    /**
     * 动态减签部门
     *
     * @param procInstId 主流程实例ID
     * @param deptCodes  要减签的部门代码列表
     * @return 是否成功
     */
    boolean removeSignDepts(String procInstId, List<String> deptCodes);
}
