package org.ahead4.workflow.service;


import org.ahead4.workflow.core.FormConf;
import org.ahead4.workflow.core.domain.ProcessQuery;
import org.ahead4.workflow.domain.common.PageQuery;
import org.ahead4.workflow.domain.page.TableDataInfo;
import org.ahead4.workflow.domain.vo.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2022/3/24 18:57
 */
public interface IWfProcessService {

    /**
     * 查询可发起流程列表
     * @param pageQuery 分页参数
     * @return
     */
    TableDataInfo<WfDefinitionVo> selectPageStartProcessList(ProcessQuery processQuery, PageQuery pageQuery);

    /**
     * 查询可发起流程列表
     */
    List<WfDefinitionVo> selectStartProcessList(ProcessQuery processQuery);

    /**
     * 查询我的流程列表
     * @param pageQuery 分页参数
     */
    TableDataInfo<WfTaskVo> selectPageOwnProcessList(ProcessQuery processQuery, PageQuery pageQuery);

    /**
     * 查询我的流程列表
     */
    List<WfTaskVo> selectOwnProcessList(ProcessQuery processQuery);

    /**
     * 查询代办任务列表
     * @param pageQuery 分页参数
     */
    TableDataInfo<WfTaskVo> selectPageTodoProcessList(ProcessQuery processQuery, PageQuery pageQuery);

    /**
     * 查询代办任务列表
     */
    List<WfTaskVo> selectTodoProcessList(ProcessQuery processQuery);

    /**
     * 查询待签任务列表
     * @param pageQuery 分页参数
     */
    TableDataInfo<WfTaskVo> selectPageClaimProcessList(ProcessQuery processQuery, PageQuery pageQuery);

    /**
     * 查询待签任务列表
     */
    List<WfTaskVo> selectClaimProcessList(ProcessQuery processQuery);

    /**
     * 查询已办任务列表
     * @param pageQuery 分页参数
     */
    TableDataInfo<WfTaskVo> selectPageFinishedProcessList(ProcessQuery processQuery, PageQuery pageQuery);

    /**
     * 查询已办任务列表
     */
    List<WfTaskVo> selectFinishedProcessList(ProcessQuery processQuery);

    /**
     * 查询流程部署关联表单信息
     * @param definitionId 流程定义ID
     * @param deployId 部署ID
     */
    FormConf selectFormContent(String definitionId, String deployId, String procInsId);

    /**
     * 启动流程实例
     * @param procDefId 流程定义ID
     * @param variables 扩展参数
     */
    String startProcessByDefId(String procDefId, String businessKey, Map<String, Object> variables);

    /**
     * 通过DefinitionKey启动流程
     * @param procDefKey 流程定义Key
     * @param variables 扩展参数
     */
    String startProcessByDefKey(String procDefKey, String businessKey, Map<String, Object> variables);

    /**
     * 删除流程实例
     */
    void deleteProcessByIds(String[] instanceIds);


    /**
     * 读取xml文件
     * @param processDefId 流程定义ID
     */
    String queryBpmnXmlById(String processDefId);


    /**
     * 查询流程任务详情信息
     * @param procInsId 流程实例ID
     * @param taskId 任务ID
     */
    WfDetailVo queryProcessDetail(String procInsId, String taskId);

    /**
     * 根据流程实例ID查询流程详情信息
     * 包括流程定义和按执行顺序获取的所有用户任务及处理详情
     *
     * @param procInstId 流程实例ID
     * @return 流程节点列表
     */
    List<WfProcNodeVo> queryProcessDetailByProcInstId(String procInstId);

    List<WfProcNodeVo> queryProcessDetailsWithOptimizedSubProcessHandling(String procInstId);

    /**
     * 查询主流程的用户任务详情（优化版本）
     * 包括已完成、进行中、未开始的用户任务，对于子流程只展示简单的流程及实例信息
     *
     * @param procInstId 流程实例ID
     * @return 流程节点列表
     */
    List<WfProcNodeVo> queryMainProcessTaskDetails(String procInstId);

    /**
     * 分页查询子流程实例列表
     * 通过流程实例ID和子流程执行ID来查询子流程实例的信息
     *
     * @param procInstId            主流程实例ID
     * @param activityId            子流程ID
     * @param pageQuery             分页参数
     * @return 子流程实例分页列表
     */
    TableDataInfo<WfSubProcessInstanceVo> selectPageSubProcessInstances(String procInstId, String activityId, PageQuery pageQuery);

    /**
     * 查询子流程实例的详细任务信息
     * 通过主流程实例ID和子流程实例ID查询子流程实例的详细信息
     *
     * @param procInstId           主流程实例ID
     * @param subProcessInstanceId 子流程实例ID
     * @return 子流程实例详细信息
     */
    WfSubProcessInstanceVo querySubProcessInstanceDetails(String procInstId, String subProcessInstanceId);
}
