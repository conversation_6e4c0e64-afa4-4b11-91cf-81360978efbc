package org.ahead4.workflow.config;

import org.ahead4.workflow.listener.GlobalEventListener;
import lombok.AllArgsConstructor;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEventType;
import org.flowable.engine.RuntimeService;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;

/**
 * flowable全局监听配置
 *
 * <AUTHOR>
 */
@Configuration
@AllArgsConstructor
public class GlobalEventListenerConfig implements ApplicationListener<ContextRefreshedEvent> {

	private final GlobalEventListener globalEventListener;
	private final RuntimeService runtimeService;

	@Override
	public void onApplicationEvent(ContextRefreshedEvent event) {
		// 流程正常结束
		runtimeService.addEventListener(globalEventListener, FlowableEngineEventType.PROCESS_COMPLETED);

		// 新增：流程因终止结束事件的监听
		runtimeService.addEventListener(globalEventListener, FlowableEngineEventType.PROCESS_COMPLETED_WITH_TERMINATE_END_EVENT);

		// 新增：流程取消事件的监听
		runtimeService.addEventListener(globalEventListener, FlowableEngineEventType.PROCESS_CANCELLED);
	}


}

