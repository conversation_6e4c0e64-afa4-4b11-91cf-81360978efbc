package org.ahead4.workflow.enums;

/**
 * 任务操作类型枚举
 *
 * <AUTHOR>
 * @createTime 2024/01/01 00:00
 */
public enum TaskActionType {
    
    /**
     * 审批通过
     */
    COMPLETE("COMPLETE", "审批通过"),
    
    /**
     * 拒绝任务
     */
    REJECT("REJECT", "拒绝任务"),
    
    /**
     * 驳回流程
     */
    REJECT_PROCESS("REJECT_PROCESS", "驳回流程"),
    
    /**
     * 退回任务
     */
    RETURN("RETURN", "退回任务"),
    
    /**
     * 认领任务
     */
    CLAIM("CLAIM", "认领任务"),
    
    /**
     * 取消认领任务
     */
    UNCLAIM("UNCLAIM", "取消认领任务"),
    
    /**
     * 委派任务
     */
    DELEGATE("DELEGATE", "委派任务"),
    
    /**
     * 转办任务
     */
    TRANSFER("TRANSFER", "转办任务"),
    
    /**
     * 取消流程
     */
    STOP("STOP", "取消流程"),
    
    /**
     * 撤回流程
     */
    REVOKE("REVOKE", "撤回流程"),
    
    /**
     * 删除任务
     */
    DELETE("DELETE", "删除任务");
    
    private final String code;
    private final String description;
    
    TaskActionType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static TaskActionType fromCode(String code) {
        if (code == null) {
            return null;
        }
        for (TaskActionType type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 检查是否为有效的操作类型
     */
    public static boolean isValidActionType(String code) {
        return fromCode(code) != null;
    }
}