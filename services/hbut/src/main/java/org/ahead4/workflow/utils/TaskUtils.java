package org.ahead4.workflow.utils;

import cn.hutool.core.util.ObjectUtil;
import org.ahead4.security.utils.AccessHolder;
import org.ahead4.workflow.constant.TaskConstants;
import org.ahead4.workflow.spi.BizSystemAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;


import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 工作流任务工具类
 *
 * <AUTHOR>
 * @createTime 2022/4/24 12:42
 */
@Slf4j
public class TaskUtils {

    public static String getUserId() {
        final BizSystemAdapter adapter = SpringUtils.getBean(BizSystemAdapter.class);
        if (ObjectUtil.isNull(adapter)) {
            log.error("未找到BizSystemAdapter实现类");
            return "";
        }
        return adapter.getLonginUserId();
    }

    /**
     * 获取用户组信息
     *
     * @return candidateGroup
     */
    public static List<String> getCandidateGroup() {
        List<String> list = new ArrayList<>();
        final BizSystemAdapter adapter = SpringUtils.getBean(BizSystemAdapter.class);
        if (ObjectUtil.isNull(adapter)) {
            log.error("未找到BizSystemAdapter实现类");
            return list;
        }
        final List<String> loginUserRoleIds = adapter.getLoginUserRoleIds();
        if (CollectionUtils.isNotEmpty(loginUserRoleIds)) {
            loginUserRoleIds.forEach(item -> {
                list.add(TaskConstants.ROLE_GROUP_PREFIX + item);
            });
        }
        final List<String> loginUserDeptIds = adapter.getLoginUserDeptIds();
        if (CollectionUtils.isNotEmpty(loginUserDeptIds)) {
            loginUserDeptIds.forEach(item -> {
                list.add(TaskConstants.DEPT_GROUP_PREFIX + item);
            });
        }
        return list;
    }
}
