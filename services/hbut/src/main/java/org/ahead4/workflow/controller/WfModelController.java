package org.ahead4.workflow.controller;


import org.ahead4.web.presentate.HttpMsg;
import org.ahead4.workflow.domain.bo.WfModelBo;
import org.ahead4.workflow.domain.common.PageQuery;
import org.ahead4.workflow.domain.common.R;
import org.ahead4.workflow.domain.page.TableDataInfo;
import org.ahead4.workflow.domain.validate.AddGroup;
import org.ahead4.workflow.domain.validate.EditGroup;
import org.ahead4.workflow.domain.vo.WfModelVo;
import org.ahead4.workflow.service.IWfCategoryService;
import org.ahead4.workflow.service.IWfModelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 工作流流程模型管理
 *
 * <AUTHOR>
 * @createTime 2022/6/21 9:09
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/workflow/model")
public class WfModelController extends BaseController {

    private final IWfModelService modelService;
    private final IWfCategoryService categoryService;

    /**
     * 查询流程模型列表
     *
     * @param modelBo 流程模型对象
     * @param pageQuery 分页参数
     */
    
    @GetMapping("/list")
    public TableDataInfo<WfModelVo> list(WfModelBo modelBo, PageQuery pageQuery) {
        return modelService.list(modelBo, pageQuery);
    }

    /**
     * 查询流程模型列表
     *
     * @param modelBo 流程模型对象
     * @param pageQuery 分页参数
     */
    
    @GetMapping("/historyList")
    public TableDataInfo<WfModelVo> historyList(WfModelBo modelBo, PageQuery pageQuery) {
        return modelService.historyList(modelBo, pageQuery);
    }

    /**
     * 获取流程模型详细信息
     *
     * @param modelId 模型主键
     */
    
    @GetMapping(value = "/{modelId}")
    public HttpMsg getInfo(@NotNull(message = "主键不能为空") @PathVariable("modelId") String modelId) {
        return HttpMsg.ok().data(modelService.getModel(modelId));
    }

    /**
     * 获取流程表单详细信息
     *
     * @param modelId 模型主键
     */
    
    @GetMapping(value = "/bpmnXml/{modelId}")
    public HttpMsg getBpmnXml(@NotNull(message = "主键不能为空") @PathVariable("modelId") String modelId) {
        return HttpMsg.ok(modelService.queryBpmnXmlById(modelId));
    }

    /**
     * 新增流程模型
     */
    
    
    @PostMapping
    public HttpMsg add(@Validated(AddGroup.class) @RequestBody WfModelBo modelBo) {
        modelService.insertModel(modelBo);
        return HttpMsg.ok();
    }

    /**
     * 修改流程模型
     */
    
    
    @PutMapping
    public HttpMsg edit(@Validated(EditGroup.class) @RequestBody WfModelBo modelBo) {
        modelService.updateModel(modelBo);
        return HttpMsg.ok();
    }

    /**
     * 保存流程模型
     */
    
    
    
    @PostMapping("/save")
    public HttpMsg save(@RequestBody WfModelBo modelBo) {
        modelService.saveModel(modelBo);
        return HttpMsg.ok();
    }

    /**
     * 设为最新流程模型
     * @param modelId
     * @return
     */
    
    
    
    @PostMapping("/latest")
    public HttpMsg latest(@RequestParam String modelId) {
        modelService.latestModel(modelId);
        return HttpMsg.ok();
    }

    /**
     * 删除流程模型
     *
     * @param modelIds 流程模型主键串
     */
    
    
    @DeleteMapping("/{modelIds}")
    public HttpMsg remove(@NotEmpty(message = "主键不能为空") @PathVariable String[] modelIds) {
        modelService.deleteByIds(Arrays.asList(modelIds));
        return HttpMsg.ok();
    }

    /**
     * 部署流程模型
     *
     * @param modelId 流程模型主键
     */
    
    
    
    @PostMapping("/deploy")
    public HttpMsg deployModel(@RequestParam String modelId) {
        return HttpMsg.ok().data(modelService.deployModel(modelId));
    }

    /**
     * 导出流程模型数据
     */
    
    
    // @PostMapping("/export")
    // public void export(WfModelBo modelBo, HttpServletResponse response) {
    //     List<WfModelVo> list =  modelService.list(modelBo);
    //     List<WfModelExportVo> listVo = BeanUtil.copyToList(list, WfModelExportVo.class);
    //     List<WfCategoryVo> categoryVos = categoryService.queryList(new WfCategory());
    //     Map<String, String> categoryMap = categoryVos.stream()
    //         .collect(Collectors.toMap(WfCategoryVo::getCode, WfCategoryVo::getCategoryName));
    //     for (WfModelExportVo exportVo : listVo) {
    //         exportVo.setCategoryName(categoryMap.get(exportVo.getCategory()));
    //     }
    //     ExcelUtil.exportExcel(listVo, "流程模型数据", WfModelExportVo.class, response);
    // }
}
