package org.ahead4.workflow.listener;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.workflow.constant.ProcessConstants;
import org.ahead4.workflow.enums.ProcessStatus;
import org.ahead4.workflow.spi.BizSystemAdapter;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEntityEvent;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.event.AbstractFlowableEngineEventListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Flowable 全局监听器
 *
 * <AUTHOR>
 * @since 2023/3/8 22:45
 */
@Slf4j
@Component
public class GlobalEventListener extends AbstractFlowableEngineEventListener {

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private BizSystemAdapter adapter;

    /**
     * 流程结束监听器
     */
    @Override
    protected void processCompleted(FlowableEngineEntityEvent event) {
        String processInstanceId = event.getProcessInstanceId();
        Object variable = runtimeService.getVariable(processInstanceId, ProcessConstants.PROCESS_STATUS_KEY);
        final String businessId = (String) runtimeService.getVariable(processInstanceId, "id");
        ProcessStatus status = ProcessStatus.getProcessStatus(Convert.toStr(variable));
        log.info("流程状态监听器：{}-{}", processInstanceId, status);
        if (ObjectUtil.isNotNull(status) && ProcessStatus.RUNNING == status) {
            runtimeService.setVariable(processInstanceId, ProcessConstants.PROCESS_STATUS_KEY, ProcessStatus.COMPLETED.getStatus());
            // 调用业务系统适配器
            adapter.updateStatus(businessId, ProcessStatus.COMPLETED.getStatus());
        }else if (ObjectUtil.isNotNull(status) && ProcessStatus.CANCELED == status) {
            runtimeService.setVariable(processInstanceId, ProcessConstants.PROCESS_STATUS_KEY, ProcessStatus.CANCELED.getStatus());
            // 调用业务系统适配器
            adapter.updateStatus(businessId, ProcessStatus.CANCELED.getStatus());
        }else if (ObjectUtil.isNotNull(status) && ProcessStatus.TERMINATED == status){
            runtimeService.setVariable(processInstanceId, ProcessConstants.PROCESS_STATUS_KEY, ProcessStatus.TERMINATED.getStatus());
            // 调用业务系统适配器
            adapter.updateStatus(businessId, ProcessStatus.TERMINATED.getStatus());
        }
        super.processCompleted(event);
    }

}
