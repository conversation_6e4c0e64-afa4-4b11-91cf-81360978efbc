package org.ahead4.workflow.factory;

import lombok.Getter;
import org.flowable.engine.*;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * flowable 引擎注入封装
 * <AUTHOR>
 * @date 2021-04-03
 */
@Component
@Getter
public class FlowServiceFactory {

    @Resource
    protected RepositoryService repositoryService;

    @Resource
    protected RuntimeService runtimeService;

    @Resource
    protected IdentityService identityService;

    @Resource
    protected TaskService taskService;

    // 删除原有的 @Resource 和 @Qualifier 注解
    // 改为从 processEngine 获取
    protected FormService formService;

    @Resource
    protected HistoryService historyService;

    @Resource
    protected ManagementService managementService;

    @Qualifier("processEngine")
    @Resource
    protected ProcessEngine processEngine;

    @Resource
    protected ExecutionEntityManager executionEntityManager;
    
    // 在 processEngine 注入后初始化 formService
    @PostConstruct
    public void init() {
        this.formService = processEngine.getFormService();
    }
}
