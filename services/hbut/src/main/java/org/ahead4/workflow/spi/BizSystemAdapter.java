package org.ahead4.workflow.spi;

import liquibase.pro.packaged.S;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * 业务系统适配器接口
 * 用于工作流引擎与业务系统的交互
 */
public interface BizSystemAdapter {
    /**
     * 获取任务候选人
     * @param taskDefinitionKey 任务定义Key
     * @param variables 流程变量
     * @param businessKey 业务标识
     * @return 候选人列表
     */
    List<String> getTaskCandidateUsers(String taskDefinitionKey, Map<String, Object> variables, String businessKey);

    /**
     * 任务完成回调
     * @param taskDefinitionKey 任务定义Key
     * @param businessKey 业务标识
     * @param variables 流程变量
     */
    void onTaskCompleted(String taskDefinitionKey, String businessKey, Map<String, Object> variables);

    /**
     * 流程完成回调
     * @param processInstanceId 流程实例ID
     * @param businessKey 业务标识
     * @param variables 流程变量
     */
    void onProcessCompleted(String processInstanceId, String businessKey, Map<String, Object> variables);

    /**
     * 获取业务表单数据
     * @param businessKey 业务标识
     * @return 业务数据
     */
    Map<String, Object> getBusinessFormData(String scopeType, String businessKey);


    /**
     * 获取 Longin 用户
     *
     * @return {@link String }
     */
    String getLonginUserId();

    String getLoginUserName();

    /**
     * 获取登录用户角色 ID
     *
     * @return {@link List }<{@link String }>
     */
    List<String> getLoginUserRoleIds();

    /**
     * 获取登录用户部门 ID
     *
     * @return {@link List }<{@link String }>
     */
    List<String> getLoginUserDeptIds();
    /**
     * 按 ID 获取用户
     *
     * @param userId 用户 ID
    /**
     * 按 ID 获取用户名
     *
     * @param userId 用户 ID
     * @return {@link String }
     */
    String getUserNameById(String userId);

    /**
     * 按 ID 获取昵称
     *
     * @param userId 用户 ID
     * @return {@link String }
     */
    String getNickNameById(String userId);

    /**
     * 按 ID 获取角色名称
     *
     * @param roleId 角色 ID
     * @return {@link String }
     */
    String getRoleNameById(String roleId);

    /**
     * 按 ID 获取部门名称
     *
     * @param deptId 部门 ID
     * @return {@link String }
     */
    String getDeptNameById(String deptId);

    /**
     * 按角色 ID 获取用户 ID
     *
     * @param roleIds 角色 ID
     * @return {@link List }<{@link String }>
     */
    List<String> getUserIdsByRoleIds(@NotEmpty List<String> roleIds);

    /**
     * 按部门 ID 获取用户 ID
     *
     * @param deptIds 部门 ID
     * @return {@link List }<{@link String }>
     */
    List<String> getUserIdsByDeptIds(@NotEmpty List<String> deptIds);

    void updateStatus(String businessId, String status);

    /**
     * 获取业务表单流程节点部门
     *
     * @param id     身份证
     * @param taskId 任务 ID
     * @return {@link List }<{@link String }>
     */
    List<String> getDeptsInBusinessFormData(String id, String taskId);

    /**
     * 按部门和位置获取用户 ID
     *
     * @param deptCodes 部门代码
     * @param postCodes 邮政编码
     * @return {@link List }<{@link String }>
     */
    List<String> getUserIdsByDeptAndPosition(List<String> deptCodes, List<String> postCodes);
}
