package org.ahead4.workflow.factory;

import org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FlowConfig {

    @Bean
    public ExecutionEntityManager executionEntityManager(ProcessEngineConfigurationImpl processEngineConfiguration) {
        return processEngineConfiguration.getExecutionEntityManager();
    }
}
