package org.ahead4.workflow.controller;


import org.ahead4.web.presentate.HttpMsg;
import org.ahead4.workflow.core.domain.ProcessQuery;
import org.ahead4.workflow.domain.common.PageQuery;
import org.ahead4.workflow.domain.common.R;
import org.ahead4.workflow.domain.page.TableDataInfo;
import org.ahead4.workflow.domain.vo.WfDeployVo;
import org.ahead4.workflow.domain.vo.WfFormVo;
import org.ahead4.workflow.service.IWfDeployFormService;
import org.ahead4.workflow.service.IWfDeployService;
import org.ahead4.workflow.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

/**
 * 流程部署
 *
 * <AUTHOR>
 * @createTime 2022/3/24 20:57
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/workflow/deploy")
public class WfDeployController extends BaseController {

    private final IWfDeployService deployService;
    private final IWfDeployFormService deployFormService;

    /**
     * 查询流程部署列表
     */
    @GetMapping("/list")
    public TableDataInfo<WfDeployVo> list(ProcessQuery processQuery, PageQuery pageQuery) {
        return deployService.queryPageList(processQuery, pageQuery);
    }

    /**
     * 查询流程部署版本列表
     */
    
    @GetMapping("/publishList")
    public TableDataInfo<WfDeployVo> publishList(@RequestParam String processKey, PageQuery pageQuery) {
        return deployService.queryPublishList(processKey, pageQuery);
    }

    /**
     * 激活或挂起流程
     *
     * @param state 状态（active:激活 suspended:挂起）
     * @param definitionId 流程定义ID
     */
    
    @PutMapping(value = "/changeState")
    public HttpMsg changeState(@RequestParam String state, @RequestParam String definitionId) {
        deployService.updateState(definitionId, state);
        return HttpMsg.ok();
    }

    /**
     * 读取xml文件
     * @param definitionId 流程定义ID
     * @return
     */
    
    @GetMapping("/bpmnXml/{definitionId}")
    public HttpMsg getBpmnXml(@PathVariable(value = "definitionId") String definitionId) {
        return HttpMsg.ok().data( deployService.queryBpmnXmlById(definitionId));
    }

    /**
     * 删除流程模型
     * @param deployIds 流程部署ids
     */
    
    
    @DeleteMapping("/{deployIds}")
    public HttpMsg remove(@NotEmpty(message = "主键不能为空") @PathVariable String[] deployIds) {
        deployService.deleteByIds(Arrays.asList(deployIds));
        return HttpMsg.ok();
    }

    /**
     * 查询流程部署关联表单信息
     *
     * @param deployId 流程部署id
     */
    @GetMapping("/form/{deployId}")
    public HttpMsg start(@PathVariable(value = "deployId") String deployId) {
        WfFormVo formVo = deployFormService.selectDeployFormByDeployId(deployId);
        if (Objects.isNull(formVo)) {
            return HttpMsg.error("请先配置流程表单");
        }
        return HttpMsg.ok().data(JsonUtils.parseObject(formVo.getContent(), Map.class));
    }
}
