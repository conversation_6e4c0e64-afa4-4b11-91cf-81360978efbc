package org.ahead4.workflow.service;

import java.util.List;

/**
 * 工作流动态加签减签服务接口
 *
 * <AUTHOR> Assistant
 * @date 2024-12-19
 */
public interface IWfDynamicSignService {

    /**
     * 动态加签部门
     *
     * @param processInstanceId 流程实例ID
     * @param newDepts 新增部门列表
     * @param reason 加签原因
     * @return 操作结果
     */
    boolean addSignDepartments(String processInstanceId, List<String> newDepts, String reason);

    /**
     * 动态减签部门
     *
     * @param processInstanceId 流程实例ID
     * @param removeDepts 要移除的部门列表
     * @param reason 减签原因
     * @return 操作结果
     */
    boolean removeSignDepartments(String processInstanceId, List<String> removeDepts, String reason);

    /**
     * 获取当前流程的部门列表
     *
     * @param processInstanceId 流程实例ID
     * @return 部门列表
     */
    List<String> getCurrentDepartments(String processInstanceId);

    /**
     * 获取原始部门列表
     *
     * @param processInstanceId 流程实例ID
     * @return 原始部门列表
     */
    List<String> getOriginalDepartments(String processInstanceId);

    /**
     * 获取加签历史记录
     *
     * @param processInstanceId 流程实例ID
     * @return 加签历史
     */
    List<SignRecord> getAddSignHistory(String processInstanceId);

    /**
     * 获取减签历史记录
     *
     * @param processInstanceId 流程实例ID
     * @return 减签历史
     */
    List<SignRecord> getRemoveSignHistory(String processInstanceId);

    /**
     * 检查是否可以加签
     *
     * @param processInstanceId 流程实例ID
     * @param newDepts 新增部门列表
     * @return 是否可以加签
     */
    boolean canAddSign(String processInstanceId, List<String> newDepts);

    /**
     * 检查是否可以减签
     *
     * @param processInstanceId 流程实例ID
     * @param removeDepts 要移除的部门列表
     * @return 是否可以减签
     */
    boolean canRemoveSign(String processInstanceId, List<String> removeDepts);

    /**
     * 加签减签记录
     */
    class SignRecord {
        private String operationType; // ADD 或 REMOVE
        private List<String> departments;
        private String reason;
        private String operator;
        private Long operateTime;

        // getters and setters
        public String getOperationType() { return operationType; }
        public void setOperationType(String operationType) { this.operationType = operationType; }
        
        public List<String> getDepartments() { return departments; }
        public void setDepartments(List<String> departments) { this.departments = departments; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        
        public String getOperator() { return operator; }
        public void setOperator(String operator) { this.operator = operator; }
        
        public Long getOperateTime() { return operateTime; }
        public void setOperateTime(Long operateTime) { this.operateTime = operateTime; }
    }
}
