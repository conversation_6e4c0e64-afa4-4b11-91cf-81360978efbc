package org.ahead4.workflow.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.ahead4.common.core.page.PageQuery;
import org.ahead4.common.core.page.TableDataInfo;
import org.ahead4.common.web.domain.HttpMsg;
import org.ahead4.workflow.domain.vo.WfProcNodeVo;
import org.ahead4.workflow.domain.vo.WfSubProcessInstanceVo;
import org.ahead4.workflow.service.IWfProcessV3Service;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工作流流程V3控制器
 * 专门处理调用子流程模式的工作流
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Api(tags = "工作流流程V3管理")
@RestController
@RequestMapping("/workflow/process/v3")
@RequiredArgsConstructor
public class WfProcessV3Controller {

    private final IWfProcessV3Service processV3Service;

    /**
     * 根据流程实例ID查询流程详情信息（适配调用子流程）
     * 只返回主流程中的用户任务，调用子流程作为整体节点处理
     *
     * @param procInstId 流程实例ID
     */
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/detail/{procInstId}")
    @ApiOperation("根据流程实例ID查询流程详情信息（适配调用子流程）")
    public HttpMsg detailWithCallActivity(@PathVariable(value = "procInstId") String procInstId) {
        return HttpMsg.ok().data(processV3Service.queryProcessDetailsWithCallActivityHandling(procInstId));
    }

    /**
     * 分页查询调用子流程实例列表
     *
     * @param procInstId 主流程实例ID
     * @param callActivityId 调用活动ID
     * @param pageQuery 分页参数
     */
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/call-activity-instances/{procInstId}")
    @ApiOperation("分页查询调用子流程实例列表")
    public TableDataInfo<WfSubProcessInstanceVo> selectPageCallActivityInstances(
            @PathVariable(value = "procInstId") String procInstId,
            @RequestParam(value = "callActivityId") String callActivityId, 
            PageQuery pageQuery) {
        return processV3Service.selectPageCallActivityInstances(procInstId, callActivityId, pageQuery);
    }

    /**
     * 查询调用子流程实例详情
     *
     * @param procInstId 主流程实例ID
     * @param subProcessInstanceId 子流程实例ID
     */
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/call-activity-detail/{procInstId}/{subProcessInstanceId}")
    @ApiOperation("查询调用子流程实例详情")
    public HttpMsg queryCallActivityInstanceDetails(
            @PathVariable(value = "procInstId") String procInstId,
            @PathVariable(value = "subProcessInstanceId") String subProcessInstanceId) {
        return HttpMsg.ok().data(processV3Service.queryCallActivityInstanceDetails(procInstId, subProcessInstanceId));
    }

    /**
     * 动态加签部门
     *
     * @param procInstId 主流程实例ID
     * @param deptCodes 要加签的部门代码列表
     */
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/add-sign/{procInstId}")
    @ApiOperation("动态加签部门")
    public HttpMsg addSignDepts(
            @PathVariable(value = "procInstId") String procInstId,
            @RequestBody List<String> deptCodes) {
        boolean success = processV3Service.addSignDepts(procInstId, deptCodes);
        if (success) {
            return HttpMsg.ok("加签成功");
        } else {
            return HttpMsg.error("加签失败");
        }
    }

    /**
     * 动态减签部门
     *
     * @param procInstId 主流程实例ID
     * @param deptCodes 要减签的部门代码列表
     */
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/remove-sign/{procInstId}")
    @ApiOperation("动态减签部门")
    public HttpMsg removeSignDepts(
            @PathVariable(value = "procInstId") String procInstId,
            @RequestBody List<String> deptCodes) {
        boolean success = processV3Service.removeSignDepts(procInstId, deptCodes);
        if (success) {
            return HttpMsg.ok("减签成功");
        } else {
            return HttpMsg.error("减签失败");
        }
    }

    /**
     * 获取流程实例的加签减签历史
     *
     * @param procInstId 主流程实例ID
     */
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/sign-history/{procInstId}")
    @ApiOperation("获取流程实例的加签减签历史")
    public HttpMsg getSignHistory(@PathVariable(value = "procInstId") String procInstId) {
        // TODO: 实现加签减签历史查询
        return HttpMsg.ok("功能开发中");
    }

    /**
     * 获取可用于加签的部门列表
     *
     * @param procInstId 主流程实例ID
     */
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/available-depts/{procInstId}")
    @ApiOperation("获取可用于加签的部门列表")
    public HttpMsg getAvailableDepts(@PathVariable(value = "procInstId") String procInstId) {
        // TODO: 实现可用部门列表查询
        return HttpMsg.ok("功能开发中");
    }

    /**
     * 批量操作调用子流程实例
     *
     * @param procInstId 主流程实例ID
     * @param operation 操作类型（suspend/activate/terminate）
     * @param subProcessInstanceIds 子流程实例ID列表
     */
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/batch-operation/{procInstId}")
    @ApiOperation("批量操作调用子流程实例")
    public HttpMsg batchOperateSubProcesses(
            @PathVariable(value = "procInstId") String procInstId,
            @RequestParam(value = "operation") String operation,
            @RequestBody List<String> subProcessInstanceIds) {
        // TODO: 实现批量操作功能
        return HttpMsg.ok("功能开发中");
    }
}
