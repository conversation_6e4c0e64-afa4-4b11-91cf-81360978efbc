package org.ahead4.workflow.spi;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

public class DefaultBizSystemAdapterImpl implements BizSystemAdapter {
    private static final Random random = new Random();

    @Override
    public List<String> getTaskCandidateUsers(String taskDefinitionKey, Map<String, Object> variables, String businessKey) {
        // 生成随机用户列表
        List<String> users = new ArrayList<>();
        int count = random.nextInt(5); // 随机生成0到4个用户
        for (int i = 0; i < count; i++) {
            users.add("User" + random.nextInt(100));
        }
        return users;
    }

    @Override
    public void onTaskCompleted(String taskDefinitionKey, String businessKey, Map<String, Object> variables) {
        // 默认实现不做任何操作
    }

    @Override
    public void onProcessCompleted(String processInstanceId, String businessKey, Map<String, Object> variables) {
        // 默认实现不做任何操作
    }

    @Override
    public Map<String, Object> getBusinessFormData(String scopeType, String businessKey) {
        // 生成随机业务表单数据
        Map<String, Object> formData = new HashMap<>();
        int count = random.nextInt(3); // 随机生成0到2个键值对
        for (int i = 0; i < count; i++) {
            formData.put("Field" + i, "Value" + random.nextInt(100));
        }
        return formData;
    }

    @Override
    public String getLonginUserId() {
        // 返回随机用户ID
        return "User" + random.nextInt(1000);
    }

    @Override
    public String getLoginUserName() {
        // 返回随机用户名
        return "Name" + random.nextInt(1000);
    }

    @Override
    public List<String> getLoginUserRoleIds() {
        // 生成随机角色ID列表
        List<String> roleIds = new ArrayList<>();
        int count = random.nextInt(5); // 随机生成0到4个角色ID
        for (int i = 0; i < count; i++) {
            roleIds.add("Role" + random.nextInt(100));
        }
        return roleIds;
    }

    @Override
    public List<String> getLoginUserDeptIds() {
        // 生成随机部门ID列表
        List<String> deptIds = new ArrayList<>();
        int count = random.nextInt(5); // 随机生成0到4个部门ID
        for (int i = 0; i < count; i++) {
            deptIds.add("Dept" + random.nextInt(100));
        }
        return deptIds;
    }

    @Override
    public String getUserNameById(String userId) {
        // 返回随机用户名
        return "Name" + random.nextInt(1000);
    }

    @Override
    public String getNickNameById(String userId) {
        // 返回随机昵称
        return "Nick" + random.nextInt(1000);
    }

    @Override
    public String getRoleNameById(String roleId) {
        // 返回随机角色名
        return "RoleName" + random.nextInt(1000);
    }

    @Override
    public String getDeptNameById(String deptId) {
        // 返回随机部门名
        return "DeptName" + random.nextInt(1000);
    }

    @Override
    public List<String> getUserIdsByRoleIds(List<String> roleIds) {
        // 生成随机用户ID列表
        List<String> userIds = new ArrayList<>();
        int count = random.nextInt(5); // 随机生成0到4个用户ID
        for (int i = 0; i < count; i++) {
            userIds.add(String.valueOf(random.nextInt(1000))); // 随机生成用户ID
        }
        return userIds;
    }

    @Override
    public List<String> getUserIdsByDeptIds(List<String> deptIds) {
        // 生成随机用户ID列表
        List<String> userIds = new ArrayList<>();
        int count = random.nextInt(5); // 随机生成0到4个用户ID
        for (int i = 0; i < count; i++) {
            userIds.add(String.valueOf(random.nextInt(1000))); // 随机生成用户ID
        }
        return userIds;
    }

    @Override
    public void updateStatus(String businessId, String status) {
    }

    @Override
    public List<String> getDeptsInBusinessFormData(String id, String taskId) {
        return Collections.emptyList();
    }

    @Override
    public List<String> getUserIdsByDeptAndPosition(List<String> deptCodes, List<String> postCodes) {
        return Collections.emptyList();
    }
}