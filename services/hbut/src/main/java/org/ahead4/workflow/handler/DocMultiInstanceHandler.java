package org.ahead4.workflow.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.workflow.constant.ProcessConstants;
import org.ahead4.workflow.spi.BizSystemAdapter;
import org.apache.commons.collections4.CollectionUtils;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
/**
 * 多实例处理类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Component("docMultiInstanceHandler")
public class DocMultiInstanceHandler {

    private final BizSystemAdapter adapter;


    //
    // public Set<String> getUserIds(DelegateExecution execution) {
    //     Set<String> candidateUserIds = new LinkedHashSet<>();
    //     FlowElement flowElement = execution.getCurrentFlowElement();
    //     if (ObjectUtil.isNotEmpty(flowElement) && flowElement instanceof UserTask) {
    //         UserTask userTask = (UserTask) flowElement;
    //         String dataType = userTask.getAttributeValue(ProcessConstants.NAMASPASE, ProcessConstants.PROCESS_CUSTOM_DATA_TYPE);
    //         if ("USERS".equals(dataType) && CollUtil.isNotEmpty(userTask.getCandidateUsers())) {
    //             // 添加候选用户id
    //             candidateUserIds.addAll(userTask.getCandidateUsers());
    //         } else if (CollUtil.isNotEmpty(userTask.getCandidateGroups())) {
    //             // 获取组的ID，角色ID集合或部门ID集合
    //             List<Long> groups = userTask.getCandidateGroups().stream()
    //                 .map(item -> Long.parseLong(item.substring(4)))
    //                 .collect(Collectors.toList());
    //             List<String> userIds = new ArrayList<>();
    //             final BizSystemAdapter adapter = SpringUtils.getBean(BizSystemAdapter.class);
    //             if (ObjectUtil.isNull(adapter)) {
    //                 log.warn("未找到BizSystemAdapter实现类");
    //                 return candidateUserIds;
    //             }
    //             if ("ROLES".equals(dataType)) {
    //                 // 通过角色id，获取所有用户id集合
    //                 userIds = adapter.getUserIdsByRoleIds(groups);
    //                 // LambdaQueryWrapper<SysUserRole> lqw = Wrappers.lambdaQuery(SysUserRole.class).select(SysUserRole::getUserId).in(SysUserRole::getRoleId, groups);
    //                 // userIds = SimpleQuery.list(lqw, SysUserRole::getUserId);
    //             } else if ("DEPTS".equals(dataType)) {
    //                 userIds = adapter.getUserIdsByDeptIds(groups);
    //                 // 通过部门id，获取所有用户id集合
    //                 // LambdaQueryWrapper<SysUser> lqw = Wrappers.lambdaQuery(SysUser.class).select(SysUser::getUserId).in(SysUser::getDeptId, groups);
    //                 // userIds = SimpleQuery.list(lqw, SysUser::getUserId);
    //             }
    //             // 添加候选用户id
    //             candidateUserIds.addAll(userIds);
    //         }
    //     }
    //     return candidateUserIds;
    // }
}
