package org.ahead4.workflow.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.ahead4.cdes.entity.dto.CCDto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 流程任务业务对象
 *
 * <AUTHOR>
 * @createTime 2022/3/10 00:12
 */
@Data
@Builder
@AllArgsConstructor
public class WfTaskBo implements Serializable {
    /**
     * 任务Id
     */
    private String taskId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 用户Id
     */
    private String userId;
    /**
     * 任务意见
     */
    private String comment;
    /**
     * 流程实例Id
     */
    private String procInstId;
    /**
     * 节点
     */
    private String targetKey;
    /**
     * 流程变量信息
     */
    private Map<String, Object> variables;
    /**
     * 审批人
     */
    private String assignee;
    /**
     * 候选人
     */
    private List<String> candidateUsers;
    /**
     * 审批组
     */
    private List<String> candidateGroups;
    /**
     * 抄送用户Id
     */
//    private String copyUserIds;
    private List<CCDto> makeCopy;
    /**
     * 下一节点审批人
     */
    private String nextUserIds;
    
    /**
     * 操作类型：COMPLETE-审批通过, REJECT-拒绝, REJECT_PROCESS-驳回流程, RETURN-退回, CLAIM-认领, UNCLAIM-取消认领, 
     * DELEGATE-委派, TRANSFER-转办, STOP-取消流程, REVOKE-撤回流程, DELETE-删除任务
     */
    private String actionType;
}
