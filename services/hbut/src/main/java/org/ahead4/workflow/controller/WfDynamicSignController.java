package org.ahead4.workflow.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.workflow.domain.common.R;
import org.ahead4.workflow.service.IWfDynamicSignService;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 工作流动态加签减签控制器
 *
 * <AUTHOR> Assistant
 * @date 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/workflow/dynamic-sign")
@RequiredArgsConstructor
@Api(tags = "工作流动态加签减签")
public class WfDynamicSignController {

    private final IWfDynamicSignService dynamicSignService;

    @PostMapping("/add-sign")
    @ApiOperation("动态加签部门")
    public R<Boolean> addSignDepartments(@Valid @RequestBody AddSignRequest request) {
        try {
            boolean result = dynamicSignService.addSignDepartments(
                    request.getProcessInstanceId(),
                    request.getNewDepts(),
                    request.getReason()
            );
            
            if (result) {
                return R.ok("加签成功", true);
            } else {
                return R.fail("加签失败");
            }
        } catch (Exception e) {
            log.error("加签部门失败: {}", e.getMessage(), e);
            return R.fail("加签失败: " + e.getMessage());
        }
    }

    @PostMapping("/remove-sign")
    @ApiOperation("动态减签部门")
    public R<Boolean> removeSignDepartments(@Valid @RequestBody RemoveSignRequest request) {
        try {
            boolean result = dynamicSignService.removeSignDepartments(
                    request.getProcessInstanceId(),
                    request.getRemoveDepts(),
                    request.getReason()
            );
            
            if (result) {
                return R.ok("减签成功", true);
            } else {
                return R.fail("减签失败");
            }
        } catch (Exception e) {
            log.error("减签部门失败: {}", e.getMessage(), e);
            return R.fail("减签失败: " + e.getMessage());
        }
    }

    @GetMapping("/current-departments/{processInstanceId}")
    @ApiOperation("获取当前流程的部门列表")
    public R<List<String>> getCurrentDepartments(
            @ApiParam("流程实例ID") @PathVariable String processInstanceId) {
        try {
            List<String> departments = dynamicSignService.getCurrentDepartments(processInstanceId);
            return R.ok(departments);
        } catch (Exception e) {
            log.error("获取当前部门列表失败: {}", e.getMessage(), e);
            return R.fail("获取部门列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/original-departments/{processInstanceId}")
    @ApiOperation("获取原始部门列表")
    public R<List<String>> getOriginalDepartments(
            @ApiParam("流程实例ID") @PathVariable String processInstanceId) {
        try {
            List<String> departments = dynamicSignService.getOriginalDepartments(processInstanceId);
            return R.ok(departments);
        } catch (Exception e) {
            log.error("获取原始部门列表失败: {}", e.getMessage(), e);
            return R.fail("获取原始部门列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/add-sign-history/{processInstanceId}")
    @ApiOperation("获取加签历史记录")
    public R<List<IWfDynamicSignService.SignRecord>> getAddSignHistory(
            @ApiParam("流程实例ID") @PathVariable String processInstanceId) {
        try {
            List<IWfDynamicSignService.SignRecord> history = dynamicSignService.getAddSignHistory(processInstanceId);
            return R.ok(history);
        } catch (Exception e) {
            log.error("获取加签历史失败: {}", e.getMessage(), e);
            return R.fail("获取加签历史失败: " + e.getMessage());
        }
    }

    @GetMapping("/remove-sign-history/{processInstanceId}")
    @ApiOperation("获取减签历史记录")
    public R<List<IWfDynamicSignService.SignRecord>> getRemoveSignHistory(
            @ApiParam("流程实例ID") @PathVariable String processInstanceId) {
        try {
            List<IWfDynamicSignService.SignRecord> history = dynamicSignService.getRemoveSignHistory(processInstanceId);
            return R.ok(history);
        } catch (Exception e) {
            log.error("获取减签历史失败: {}", e.getMessage(), e);
            return R.fail("获取减签历史失败: " + e.getMessage());
        }
    }

    @PostMapping("/can-add-sign")
    @ApiOperation("检查是否可以加签")
    public R<Boolean> canAddSign(@Valid @RequestBody CanAddSignRequest request) {
        try {
            boolean canAdd = dynamicSignService.canAddSign(
                    request.getProcessInstanceId(),
                    request.getNewDepts()
            );
            return R.ok(canAdd);
        } catch (Exception e) {
            log.error("检查加签权限失败: {}", e.getMessage(), e);
            return R.fail("检查加签权限失败: " + e.getMessage());
        }
    }

    @PostMapping("/can-remove-sign")
    @ApiOperation("检查是否可以减签")
    public R<Boolean> canRemoveSign(@Valid @RequestBody CanRemoveSignRequest request) {
        try {
            boolean canRemove = dynamicSignService.canRemoveSign(
                    request.getProcessInstanceId(),
                    request.getRemoveDepts()
            );
            return R.ok(canRemove);
        } catch (Exception e) {
            log.error("检查减签权限失败: {}", e.getMessage(), e);
            return R.fail("检查减签权限失败: " + e.getMessage());
        }
    }

    /**
     * 加签请求
     */
    public static class AddSignRequest {
        @NotBlank(message = "流程实例ID不能为空")
        private String processInstanceId;
        
        @NotEmpty(message = "新增部门列表不能为空")
        private List<String> newDepts;
        
        private String reason;

        // getters and setters
        public String getProcessInstanceId() { return processInstanceId; }
        public void setProcessInstanceId(String processInstanceId) { this.processInstanceId = processInstanceId; }
        
        public List<String> getNewDepts() { return newDepts; }
        public void setNewDepts(List<String> newDepts) { this.newDepts = newDepts; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    /**
     * 减签请求
     */
    public static class RemoveSignRequest {
        @NotBlank(message = "流程实例ID不能为空")
        private String processInstanceId;
        
        @NotEmpty(message = "移除部门列表不能为空")
        private List<String> removeDepts;
        
        private String reason;

        // getters and setters
        public String getProcessInstanceId() { return processInstanceId; }
        public void setProcessInstanceId(String processInstanceId) { this.processInstanceId = processInstanceId; }
        
        public List<String> getRemoveDepts() { return removeDepts; }
        public void setRemoveDepts(List<String> removeDepts) { this.removeDepts = removeDepts; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    /**
     * 检查加签权限请求
     */
    public static class CanAddSignRequest {
        @NotBlank(message = "流程实例ID不能为空")
        private String processInstanceId;
        
        @NotEmpty(message = "新增部门列表不能为空")
        private List<String> newDepts;

        // getters and setters
        public String getProcessInstanceId() { return processInstanceId; }
        public void setProcessInstanceId(String processInstanceId) { this.processInstanceId = processInstanceId; }
        
        public List<String> getNewDepts() { return newDepts; }
        public void setNewDepts(List<String> newDepts) { this.newDepts = newDepts; }
    }

    /**
     * 检查减签权限请求
     */
    public static class CanRemoveSignRequest {
        @NotBlank(message = "流程实例ID不能为空")
        private String processInstanceId;
        
        @NotEmpty(message = "移除部门列表不能为空")
        private List<String> removeDepts;

        // getters and setters
        public String getProcessInstanceId() { return processInstanceId; }
        public void setProcessInstanceId(String processInstanceId) { this.processInstanceId = processInstanceId; }
        
        public List<String> getRemoveDepts() { return removeDepts; }
        public void setRemoveDepts(List<String> removeDepts) { this.removeDepts = removeDepts; }
    }
}
