package org.ahead4.workflow.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 任务驳回事件监听器
 *
 * <AUTHOR>
 * @date 2025/05/10
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CusDocFlowEventListener {

    private final TaskService taskService;


    /**
     * 任务驳回监听器 事件
     *
     * @param event 事件
     */
    @EventListener
    public void onTaskRejectEvent(TaskRejectEvent event) {
        log.info("任务被驳回：" +
                "原任务ID=" + event.getTaskId() +
                ", 驳回至任务ID=" + event.getTargetTaskId() +
                ", 活动ID=" + event.getActivityId());
        // todo 调用业务系统适配器,更新业务状态
        // 获取目标任务节点信息
        final Task task = taskService.createTaskQuery().processInstanceId(event.getProcessInstanceId())
                .taskId(event.getTargetTaskId()).singleResult();
        if (Objects.isNull(task)) {
            return;
        }
//        final String description = task.g();


    }

//    @EventListener
//    public void otherEvent(ProcessStatusEvent event) {
//
//    }

}
