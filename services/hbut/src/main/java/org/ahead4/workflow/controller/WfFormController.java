package org.ahead4.workflow.controller;

import org.ahead4.web.presentate.HttpMsg;
import org.ahead4.workflow.domain.WfDeployForm;
import org.ahead4.workflow.domain.bo.WfFormBo;
import org.ahead4.workflow.domain.common.PageQuery;
import org.ahead4.workflow.domain.common.R;
import org.ahead4.workflow.domain.page.TableDataInfo;
import org.ahead4.workflow.domain.validate.QueryGroup;
import org.ahead4.workflow.domain.vo.WfFormVo;
import org.ahead4.workflow.service.IWfDeployFormService;
import org.ahead4.workflow.service.IWfFormService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 流程表单Controller
 *
 * <AUTHOR>
 * @createTime 2022/3/7 22:07
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/workflow/form")
public class WfFormController extends BaseController {

    private final IWfFormService formService;

    private final IWfDeployFormService deployFormService;

    /**
     * 查询流程表单列表
     */
    
    @GetMapping("/list")
    public TableDataInfo<WfFormVo> list(@Validated(QueryGroup.class) WfFormBo bo, PageQuery pageQuery) {
        return formService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出流程表单列表
     */
    
    
    // @PostMapping("/export")
    // public void export(@Validated WfFormBo bo, HttpServletResponse response) {
    //     List<WfFormVo> list = formService.queryList(bo);
    //     ExcelUtil.exportExcel(list, "流程表单", WfFormVo.class, response);
    // }

    /**
     * 获取流程表单详细信息
     * @param formId 主键
     */
    
    @GetMapping(value = "/{formId}")
    public HttpMsg getInfo(@NotNull(message = "主键不能为空") @PathVariable("formId") Long formId) {
        return HttpMsg.ok().data(formService.queryById(formId));
    }

    /**
     * 新增流程表单
     */
    
    
    @PostMapping
    public HttpMsg add(@RequestBody WfFormBo bo) {
        return HttpMsg.ok().data(formService.insertForm(bo));
    }

    /**
     * 修改流程表单
     */
    
    
    @PutMapping
    public HttpMsg edit(@RequestBody WfFormBo bo) {
        return HttpMsg.ok().data(formService.updateForm(bo));
    }

    /**
     * 删除流程表单
     * @param formIds 主键串
     */
    
    
    @DeleteMapping("/{formIds}")
    public HttpMsg remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] formIds) {
        return HttpMsg.ok().data(formService.deleteWithValidByIds(Arrays.asList(formIds)) ? 1 : 0);
    }


    /**
     * 挂载流程表单
     */
    
    @PostMapping("/addDeployForm")
    public HttpMsg addDeployForm(@RequestBody WfDeployForm deployForm) {
        return HttpMsg.ok().data(deployFormService.insertWfDeployForm(deployForm));
    }
}
