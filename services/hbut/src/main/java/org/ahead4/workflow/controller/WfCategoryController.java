package org.ahead4.workflow.controller;



import org.ahead4.web.presentate.HttpMsg;
import org.ahead4.workflow.domain.WfCategory;
import org.ahead4.workflow.domain.common.PageQuery;
import org.ahead4.workflow.domain.common.R;
import org.ahead4.workflow.domain.page.TableDataInfo;
import org.ahead4.workflow.domain.vo.WfCategoryVo;
import org.ahead4.workflow.service.IWfCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import java.util.Arrays;
import java.util.List;

/**
 * 流程分类Controller
 *
 * <AUTHOR>
 * @createTime 2022/3/10 00:12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/workflow/category")
public class WfCategoryController extends BaseController {

    private final IWfCategoryService categoryService;

    /**
     * 查询流程分类列表
     */
    @GetMapping("/list")
    public TableDataInfo<WfCategoryVo> list(WfCategory category, PageQuery pageQuery) {
        return categoryService.queryPageList(category, pageQuery);
    }

    /**
     * 查询全部的流程分类列表
     */
    @GetMapping("/listAll")
    public HttpMsg listAll(WfCategory category) {
        return HttpMsg.ok().data(categoryService.queryList(category));
    }

    /**
     * 导出流程分类列表
     */
    // @PostMapping("/export")
    // public void export(@Validated WfCategory category, HttpServletResponse response) {
    //     List<WfCategoryVo> list = categoryService.queryList(category);
    //     ExcelUtil.exportExcel(list, "流程分类", WfCategoryVo.class, response);
    // }

    /**
     * 获取流程分类详细信息
     * @param categoryId 分类主键
     */
    @GetMapping("/{categoryId}")
    public HttpMsg getInfo(@NotNull(message = "主键不能为空") @PathVariable("categoryId") Long categoryId) {
        return HttpMsg.ok().data(categoryService.queryById(categoryId));
    }

    /**
     * 新增流程分类
     */
    
    
    @PostMapping()
    public HttpMsg add(@Validated @RequestBody WfCategory category) {
        if (!categoryService.checkCategoryCodeUnique(category)) {
            return HttpMsg.error( "新增流程分类'" + category.getCategoryName() + "'失败，流程编码已存在");
        }
        return HttpMsg.ok().data(categoryService.insertCategory(category));
    }

    /**
     * 修改流程分类
     */
    
    

    @PutMapping()
    public R<Void> edit(@Validated @RequestBody WfCategory category) {
        if (!categoryService.checkCategoryCodeUnique(category)) {
            return R.fail("修改流程分类'" + category.getCategoryName() + "'失败，流程编码已存在");
        }
        return toAjax(categoryService.updateCategory(category));
    }

    /**
     * 删除流程分类
     * @param categoryIds 分类主键串
     */
    
    
    @DeleteMapping("/{categoryIds}")
    public HttpMsg remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] categoryIds) {
        return HttpMsg.ok().data(categoryService.deleteWithValidByIds(Arrays.asList(categoryIds), true));
    }
}
