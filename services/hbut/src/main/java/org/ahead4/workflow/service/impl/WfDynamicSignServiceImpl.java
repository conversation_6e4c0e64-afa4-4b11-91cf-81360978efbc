package org.ahead4.workflow.service.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.workflow.handler.DocFlowV2BusinessHandler;
import org.ahead4.workflow.service.IWfDynamicSignService;
import org.ahead4.workflow.utils.TaskUtils;
import org.flowable.engine.RuntimeService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作流动态加签减签服务实现
 *
 * <AUTHOR> Assistant
 * @date 2024-12-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WfDynamicSignServiceImpl implements IWfDynamicSignService {

    private final RuntimeService runtimeService;
    private final DocFlowV2BusinessHandler docFlowV2WfHandler;

    @Override
    public boolean addSignDepartments(String processInstanceId, List<String> newDepts, String reason) {
        if (CollUtil.isEmpty(newDepts)) {
            log.warn("新增部门列表为空，无法加签");
            return false;
        }

        try {
            // 检查是否可以加签
            if (!canAddSign(processInstanceId, newDepts)) {
                log.warn("当前状态不允许加签，流程实例: {}", processInstanceId);
                return false;
            }

            // 执行加签操作
            boolean result = docFlowV2WfHandler.addSignDepartments(processInstanceId, newDepts);
            
            if (result) {
                // 记录加签历史
                recordSignOperation(processInstanceId, "ADD", newDepts, reason);
                log.info("成功加签部门: {}, 流程实例: {}, 原因: {}", newDepts, processInstanceId, reason);
            }
            
            return result;
        } catch (Exception e) {
            log.error("加签部门失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean removeSignDepartments(String processInstanceId, List<String> removeDepts, String reason) {
        if (CollUtil.isEmpty(removeDepts)) {
            log.warn("移除部门列表为空，无法减签");
            return false;
        }

        try {
            // 检查是否可以减签
            if (!canRemoveSign(processInstanceId, removeDepts)) {
                log.warn("当前状态不允许减签，流程实例: {}", processInstanceId);
                return false;
            }

            // 执行减签操作
            boolean result = docFlowV2WfHandler.removeSignDepartments(processInstanceId, removeDepts);
            
            if (result) {
                // 记录减签历史
                recordSignOperation(processInstanceId, "REMOVE", removeDepts, reason);
                log.info("成功减签部门: {}, 流程实例: {}, 原因: {}", removeDepts, processInstanceId, reason);
            }
            
            return result;
        } catch (Exception e) {
            log.error("减签部门失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<String> getCurrentDepartments(String processInstanceId) {
        try {
            Map<String, Object> variables = runtimeService.getVariables(processInstanceId);
            @SuppressWarnings("unchecked")
            List<String> currentDepts = (List<String>) variables.get("dynamicDepts");
            return currentDepts != null ? currentDepts : Collections.emptyList();
        } catch (Exception e) {
            log.error("获取当前部门列表失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> getOriginalDepartments(String processInstanceId) {
        try {
            Map<String, Object> variables = runtimeService.getVariables(processInstanceId);
            @SuppressWarnings("unchecked")
            List<String> originalDepts = (List<String>) variables.get("originalDepts");
            return originalDepts != null ? originalDepts : Collections.emptyList();
        } catch (Exception e) {
            log.error("获取原始部门列表失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<SignRecord> getAddSignHistory(String processInstanceId) {
        return getSignHistory(processInstanceId, "ADD");
    }

    @Override
    public List<SignRecord> getRemoveSignHistory(String processInstanceId) {
        return getSignHistory(processInstanceId, "REMOVE");
    }

    @Override
    public boolean canAddSign(String processInstanceId, List<String> newDepts) {
        try {
            // 检查流程是否还在运行
            if (!isProcessActive(processInstanceId)) {
                log.warn("流程已结束，无法加签: {}", processInstanceId);
                return false;
            }

            // 检查是否有重复部门
            List<String> currentDepts = getCurrentDepartments(processInstanceId);
            for (String dept : newDepts) {
                if (currentDepts.contains(dept)) {
                    log.warn("部门 {} 已存在，无法重复加签", dept);
                    return false;
                }
            }

            // 检查是否在允许加签的节点
            return isInAddSignAllowedNode(processInstanceId);
            
        } catch (Exception e) {
            log.error("检查加签权限失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean canRemoveSign(String processInstanceId, List<String> removeDepts) {
        try {
            // 检查流程是否还在运行
            if (!isProcessActive(processInstanceId)) {
                log.warn("流程已结束，无法减签: {}", processInstanceId);
                return false;
            }

            // 检查要移除的部门是否存在
            List<String> currentDepts = getCurrentDepartments(processInstanceId);
            for (String dept : removeDepts) {
                if (!currentDepts.contains(dept)) {
                    log.warn("部门 {} 不存在，无法减签", dept);
                    return false;
                }
            }

            // 检查减签后是否还有部门
            if (currentDepts.size() <= removeDepts.size()) {
                log.warn("减签后将没有部门处理，不允许减签");
                return false;
            }

            // 检查是否在允许减签的节点
            return isInRemoveSignAllowedNode(processInstanceId);
            
        } catch (Exception e) {
            log.error("检查减签权限失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 记录加签减签操作
     */
    private void recordSignOperation(String processInstanceId, String operationType, List<String> departments, String reason) {
        try {
            Map<String, Object> variables = runtimeService.getVariables(processInstanceId);
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> signHistory = (List<Map<String, Object>>) variables.get("signHistory");
            if (signHistory == null) {
                signHistory = new ArrayList<>();
            }

            Map<String, Object> record = new HashMap<>();
            record.put("operationType", operationType);
            record.put("departments", departments);
            record.put("reason", reason);
            record.put("operator", TaskUtils.getUserId());
            record.put("operateTime", System.currentTimeMillis());

            signHistory.add(record);
            runtimeService.setVariable(processInstanceId, "signHistory", signHistory);
            
        } catch (Exception e) {
            log.error("记录加签减签操作失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取加签减签历史
     */
    private List<SignRecord> getSignHistory(String processInstanceId, String operationType) {
        try {
            Map<String, Object> variables = runtimeService.getVariables(processInstanceId);
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> signHistory = (List<Map<String, Object>>) variables.get("signHistory");
            
            if (signHistory == null) {
                return Collections.emptyList();
            }

            return signHistory.stream()
                    .filter(record -> operationType.equals(record.get("operationType")))
                    .map(this::convertToSignRecord)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("获取加签减签历史失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 转换为SignRecord对象
     */
    private SignRecord convertToSignRecord(Map<String, Object> record) {
        SignRecord signRecord = new SignRecord();
        signRecord.setOperationType((String) record.get("operationType"));
        @SuppressWarnings("unchecked")
        List<String> departments = (List<String>) record.get("departments");
        signRecord.setDepartments(departments);
        signRecord.setReason((String) record.get("reason"));
        signRecord.setOperator((String) record.get("operator"));
        signRecord.setOperateTime((Long) record.get("operateTime"));
        return signRecord;
    }

    /**
     * 检查流程是否还在运行
     */
    private boolean isProcessActive(String processInstanceId) {
        try {
            return runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .active()
                    .count() > 0;
        } catch (Exception e) {
            log.error("检查流程状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查是否在允许加签的节点
     */
    private boolean isInAddSignAllowedNode(String processInstanceId) {
        // 这里可以根据具体业务需求实现
        // 例如：只有在特定节点才允许加签
        return true; // 暂时允许所有节点加签
    }

    /**
     * 检查是否在允许减签的节点
     */
    private boolean isInRemoveSignAllowedNode(String processInstanceId) {
        // 这里可以根据具体业务需求实现
        // 例如：只有在特定节点才允许减签
        return true; // 暂时允许所有节点减签
    }
}
