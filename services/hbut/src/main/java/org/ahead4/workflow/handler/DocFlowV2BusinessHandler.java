package org.ahead4.workflow.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.cdes.entity.dto.CCDto;
import org.ahead4.cdes.service.DocFlowService;
import org.ahead4.cdes.service.SmsService;
import org.ahead4.permission.service.UserService;
import org.ahead4.workflow.spi.BizSystemAdapter;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.delegate.TaskListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("docFlowV2WfHandler")
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class DocFlowV2BusinessHandler implements TaskListener {

    private final DocFlowService docFlowService;

    private final BizSystemAdapter adapter;

    private final UserService userService;

    private final SmsService smsService;

    private final RuntimeService runtimeService;


    /**
     * 获取选中的去重后的部门列表
     *
     * @param execution 执行上下文
     * @return 部门列表
     */
    public List<String> getDistinctDepts(DelegateExecution execution, JSONArray ccDtos) {
        if (ObjectUtil.isEmpty(ccDtos)) {
            return Collections.emptyList();
        }
        List<CCDto> dtos = ccDtos.toJavaList(CCDto.class);
        return dtos.stream().map(CCDto::getDeptCode).distinct().collect(Collectors.toList());
    }

    /**
     * 由流程设计引用
     * 获取动态部门列表（支持加签减签）
     *
     * @param execution 执行上下文
     * @param ccDtos 原始部门数据
     * @return 动态调整后的部门列表
     */
    public List<String> getDynamicDepts(DelegateExecution execution, JSONArray ccDtos) {
        // 获取初始部门列表
        List<String> initialDepts = getDistinctDepts(execution, ccDtos);

        // 检查是否有动态调整
        Object dynamicDeptsObj = execution.getVariable("dynamicDepts");
        if (dynamicDeptsObj == null) {
            // 第一次执行，保存初始部门列表
            execution.setVariable("dynamicDepts", new ArrayList<>(initialDepts));
            execution.setVariable("originalDepts", new ArrayList<>(initialDepts));
            return initialDepts;
        }

        // 返回动态调整后的部门列表
        @SuppressWarnings("unchecked")
        List<String> dynamicDepts = (List<String>) dynamicDeptsObj;
        return dynamicDepts;
    }

    /**
     * @param execution 执行
     * @param deptCode  部门代码
     * @param ccDtos    抄送 DTO
     * @param markCode  用户类型
     * @return {@link List }<{@link CCDto }>
     */
    public List<CCDto> getUserByDeptCode(DelegateExecution execution, String deptCode, JSONArray ccDtos, String markCode) {
        // ccdtos 转换为map, 以deptCode为key List<CCDto> 为value
        List<CCDto> dtos = ccDtos.toJavaList(CCDto.class);
        if (ObjectUtil.isEmpty(dtos)) {
            return Collections.emptyList();
        }
        if (StringUtils.isBlank(markCode)) {
            markCode = "leader";
        }
        // list 转 map<deptCode, List<CCDto>>
        // 按照部门分组
        String finalMarkCode = markCode;
        Map<String, List<CCDto>> deptMap = dtos.stream().filter(c -> c.getMarkCode().equals(finalMarkCode)).collect(Collectors.groupingBy(CCDto::getDeptCode, LinkedHashMap::new, Collectors.toList()));
        return deptMap.get(deptCode);
    }

    /**
     * 动态添加部门到处理列表
     *
     * @param execution 执行上下文
     * @param newDepts  新增部门列表
     */
//    public void addDepartments(DelegateExecution execution, List<String> newDepts) {
//        if (CollUtil.isEmpty(newDepts)) {
//            return;
//        }
//
//        List<String> currentDepts = getSelectedDepts(execution);
//        Set<String> allDepts = new LinkedHashSet<>(currentDepts);
//        allDepts.addAll(newDepts);
//
//        execution.setVariable("selectedDepts", new ArrayList<>(allDepts));
//        execution.setVariable("dynamicDeptsUpdated", true);
//
//        log.info("动态添加部门：{}, 当前部门列表：{}", newDepts, allDepts);
//    }
//
//    /**
//     * 动态移除部门从处理列表
//     *
//     * @param execution   执行上下文
//     * @param removeDepts 要移除的部门列表
//     */
//    public void removeDepartments(DelegateExecution execution, List<String> removeDepts) {
//        if (CollUtil.isEmpty(removeDepts)) {
//            return;
//        }
//
//        List<String> currentDepts = getSelectedDepts(execution);
//        List<String> updatedDepts = currentDepts.stream()
//                .filter(dept -> !removeDepts.contains(dept))
//                .collect(Collectors.toList());
//
//        execution.setVariable("selectedDepts", updatedDepts);
//        execution.setVariable("dynamicDeptsUpdated", true);
//
//        log.info("动态移除部门：{}, 当前部门列表：{}", removeDepts, updatedDepts);
//    }

    /**
     * 检查是否有动态部门变更
     *
     * @param execution 执行上下文
     * @return 是否有变更
     */
    public boolean hasDynamicDeptChanges(DelegateExecution execution) {
        final Object updated = execution.getVariable("dynamicDeptsUpdated");
        return Boolean.TRUE.equals(updated);
    }

    /**
     * 重置动态部门变更标志
     * @param execution 执行上下文
     */
    public void resetDynamicDeptFlag(DelegateExecution execution) {
        execution.setVariable("dynamicDeptsUpdated", false);
    }

    /**
     * 获取当前活跃的子流程实例数量
     *
     * @param execution 执行上下文
     * @return 活跃子流程数量
     */
    public int getActiveSubprocessCount(DelegateExecution execution) {
        // 这里可以通过RuntimeService查询当前活跃的子流程实例
        final String processInstanceId = execution.getProcessInstanceId();
        // 实际实现需要查询子流程实例状态
        return 0;
    }

    /**
     * 检查子流程完成条件
     * 支持多种完成策略：全部完成、部分完成、超时处理等
     *
     * @param execution 执行上下文
     * @param nrOfCompletedInstances 已完成实例数
     * @param nrOfInstances 总实例数
     * @param nrOfActiveInstances 活跃实例数
     * @return 是否应该完成多实例
     */
    public boolean checkSubProcessCompletion(DelegateExecution execution,
                                           int nrOfCompletedInstances,
                                           int nrOfInstances,
                                           int nrOfActiveInstances) {
        try {
            // 获取完成策略配置
            Object completionStrategy = execution.getVariable("subProcessCompletionStrategy");
            String strategy = completionStrategy != null ? completionStrategy.toString() : "ALL";

            switch (strategy.toUpperCase()) {
                case "ALL":
                    // 默认策略：所有子流程都必须完成
                    return nrOfCompletedInstances >= nrOfInstances;

                case "MAJORITY":
                    // 大多数策略：超过一半的子流程完成即可
                    return nrOfCompletedInstances > (nrOfInstances / 2);

                case "ANY":
                    // 任意策略：任何一个子流程完成即可
                    return nrOfCompletedInstances > 0;

                case "PARTIAL":
                    // 部分策略：根据配置的最小完成数量
                    Object minCompleted = execution.getVariable("minCompletedSubProcesses");
                    int minRequired = minCompleted != null ? Integer.parseInt(minCompleted.toString()) : nrOfInstances;
                    return nrOfCompletedInstances >= minRequired;

                case "TIMEOUT":
                    // 超时策略：检查是否超时，超时则以当前完成的为准
                    return checkTimeoutCompletion(execution, nrOfCompletedInstances, nrOfActiveInstances);

                default:
                    // 默认回退到全部完成
                    return nrOfCompletedInstances >= nrOfInstances;
            }
        } catch (Exception e) {
            log.warn("检查子流程完成条件时发生异常: {}", e.getMessage());
            // 异常情况下回退到默认策略
            return nrOfCompletedInstances >= nrOfInstances;
        }
    }

    /**
     * 检查超时完成条件
     */
    private boolean checkTimeoutCompletion(DelegateExecution execution, int nrOfCompletedInstances, int nrOfActiveInstances) {
        try {
            Object timeoutMinutes = execution.getVariable("subProcessTimeoutMinutes");
            if (timeoutMinutes == null) {
                return false; // 没有设置超时，不使用超时策略
            }

            Object startTimeObj = execution.getVariable("subProcessStartTime");
            if (startTimeObj == null) {
                // 第一次检查，记录开始时间
                execution.setVariable("subProcessStartTime", System.currentTimeMillis());
                return false;
            }

            long startTime = Long.parseLong(startTimeObj.toString());
            long currentTime = System.currentTimeMillis();
            long timeoutMs = Long.parseLong(timeoutMinutes.toString()) * 60 * 1000;

            if (currentTime - startTime > timeoutMs) {
                log.warn("子流程执行超时，已完成: {}, 仍在执行: {}", nrOfCompletedInstances, nrOfActiveInstances);
                // 超时了，如果有完成的就算完成，否则继续等待
                return nrOfCompletedInstances > 0;
            }

            return false; // 未超时，继续等待
        } catch (Exception e) {
            log.warn("检查超时条件时发生异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 支持动态加签减签的子流程完成检查
     */
    public boolean checkDynamicSubProcessCompletion(DelegateExecution execution,
                                                   int nrOfCompletedInstances,
                                                   int nrOfInstances,
                                                   int nrOfActiveInstances) {
        try {
            // 检查是否有动态调整
            if (hasDynamicDeptChanges(execution)) {
                log.info("检测到动态部门调整，重新评估完成条件");

                // 获取当前动态部门列表
                @SuppressWarnings("unchecked")
                List<String> currentDepts = (List<String>) execution.getVariable("dynamicDepts");
                if (currentDepts != null) {
                    int currentRequiredInstances = currentDepts.size();

                    // 处理减签情况：如果当前完成数已经满足新的要求
                    if (nrOfCompletedInstances >= currentRequiredInstances) {
                        log.info("减签后完成条件已满足: 完成数={}, 要求数={}", nrOfCompletedInstances, currentRequiredInstances);
                        return true;
                    }

                    // 处理加签情况：需要等待新增的部门完成
                    // 这里需要特殊处理，因为Flowable的多实例在运行时不能直接增加实例
                    // 我们通过标记来处理这种情况
                    Object addSignDepts = execution.getVariable("addSignDepts");
                    if (addSignDepts != null) {
                        return handleAddSignCompletion(execution, nrOfCompletedInstances, nrOfInstances);
                    }
                }
            }

            // 默认完成条件检查
            return checkSubProcessCompletion(execution, nrOfCompletedInstances, nrOfInstances, nrOfActiveInstances);

        } catch (Exception e) {
            log.warn("动态子流程完成检查异常: {}", e.getMessage());
            return checkSubProcessCompletion(execution, nrOfCompletedInstances, nrOfInstances, nrOfActiveInstances);
        }
    }

    /**
     * 处理加签完成逻辑
     */
    private boolean handleAddSignCompletion(DelegateExecution execution, int nrOfCompletedInstances, int nrOfInstances) {
        // 检查加签的部门是否都已完成
        @SuppressWarnings("unchecked")
        List<String> addSignDepts = (List<String>) execution.getVariable("addSignDepts");
        @SuppressWarnings("unchecked")
        List<String> addSignCompletedDepts = (List<String>) execution.getVariable("addSignCompletedDepts");

        if (addSignCompletedDepts == null) {
            addSignCompletedDepts = new ArrayList<>();
            execution.setVariable("addSignCompletedDepts", addSignCompletedDepts);
        }

        // 原有实例完成 + 加签实例完成
        boolean originalCompleted = nrOfCompletedInstances >= nrOfInstances;
        boolean addSignCompleted = addSignCompletedDepts.size() >= addSignDepts.size();

        return originalCompleted && addSignCompleted;
    }

    /**
     * 动态加签部门
     *
     * @param processInstanceId 流程实例ID
     * @param newDepts 新增部门列表
     * @return 操作结果
     */
    public boolean addSignDepartments(String processInstanceId, List<String> newDepts) {
        if (CollUtil.isEmpty(newDepts)) {
            return false;
        }

        try {
            // 1. 找到多实例子流程的执行
            Execution subProcessExecution = runtimeService.createExecutionQuery()
                    .processInstanceId(processInstanceId)
                    .activityId("Activity_dept_subprocess")
                    .singleResult();

            if (subProcessExecution == null) {
                log.warn("未找到业务部门处理子流程执行");
                return false;
            }

            // 2. 更新动态部门列表
            List<CCDto> deptList = new ArrayList<>();
            CCDto ccDto = new CCDto();
            ccDto.setDeptCode("10102");
            ccDto.setDeptName("校办");
            ccDto.setUsername("20041029");
            ccDto.setDisplayname("孙");
            Object variable = (JSONArray) runtimeService.getVariable(processInstanceId, "businessDeptProc");
            if (variable != null) {
                deptList = JSON.parseArray(variable.toString(), CCDto.class);
            }
            // 添加新的部门
            deptList.add(ccDto);
            // 更新流程变量
            // 将deptList 转换为 JSONArray
            JSONArray array = (JSONArray) JSON.toJSON(deptList);
            runtimeService.setVariable(processInstanceId, "businessDeptProc", array);
            // 3. 为每个新增部门添加多实例执行
            for (String dept : newDepts) {
                Map<String, Object> variables = new HashMap<>();
                variables.put("dept", dept);

                // 使用 Flowable 的多实例运行时 API
                runtimeService.addMultiInstanceExecution(
                        "Activity_dept_subprocess",
                        processInstanceId,
                        variables
                );
            }

            // 4. 记录加签信息
            List<String> addSignDepts = (List<String>) runtimeService.getVariable(processInstanceId, "addSignDepts");
            if (addSignDepts == null) {
                addSignDepts = new ArrayList<>();
            }
            addSignDepts.addAll(newDepts);
            runtimeService.setVariable(processInstanceId, "addSignDepts", addSignDepts);

            return true;
        } catch (Exception e) {
            log.error("加签失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 动态减签部门
     *
     * @param processInstanceId 流程实例ID
     * @param removeDepts 要移除的部门列表
     * @return 操作结果
     */
    public boolean removeSignDepartments(String processInstanceId, List<String> removeDepts) {
        if (CollUtil.isEmpty(removeDepts)) {
            return false;
        }

        try {
            // 获取当前动态部门列表（这里需要通过RuntimeService获取）
            Map<String, Object> variables = runtimeService.getVariables(processInstanceId);
            @SuppressWarnings("unchecked")
            List<String> currentDepts = (List<String>) variables.get("dynamicDepts");

            if (currentDepts == null) {
                log.warn("当前部门列表为空，无法减签");
                return false;
            }

            // 移除指定部门
            List<String> updatedDepts = currentDepts.stream()
                    .filter(dept -> !removeDepts.contains(dept))
                    .collect(Collectors.toList());

            // 更新流程变量
            runtimeService.setVariable(processInstanceId, "dynamicDepts", updatedDepts);
            runtimeService.setVariable(processInstanceId, "dynamicDeptsUpdated", true);

            // 记录减签的部门
            @SuppressWarnings("unchecked")
            List<String> removeSignDepts = (List<String>) variables.get("removeSignDepts");
            if (removeSignDepts == null) {
                removeSignDepts = new ArrayList<>();
            }
            removeSignDepts.addAll(removeDepts);
            runtimeService.setVariable(processInstanceId, "removeSignDepts", removeSignDepts);

            log.info("成功减签部门: {}, 流程实例: {}", removeDepts, processInstanceId);
            return true;

        } catch (Exception e) {
            log.error("减签部门失败: {}", e.getMessage(), e);
            return false;
        }
    }



    /**
     * 根据文档说明获取默认用户列表
     *
     * @param documentation 文档说明
     * @return 用户ID列表
     */
    private List<String> getDefaultUsersByDocumentation(String documentation) {
        List<String> userIds = new ArrayList<>();
        
        if (StringUtils.isNotBlank(documentation)) {
            // 这里可以根据实际业务需求配置默认用户
            // 暂时返回空列表，实际使用时需要根据业务配置
            log.info("根据文档说明获取默认用户：{}", documentation);
        }
        
        return userIds;
    }

    /**
     * 获取当前流程执行上下文（简化实现）
     */
    private DelegateExecution getCurrentExecution(String processInstanceId) {
        // 在实际应用中，这里需要通过RuntimeService获取执行上下文
        // 由于DelegateExecution是接口，这里返回null，实际使用时需要具体实现
        log.warn("getCurrentExecution方法需要具体实现，当前返回null");
        return null;
    }

    /**
     * 为加签部门启动独立的审批流程
     */
    private void startAddSignProcesses(String parentProcessInstanceId, List<String> newDepts) {
        // 为每个新增部门启动一个独立的子流程
        // 这些子流程会与主流程关联，但独立执行
        for (String dept : newDepts) {
            try {
                Map<String, Object> variables = new HashMap<>();
                variables.put("dept", dept);
                variables.put("parentProcessInstanceId", parentProcessInstanceId);
                variables.put("isAddSignProcess", true);

                // 启动加签子流程（需要单独定义一个加签流程）
                 ProcessInstance addSignProcess = runtimeService.startProcessInstanceByKey("add_sign_dept_process", variables);

                log.info("为部门 {} 启动加签流程", dept);
            } catch (Exception e) {
                log.error("启动部门 {} 的加签流程失败: {}", dept, e.getMessage());
            }
        }
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        // 任务监听器实现
        log.info("任务监听器被触发: {}", delegateTask.getName());
    }

}