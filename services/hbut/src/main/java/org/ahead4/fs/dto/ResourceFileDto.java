package org.ahead4.fs.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 资源文件表
 */
@Getter
@Setter
@TableName(value = "t_resource_file", autoResultMap = true)
public class ResourceFileDto {
    private String id;

    private String path;

    private String name;

    private String  type;

    private String  postfix;

    private String resourceId;

    private long fileSize;
    //创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+08:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String createName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+08:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String updateName;

    private String md5;
    private String attachmentsId;
    private String source;
    private String superiorFile;
    private String originalName;
}
