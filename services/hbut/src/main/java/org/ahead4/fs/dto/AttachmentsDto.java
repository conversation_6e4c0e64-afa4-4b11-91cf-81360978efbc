package org.ahead4.fs.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 文件记录表，已废弃
 */
@Setter
@Getter
public class AttachmentsDto {
    private String id;
    private String path;
    private String name;
    private String type;
    private String postfix;

    private String parentId;
    private String originalName;
    //创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+08:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String createName;
    private String source;
    private String superiorFile;
    private String md5;
    private long size;
}
