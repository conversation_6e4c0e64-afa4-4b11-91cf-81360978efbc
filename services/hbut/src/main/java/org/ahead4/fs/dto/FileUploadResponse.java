package org.ahead4.fs.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class FileUploadResponse {
    private String attachmentsId;
    private String md5;
    private String parentId;
    private String fileName;
    private String filePath;
    private String fileNewName;
    private long fileSize;
    private String postfix;

    public FileUploadResponse(String id,String md5,String parentId,String fileName, String filePath,String fileNewName, long fileSize,String postfix) {
        this.attachmentsId=id;
        this.md5=md5;
        this.parentId=parentId;
        this.fileName = fileName;
        this.filePath = filePath;
        this.fileNewName=fileNewName;
        this.fileSize = fileSize;
        this.postfix=postfix;
    }
    public FileUploadResponse(String fileName, String filePath) {
        this.fileName = fileName;
        this.filePath = filePath;
    }
}
