package org.ahead4.fs.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import dm.jdbc.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.ahead4.fs.dto.ResourceFileDto;
import org.ahead4.fs.mapper.ResourceFileRepository;
import org.ahead4.web.presentate.HttpMsg;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.FileSystemUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 资源控制层
 */
@RestController
@RequestMapping("/file")
@Api("文件管理")
public class FileController {
    @Autowired
    private ResourceFileRepository repository;
    @Value("${file.upload-dir:}")
    private String uploadDir;
    private Map<String, UploadConfig> uploadConfigs = new HashMap<>();

    public FileController() {
        // 设置不同type的上传配置
        uploadConfigs.put("image", new UploadConfig(5 * 1024 * 1024, new String[]{"jpg", "png", "jpeg"})); // 5MB
        uploadConfigs.put("document", new UploadConfig(10 * 1024 * 1024, new String[]{"pdf", "docx", "txt"})); // 10MB
        uploadConfigs.put("aheat4", new UploadConfig(10000L * 1024 * 1024, new String[]{})); // 10MB
        // 可以根据需要添加更多类型
    }

    @GetMapping("/judgeMd5/{md5}")
    @ApiOperation("根据md5判断文件是否存在")
    public HttpMsg judgeMd5(@PathVariable String md5) {
        return  getJudgeMd5(md5)?HttpMsg.ok("true"):HttpMsg.ok("false");
    }
    public Boolean getJudgeMd5(String md5){
        Integer integer = repository.selectCount(new QueryWrapper<ResourceFileDto>().eq("md5", md5));
        return integer>0;
    }
    @PostMapping({"/upload", "/upload/{type}"})
    @ApiOperation("文件上传")
    public ResponseEntity<?> uploadFile(@RequestParam(value = "file",required = false) MultipartFile file,@RequestParam("parentId")String parentId,@RequestParam(value = "md5",required = false)String md5,@RequestParam(value = "originalName",required = false)String originalName, @PathVariable(required = false) String type) {
        try {
            String fileMd5;
            if(StringUtil.isNotEmpty(md5)&&getJudgeMd5(md5)){
                if(StringUtil.isEmpty(originalName)) return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("originalName is empty");
                //md5已存在，不用上传文件，跳过上传文件步骤
                fileMd5=md5;
                List<ResourceFileDto> resourceFileDtos = repository.selectList(new QueryWrapper<ResourceFileDto>().eq("md5", fileMd5));
                if(resourceFileDtos.size()> 0) {
                    ResourceFileDto resourceFileDto=resourceFileDtos.get(0);
                    //再生成一个上传记录，但是不上传这个文件
                    ResourceFileDto resourceFileDto1=new ResourceFileDto();
                    BeanUtils.copyProperties(resourceFileDto,resourceFileDto1);
                    resourceFileDto1.setId(UUID.randomUUID().toString());
                    resourceFileDto1.setName(fileMd5);
                    resourceFileDto1.setOriginalName(originalName);
                    //上传这个记录
                    repository.insert(resourceFileDto1);
                    //返回这个文件
                    return ResponseEntity.ok(resourceFileDto1);
                }
            }
            //md5不存在正常走流程,文件不存在
            if (file.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("File is empty");
            }
            if (type != null && uploadConfigs.containsKey(type)) {
                UploadConfig config = uploadConfigs.get(type);

                // 检查文件大小
                if (file.getSize() > config.getMaxSize()) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("File size exceeds the maximum allowed size");
                }

                // 检查文件扩展名
                String fileExtension = getFileExtension(file.getOriginalFilename());
                boolean isAllowed = false;
                for (String ext : config.getAllowedExtensions()) {
                    if (ext.equalsIgnoreCase(fileExtension)) {
                        isAllowed = true;
                        break;
                    }
                }
                if (!isAllowed) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("File type not allowed");
                }
            }
            // 生成文件的MD5摘要
            try (InputStream inputStream = file.getInputStream()) {
                fileMd5 = getMD5Checksum(inputStream);
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException(e);
            }
            // 获取当前日期并创建文件夹
            String currentDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
            Path dateDir = Paths.get(uploadDir, currentDate);
            if (!Files.exists(dateDir)) {
                Files.createDirectories(dateDir);
            }
            String fileNewName =fileMd5;
            Path filePath = dateDir.resolve(fileNewName);
            Files.write(filePath, file.getBytes());
            // 保存文件信息到数据库
            ResourceFileDto resourceFileDto=new ResourceFileDto();
            resourceFileDto.setId(UUID.randomUUID().toString());
            resourceFileDto.setName(UUID.randomUUID().toString());
            resourceFileDto.setPath(filePath.toString());
            resourceFileDto.setType(file.getContentType());
            resourceFileDto.setPostfix(getFileExtension(file.getOriginalFilename()));
            resourceFileDto.setResourceId(parentId);
            resourceFileDto.setOriginalName(file.getOriginalFilename());
            resourceFileDto.setSource(type);
            resourceFileDto.setSuperiorFile(currentDate);
            resourceFileDto.setMd5(fileMd5);
            resourceFileDto.setFileSize(file.getSize());
            repository.insert(resourceFileDto);
            //上传附件
            return ResponseEntity.ok(resourceFileDto);
        } catch (IOException e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Failed to upload file");
        }
    }

    // 下载文件
    @GetMapping("/download/{fileId}")
    @ApiOperation("文件下载")
    public void downloadFile(@PathVariable String fileId, HttpServletResponse res) {
        try {
            // 从数据库获取文件信息
            ResourceFileDto resourceFileDto = repository.selectById(fileId);
            if (resourceFileDto == null) {
                res.sendError(HttpServletResponse.SC_NOT_FOUND, "File not found");
                return;
            }

            // 构造文件路径
            Path filePath = Paths.get(uploadDir, resourceFileDto.getSuperiorFile(), resourceFileDto.getMd5());
            if (!Files.exists(filePath)) {
                res.sendError(HttpServletResponse.SC_NOT_FOUND, "File not found");
                return;
            }

            // 获取MIME类型
            String mimeType = resourceFileDto.getType();
            if (mimeType == null || mimeType.isEmpty()) {
                mimeType = Files.probeContentType(filePath); // 根据文件路径推断 MIME 类型
            }

            // 读取文件并发送响应
            try (FileInputStream inputStream = new FileInputStream(filePath.toFile())) {
                doGet(res, inputStream, resourceFileDto.getOriginalName(), mimeType, Files.size(filePath));
            }
        } catch (Exception e) {
            e.printStackTrace();
            try {
                res.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Error while processing the file download");
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
        }
    }

    protected void doGet(HttpServletResponse res, FileInputStream inputStream, String filename, String mimeType, long fileLength) throws IOException {
        // 设置响应内容类型和文件长度
        res.setContentType(mimeType);
        res.setHeader("Content-Disposition", "attachment; filename=" + new String(filename.getBytes(StandardCharsets.UTF_8), "ISO-8859-1"));
        res.setContentLengthLong(fileLength);

        // 将文件流复制到响应输出流
        IOUtils.copy(inputStream, res.getOutputStream());
        res.flushBuffer(); // 确保所有数据都被发送出去
    }
    @DeleteMapping("/delete/{fileId}")
    @ApiOperation("文件删除")
    public ResponseEntity<String> deleteFile(@PathVariable String fileId) {
        try {
            //查出这个文件记录
            ResourceFileDto resourceFileDto = repository.selectById(fileId);
            //删除这个文件记录
            repository.deleteById(fileId);
            //查询表里面是否有别的引用这个文件
            int count = repository.selectCount(new QueryWrapper<ResourceFileDto>().eq("md5", resourceFileDto.getMd5()));
            //有就只删除记录，没有就把文件也删除掉
            if(count>0) return ResponseEntity.ok("File deleted successfully: " + resourceFileDto.getOriginalName());
            Path filePath = Paths.get(uploadDir, resourceFileDto.getSuperiorFile(), resourceFileDto.getMd5());
            if (Files.exists(filePath)) {
                FileSystemUtils.deleteRecursively(filePath);
                return ResponseEntity.ok("File deleted successfully: " + resourceFileDto.getOriginalName());
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("File not found: " + resourceFileDto.getOriginalName());
            }
        } catch (IOException e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Failed to delete file");
        }
    }
    private static class UploadConfig {
        private long maxSize;
        private String[] allowedExtensions;

        public UploadConfig(long maxSize, String[] allowedExtensions) {
            this.maxSize = maxSize;
            this.allowedExtensions = allowedExtensions;
        }

        public long getMaxSize() {
            return maxSize;
        }

        public String[] getAllowedExtensions() {
            return allowedExtensions;
        }
    }
    private String getFileExtension(String filename) {
        if (filename == null) {
            return null;
        }
        int dotIndex = filename.lastIndexOf('.');
        return (dotIndex == -1) ? "" : filename.substring(dotIndex + 1);
    }
    public static String getMD5Checksum(InputStream inputStream) throws IOException, NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            md.update(buffer, 0, bytesRead);
        }
        byte[] digest = md.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
}
