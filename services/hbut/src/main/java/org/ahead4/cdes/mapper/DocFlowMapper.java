package org.ahead4.cdes.mapper;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.ahead4.cdes.entity.DocFlow;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 公文流转信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
public interface DocFlowMapper extends BaseMapper<DocFlow> {

    IPage<DocFlow> ccPage(IPage<Object> objectIPage, @Param("ew") UpdateWrapper<Object> objectUpdateWrapper,
            @Param("username") String username);

    /**
     * 参与列表
     *
     * @param objectIPage         对象 ipage
     * @param objectUpdateWrapper 对象更新包装器
     * @param username            显示名称
     * @return {@link IPage }<{@link DocFlow }>
     */
    IPage<DocFlow> finishedList(IPage<Object> objectIPage, @Param("ew") UpdateWrapper<Object> objectUpdateWrapper,
                                @Param("username") String username);
}
