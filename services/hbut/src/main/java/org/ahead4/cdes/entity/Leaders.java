package org.ahead4.cdes.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/*

   CREATE TABLE "public"."leaders" (
  "dept_des" text COLLATE "pg_catalog"."default",
  "display_name" text COLLATE "pg_catalog"."default",
  "role_des" text COLLATE "pg_catalog"."default",
  "dept_code" text COLLATE "pg_catalog"."default",
  "username" text COLLATE "pg_catalog"."default",
  "dept_name" text COLLATE "pg_catalog"."default",
  "role" text COLLATE "pg_catalog"."default",
  "is_top_leader" bool,
  "can_vote" bool,
  "parent_dept_code" text COLLATE "pg_catalog"."default",
  "parent_dept_name" text COLLATE "pg_catalog"."default",
  "primary" bool
)

 */
@EqualsAndHashCode(callSuper = false)
@TableName(value="leaders", autoResultMap = true)
public class Leaders implements Serializable {
    private String deptDes;
    private String displayName;
    private String roleDes;
    private String deptCode;
    private String username;
    private String deptName;
    private String role;
    private boolean isTopLeader;
    private boolean canVote;
    private String parentDeptCode;
    private String parentDeptName;
    @TableField("\"primary\"")
    private boolean primary;

    // Getters and setters
    public String getDeptDes() {
        return deptDes;
    }

    public void setDeptDes(String deptDes) {
        this.deptDes = deptDes;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getRoleDes() {
        return roleDes;
    }

    public void setRoleDes(String roleDes) {
        this.roleDes = roleDes;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public boolean isTopLeader() {
        return isTopLeader;
    }

    public void setTopLeader(boolean topLeader) {
        isTopLeader = topLeader;
    }

    public boolean isCanVote() {
        return canVote;
    }

    public void setCanVote(boolean canVote) {
        this.canVote = canVote;
    }

    public String getParentDeptCode() {
        return parentDeptCode;
    }

    public void setParentDeptCode(String parentDeptCode) {
        this.parentDeptCode = parentDeptCode;
    }

    public String getParentDeptName() {
        return parentDeptName;
    }

    public void setParentDeptName(String parentDeptName) {
        this.parentDeptName = parentDeptName;
    }

    public boolean isPrimary() {
        return primary;
    }

    public void setPrimary(boolean primary) {
        this.primary = primary;
    }
}
