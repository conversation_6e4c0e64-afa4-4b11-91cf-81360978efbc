package org.ahead4.cdes.enums;

import lombok.Getter;

@Getter
public enum DocFlowStatusEnum {

    // schoolLeader	校领导审批
    // officeHandling	校办处理
    // inChargeLeader	分管领导审批
    // officeDistribution	校办分发
    // departmentHandling	业务部门处理
    // officeArchiving	校办归档
    // completed	完成
    // canceled	已取消
    // terminated	已终止

    SUBMIT("submit", "已提交"),
    SCHOOL_LEADER("schoolLeader", "校领导审批"),
    OFFICE_HANDLING("officeHandling", "校办处理"),
    IN_CHARGE_LEADER("inChargeLeader", "分管领导审批"),
    OFFICE_DISTRIBUTION("officeDistribution", "校办分发"),
    DEPARTMENT_HANDLING("departmentHandling", "业务部门处理"),
    OFFICE_ARCHIVING("officeArchiving", "校办归档"),
    COMPLETED("completed", "完成"),
    RUNNING("running", "审批中"),
    TERMINATED("terminated", "终止"),
    CANCELED("canceled", "已取消");

    private final String code;
    private final String desc;

    DocFlowStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DocFlowStatusEnum getByCode(String code) {
        for (DocFlowStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
