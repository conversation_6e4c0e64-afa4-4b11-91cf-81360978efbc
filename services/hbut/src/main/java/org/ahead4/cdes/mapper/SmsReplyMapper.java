package org.ahead4.cdes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.ahead4.cdes.entity.SmsReply;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 短信上行回复Mapper接口
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
public interface SmsReplyMapper extends BaseMapper<SmsReply> {

    /**
     * 批量插入短信上行回复
     *
     * @param smsReplies 短信上行回复列表
     * @return 是否成功
     */
    boolean insertBatch(@Param("list") List<SmsReply> smsReplies);
}
