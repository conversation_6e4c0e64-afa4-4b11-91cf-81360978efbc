package org.ahead4.cdes.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.ahead4.cdes.entity.DocFlow;
import org.ahead4.cdes.service.DocFlowService;
import org.ahead4.common.dto.Page;
import org.ahead4.common.dto.PageParam;
import org.ahead4.common.dto.SearchFilter;
import org.ahead4.common.dto.SearchFilter.LogicType;
import org.ahead4.common.dto.SearchFilter.MatchType;
import org.ahead4.security.utils.AccessHolder;
import org.ahead4.web.presentate.HttpMsg;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static org.ahead4.jdbc.utils.MybatisPlusUtils.*;

/**
 * <p>
 * 公文流转信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@RestController
@RequestMapping("/doc-flow")
@Api(value = "公文流转信息控制器", description = "公文流转信息表控制器")
public class DocFlowController {
    @Autowired
    DocFlowService service;
    /**
     * 详情
     * @return
     */
    @PreAuthorize("isAuthenticated()")
    @GetMapping("detail/{id}")
    @ApiOperation(value = "详情")
    public HttpMsg get(@PathVariable String id) {
        return HttpMsg.ok().data(service.getById(id));
    }

    /**
     * 分页
     * @return
     */
    @PostMapping("page")
    @ApiOperation(value = "分页")
    public Page<DocFlow> page(@RequestBody PageParam param) {
        return plusPage2Page(service.page(param2PlusPage(param), param2PlusWrapper(param)));
    }

    /**
     * 全部
     * @return
     */
    @PostMapping("list")
    @ApiOperation(value = "全部")
    public List<DocFlow> list(@RequestBody PageParam param) {
        return service.list(param2PlusWrapper(param));
    }

    /**
     * 新增
     * @return
     */
    @PreAuthorize("hasAnyRole('ROLE_admin','ROLE_SOA') or hasAuthority('submit_doc')")
    @PostMapping
    @ApiOperation(value = "提交流程")
    public HttpMsg submit(@RequestBody DocFlow entity) {
        return HttpMsg.ok().data(service.addDocFlow(entity));
    }

    /**
     * 新增
     * @return
     */
    @PreAuthorize("hasAnyRole('ROLE_admin','ROLE_SOA') or hasAuthority('save_doc')")
    @PostMapping("/save")
    @ApiOperation(value = "保存草稿")
    public HttpMsg save(@RequestBody DocFlow entity) {
        return HttpMsg.ok().data(service.saveDocFlow(entity));
    }
    /**
     * 修改
     * @return
     */
    @PreAuthorize("hasAnyRole('ROLE_admin','ROLE_SOA') or hasAuthority('update_doc')")
    @PutMapping
    @ApiOperation(value = "修改")
    public HttpMsg update(@RequestBody DocFlow entity) {
        return HttpMsg.ok().data(service.updateDocFlow(entity));
    }
    /**
     * 删除
     * @return
     */
    @PreAuthorize("hasAnyRole('ROLE_admin','ROLE_SOA') or hasAuthority('del_doc')")
    @DeleteMapping({"{id}", ""})
    @ApiOperation(value = "删除")
    public HttpMsg delete(@PathVariable(required = false) String id, @RequestParam(required = false) List<String> idlist) {
        boolean r = false;
        if (!CollectionUtils.isEmpty(idlist)) {
            r = service.removeByIds(idlist);
        } else if (!StringUtils.isEmpty(id)) {
            r = service.removeById(id);
        }
        return r ? HttpMsg.ok("删除成功！") : HttpMsg.error("删除失败！");
    }

    /**
     * 抄送给我的
     */
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/ccList")
    @ApiOperation(value = "抄送给我的")
    public Page<DocFlow> copyList(@RequestBody PageParam param) {
        return plusPage2Page(service.ccPage(param));
    }

    /**
     * 我的申请
     */
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/myList")
    @ApiOperation(value = "我的申请")
    public Page<DocFlow> myList(@RequestBody PageParam param) {
        final SearchFilter searchFilter = new SearchFilter("creator", MatchType.EQ, AccessHolder.displayname(), LogicType.AND);
        param.getSearchFilters().add(searchFilter);
        param.getSortMap().putIfAbsent("update_time", "desc");
        return plusPage2Page(service.page(param2PlusPage(param), param2PlusWrapper(param)));
    }

    /**
     * 短信催办
     */
    @PreAuthorize("hasAnyRole('ROLE_admin','ROLE_SOA') or hasAnyAuthority('task_remind', 'task_user_remind')")
    @PutMapping("/remind")
    @ApiOperation(value = "短信催办")
    public HttpMsg remind(@ApiParam(value = "公文id", required = true) @RequestParam String businessKey,
                          @ApiParam(value = "催办用户") @RequestParam(required = false) String username) {
        return HttpMsg.ok().data(service.remind(businessKey, username));
    }

    @PreAuthorize("isAuthenticated()")
    @ApiOperation(value = "我参与的")
    @PostMapping("/finishedList")
    public Page<DocFlow> finishedList(@RequestBody PageParam param) {
        param.getSortMap().putIfAbsent("update_time", "desc");
        return plusPage2Page(service.finishedList(param));
    }
}

