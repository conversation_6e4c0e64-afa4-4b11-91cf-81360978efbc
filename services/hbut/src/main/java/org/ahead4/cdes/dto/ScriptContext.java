package org.ahead4.cdes.dto;

import groovy.lang.Binding;
import groovy.lang.GroovyClassLoader;
import groovy.lang.Script;
import lombok.Getter;
import lombok.Setter;
import org.ahead4.common.tools.SpringContextHolder;
import org.ahead4.dbms.service.ApiRealese;

import java.util.HashMap;

/**
 * 脚本执行上下文对象
 * @param <RESULT>
 */
public class ScriptContext<RESULT> extends HashMap<String, Object> {
    public ScriptContext() {}
    public ScriptContext(String scriptText) {
        this.scriptText = scriptText;
    }

    @Getter
    @Setter
    String scriptText;
    @Getter
    @Setter
    RESULT result;

    public RESULT exe() {
        execGroovy(scriptText, this);
        return this.result;
    }

    public void execGroovy(String scriptText, ScriptContext context) {
        try {
            Script script = parseGroovy(scriptText).newInstance();
            execGroovy(script, context);
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    /**
     * 缓存用的HashMap
     */
    private static final HashMap<String, Class<Script>> GROOVY_CACHE = new HashMap<>();
    /**
     * 加同步锁防重入，内部有一个HashMap实现缓存以节省Groovy脚本编译开销
     * @param scriptText
     * @return
     */
    public static synchronized Class<Script> parseGroovy(String scriptText) {
        if (GROOVY_CACHE.containsKey(scriptText)) {
            return GROOVY_CACHE.get(scriptText);
        }
        GroovyClassLoader classLoader = new GroovyClassLoader();
        String scriptFinalText = "import org.ahead4.cdes.dto.*\n" +
                "import org.ahead4.cdes.entity.*\n"
                + "import java.math.RoundingMode\n"
                +"import org.springframework.context.ApplicationContext\n"
                +"import org.ahead4.common.tools.SpringContextHolder\n"
                +"import com.alibaba.fastjson.JSON\n"
                +"import com.alibaba.fastjson.JSONArray\n"
                +"import com.alibaba.fastjson.JSONObject\n"
                +"import com.alibaba.fastjson.JSONPath\n"
                +"import org.ahead4.common.utils.*\n"
                +"import org.ahead4.permission.utils.AreaCodeHelp\n"+
                "def __exec() {\n" +
                    scriptText +
                "\n}\n" +
                "context.result = __exec()";
        Class<Script> clazz = classLoader.parseClass(scriptFinalText);
        // 存入缓存
        GROOVY_CACHE.put(scriptText, clazz);
        return clazz;
    }
    public ScriptContext<RESULT> execGroovy(Script script, ScriptContext<RESULT> context) {
        Binding binding = new Binding();
        // 执行上下文参数
        binding.setProperty("context", context);
        // 获取ApplicationContext对象
        binding.setProperty("springContext", SpringContextHolder.getApplicationContext());
        binding.setProperty("apiReleaseService", SpringContextHolder.getBean(ApiRealese.class));



        script.setBinding(binding);
        script.run();
        return (ScriptContext<RESULT>)binding.getProperty("context");
    }

}
