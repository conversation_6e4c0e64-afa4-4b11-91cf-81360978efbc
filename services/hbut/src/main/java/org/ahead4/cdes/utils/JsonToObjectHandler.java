package org.ahead4.cdes.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

@MappedTypes(Object.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class JsonToObjectHandler extends AbstractJsonTypeHandler<Object> {



    @Override
    public String toJson(Object object) {
        if (object == null) {
            return null;
        }
        try {
            JSONObject a=new JSONObject();
            a.put("value",object);
            return JSON.toJSONString(a);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert Annotation object to JSON", e);
        }
    }

    @Override
    public Object parse(String content) {
        if (StringUtils.isEmpty(content)) {
            return null;
        }
        try {
            JSONObject parse = (JSONObject) JSON.parse(content);
            return parse.get("value");
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse JSON content into Annotation object", e);
        }
    }

}