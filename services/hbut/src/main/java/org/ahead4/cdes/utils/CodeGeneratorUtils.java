package org.ahead4.cdes.utils;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;

public class CodeGeneratorUtils {

    public static void main(String[] args) {
        run(args);
    }
    public static void run(String[] args) {

        // 1、创建代码生成器
        AutoGenerator mpg = new AutoGenerator();
        // 2、全局配置
        GlobalConfig gc = new GlobalConfig();
        String projectPath = System.getProperty("user.dir");
       // gc.setOutputDir(projectPath + "/services/cdes/src/main/java");
        gc.setOutputDir(projectPath);
        gc.setAuthor("xinfa");
        gc.setOpen(true); //生成后是否打开资源管理器
        gc.setFileOverride(true); //重新生成时文件是否覆盖
        gc.setServiceName("%sService"); //去掉Service接口的首字母I
        gc.setIdType(IdType.ASSIGN_UUID); //主键策略
        gc.setDateType(DateType.ONLY_DATE);//定义生成的实体类中日期类型
        gc.setSwagger2(true);//开启Swagger2模式

        mpg.setGlobalConfig(gc);

        // 3、数据源配置
        DataSourceConfig dsc = new DataSourceConfig();
        // dsc.setUrl("***************************************************************************************");
        dsc.setUrl("**********************************************************************************");
        dsc.setDriverName("org.postgresql.Driver");
        dsc.setUsername("hbut");
        dsc.setPassword("nlwUBWY8");
        dsc.setDbType(DbType.POSTGRE_SQL);
        mpg.setDataSource(dsc);

        // 4、包配置
        PackageConfig pc = new PackageConfig();
        pc.setModuleName(null); //模块名
        pc.setParent("org.ahead4.cdes");
        pc.setController("controller");
        pc.setEntity("entity");
        pc.setService("service");
        pc.setMapper("mapper");
        mpg.setPackageInfo(pc);

        // 5、策略配置
        StrategyConfig strategy = new StrategyConfig();
//        strategy.setExclude("flyway_history"); // 排除
        strategy.setInclude("hb_doc_flow");
        strategy.setNaming(NamingStrategy.underline_to_camel);//数据库表映射到实体的命名策略
        strategy.setTablePrefix("govern_", "sys_", "api_", "dbm_", "data_" ,"qrtz_" ,"biz_", "oauth_", "zc_","hb_");
        //生成实体时去掉表前缀

        strategy.setColumnNaming(NamingStrategy.underline_to_camel);//数据库表字段映射到实体的命名策略
        strategy.setEntityLombokModel(true); // lombok 模型 @Accessors(chain = true) setter链式操作

        strategy.setRestControllerStyle(true); //restful api风格控制器
        strategy.setControllerMappingHyphenStyle(true); //url中驼峰转连字符

        mpg.setStrategy(strategy);
        // 6、自定模版
        TemplateConfig templateConfig = new TemplateConfig();
        templateConfig.setController("/templates/i-controller.java");
        mpg.setTemplate(templateConfig);


        InjectionConfig injectionConfig = new VueInjectionConfig();
        mpg.setCfg(injectionConfig);
        // 7、执行
        mpg.execute();
    }
}