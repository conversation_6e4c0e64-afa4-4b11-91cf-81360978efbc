package org.ahead4.cdes.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 短信回执查询响应DTO
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
@Data
public class SmsReportResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 结果代码
     */
    private String result;

    /**
     * 回执结果
     */
    private String out;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 回执列表
     */
    private List<SmsReportItem> reportItems;

    /**
     * 创建成功响应
     *
     * @param result 结果代码
     * @param out 回执结果
     * @return 响应对象
     */
    public static SmsReportResponse success(String result, String out) {
        SmsReportResponse response = new SmsReportResponse();
        response.setResult(result);
        response.setOut(out);
        response.setSuccess(true);
        
        // 解析回执结果
        List<SmsReportItem> reportItems = new ArrayList<>();
        if (out != null && !out.isEmpty()) {
            String[] items = out.split(";");
            for (String item : items) {
                if (item.trim().isEmpty()) {
                    continue;
                }
                String[] parts = item.split(",");
                if (parts.length >= 3) {
                    SmsReportItem reportItem = new SmsReportItem();
                    reportItem.setSerialNumber(parts[0]);
                    reportItem.setPhoneNumber(parts[1]);
                    reportItem.setStatus(parts[2]);
                    reportItems.add(reportItem);
                }
            }
        }
        response.setReportItems(reportItems);
        
        return response;
    }

    /**
     * 创建失败响应
     *
     * @param result 结果代码
     * @return 响应对象
     */
    public static SmsReportResponse failure(String result) {
        SmsReportResponse response = new SmsReportResponse();
        response.setResult(result);
        response.setSuccess(false);
        return response;
    }

    /**
     * 短信回执项
     */
    @Data
    public static class SmsReportItem implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 流水号
         */
        private String serialNumber;

        /**
         * 手机号码
         */
        private String phoneNumber;

        /**
         * 状态
         * 0、成功到达
         * 1、手机号停机或空号
         * 2、关机时间长过期
         * 3、网关拒绝关机时间长过期
         * 4、其他
         * 5、手机短信箱或者内存满了
         * 6、网关黑名单
         */
        private String status;
    }
}
