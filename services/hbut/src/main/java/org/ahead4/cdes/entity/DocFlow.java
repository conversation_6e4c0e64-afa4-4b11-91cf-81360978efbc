package org.ahead4.cdes.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.ahead4.cdes.entity.dto.CCDto;
import org.ahead4.cdes.entity.dto.TaskDeptDto;
import org.ahead4.fs.dto.ResourceFileDto;

/**
 * <p>
 * 公文流转信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "hb_doc_flow", autoResultMap = true)
@ApiModel(value="DocFlow对象", description="公文流转信息表")
public class DocFlow implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "流转编号")
    private String flowCode;

    @ApiModelProperty(value = "公文标题")
    private String docTitle;

    @ApiModelProperty(value = "公文类型")
    private String docType;

    @ApiModelProperty(value = "优先级")
    private String priorityLevel;

    @ApiModelProperty(value = "是否提醒： 1:是；0:否；")
    private Integer isRemind;

    @ApiModelProperty(value = "公文简介")
    private String docDesp;

    @TableField(value = "doc_files", typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty(value = "公文列表")
    private List<ResourceFileDto> docFiles;

    @TableField(value = "other_files", typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty(value = "其他附件")
    private List<ResourceFileDto> otherFiles;

    @ApiModelProperty(value = "是否公开：1:是；0:否；")
    private Integer isPublic;

    @TableField(value = "make_copy", typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty(value = "抄送人分管领导")
    private List<CCDto> makeCopy;

    /**
     * 分管领导
     */
    @TableField(value = "in_charge_leader", typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty(value = "分管领导")
    private List<CCDto> inChargeLeader;

    /**
     * 部门领导
     */
    @TableField(value = "department_leader" , typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty(value = "部门领导")
    private List<CCDto> departmentLeader;

    @TableField(value = "status" )
    @ApiModelProperty(value = "状态：submit:已提交;running:审批中；terminated:终止；canceled:已取消")
    private String status;

    /**
     * proc inst id
     */
    @ApiModelProperty(value = "流程实例id")
    @TableField("proc_inst_id")
    private String procInstId;

    @ApiModelProperty(value = "删除标志（0代表存在 时间戳代表删除时间）")
    @TableField(value = "del_flag")
    @TableLogic(value = "0", delval = "EXTRACT(EPOCH FROM NOW()) * 1000")
    private String delFlag;

    @ApiModelProperty(value = "创建者")
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(value = "updator", fill = FieldFill.INSERT_UPDATE)
    private String updator;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
