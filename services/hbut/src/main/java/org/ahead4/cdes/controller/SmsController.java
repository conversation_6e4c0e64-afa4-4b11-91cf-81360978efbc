package org.ahead4.cdes.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.cdes.config.SmsConfig;
import org.ahead4.cdes.constant.CommonConstant;
import org.ahead4.cdes.entity.SmsRecord;
import org.ahead4.cdes.entity.dto.SmsQueryRequest;
import org.ahead4.cdes.entity.dto.SmsReplyResponse;
import org.ahead4.cdes.entity.dto.SmsReportResponse;
import org.ahead4.cdes.entity.dto.SmsSendRequest;
import org.ahead4.cdes.entity.dto.SmsSendResponse;
import org.ahead4.cdes.service.SmsService;
import org.ahead4.cdes.utils.IpUtil;
import org.ahead4.common.service.CacheService;
import org.ahead4.redis.utils.RedisUtil;
import org.ahead4.web.presentate.HttpMsg;
import org.ahead4.workflow.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * 短信服务控制器
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
@Slf4j
@RestController
@RequestMapping("/sms")
@Api(tags = "短信服务接口")
public class SmsController {

    @Autowired
    private SmsService smsService;

    @Autowired
    private SmsConfig smsConfig;

    @Autowired
    private CacheService redisService;

    /**
     * IP请求次数Redis前缀
     */
    private static final String SMS_IP_REQUEST_PREFIX = "sms:ip:";

    /**
     * 手机号请求次数Redis前缀
     */
    private static final String SMS_PHONE_REQUEST_PREFIX = "sms:phone:";

    /**
     * 手机号验证码间隔Redis前缀
     */
    private static final String SMS_PHONE_INTERVAL_PREFIX = "sms:interval:";

    /**
     * 发送短信
     */
    @PostMapping("/send")
    @ApiOperation("发送短信")
    @PreAuthorize("hasAnyRole('ROLE_admin')")
    public HttpMsg sendSms(@RequestBody @Validated SmsSendRequest request) {
        // 生成流水号（如果未提供）
        if (StringUtils.isEmpty(request.getSerialNumber())) {
            request.setSerialNumber(generateSerialNumber());
        }

        SmsSendResponse response = smsService.sendSms(request);
        if (response.isSuccess()) {
            return HttpMsg.ok().data(response);
        } else {
            return HttpMsg.error(response.getDescription()).data(response);
        }
    }
    // 验证码白名单
    static final List<String> WHILTE = Arrays.asList("EDU_EMAIL");
    /**
     * 发送验证码
     */
    @PostMapping("/send-code")
    @ApiOperation("发送验证码")
    public HttpMsg sendVerificationCode(
            @ApiParam(value = "手机号码", required = true) @RequestParam String phoneNumber,
            @ApiParam(value = "业务类型", required = true) @RequestParam String businessType,
            @ApiParam(value = "图形验证码") @RequestParam(required = false) String captcha,
            @ApiParam(value = "图形验证码键") @RequestParam(required = false) String captchaKey,
            HttpServletRequest request) {

        // 验证图形验证码
        if (!validateCaptcha(captcha, captchaKey) && !WHILTE.contains(businessType)) {
            return HttpMsg.error("图形验证码验证失败");
        }

        // 检查IP请求频率限制
        String clientIp = IpUtil.getIpAddr(request);
        if (!checkIpRequestLimit(clientIp)) {
            return HttpMsg.error("请求过于频繁，请稍后再试");
        }

        // 检查手机号请求频率限制
        if (!checkPhoneRequestLimit(phoneNumber)) {
            return HttpMsg.error("该手机号请求次数过多，请明天再试");
        }

        // 检查手机号请求间隔
        if (!checkPhoneRequestInterval(phoneNumber)) {
            return HttpMsg.error("请求过于频繁，请" + smsConfig.getVerificationCodeInterval() + "秒后再试");
        }

        String code = smsService.sendVerificationCode(phoneNumber, businessType);
        if (code != null) {
            // 记录手机号请求间隔
            String intervalKey = SMS_PHONE_INTERVAL_PREFIX + phoneNumber;
            redisService.set(intervalKey, System.currentTimeMillis(), smsConfig.getVerificationCodeInterval() * 60L);

            return HttpMsg.ok().data("验证码发送成功");
        } else {
            return HttpMsg.error("验证码发送失败");
        }
    }

    /**
     * 验证短信验证码
     */
    @PostMapping("/verify-code")
    @ApiOperation("验证短信验证码")
    public HttpMsg verifyCode(
            @ApiParam(value = "手机号码", required = true) @RequestParam String phoneNumber,
            @ApiParam(value = "验证码", required = true) @RequestParam String code,
            @ApiParam(value = "业务类型", required = true) @RequestParam String businessType) {
        boolean result = smsService.verifyCode(phoneNumber, code, businessType);
        if (result) {
            return HttpMsg.ok().data("验证码验证成功");
        } else {
            return HttpMsg.error("验证码验证失败");
        }
    }

    /**
     * 查询短信记录
     */
    @PreAuthorize("hasAnyRole('ROLE_admin')")
    @PostMapping("/records")
    @ApiOperation("查询短信记录")
    public HttpMsg querySmsRecords(@RequestBody SmsQueryRequest request) {
        return HttpMsg.ok().data(smsService.querySmsRecords(request));
    }

    /**
     * 根据流水号查询短信记录
     */
    @PreAuthorize("hasAnyRole('ROLE_admin')")
    @GetMapping("/record/serial/{serialNumber}")
    @ApiOperation("根据流水号查询短信记录")
    public HttpMsg getBySerialNumber(@PathVariable String serialNumber) {
        SmsRecord record = smsService.getBySerialNumber(serialNumber);
        if (record != null) {
            return HttpMsg.ok().data(record);
        } else {
            return HttpMsg.error("未找到对应的短信记录");
        }
    }

    /**
     * 根据业务ID查询短信记录
     */
    @PreAuthorize("hasAnyRole('ROLE_admin')")
    @GetMapping("/record/business/{businessId}")
    @ApiOperation("根据业务ID查询短信记录")
    public HttpMsg getByBusinessId(@PathVariable String businessId) {
        SmsRecord record = smsService.getByBusinessId(businessId);
        if (record != null) {
            return HttpMsg.ok().data(record);
        } else {
            return HttpMsg.error("未找到对应的短信记录");
        }
    }

    /**
     * 查询短信回执
     */
    @PreAuthorize("hasAnyRole('ROLE_admin')")
    @GetMapping("/report")
    @ApiOperation("查询短信回执")
    public HttpMsg queryReport() {
        SmsReportResponse response = smsService.queryReport();
        if (response.isSuccess()) {
            // 更新短信回执状态
            smsService.updateSmsReports(response.getReportItems());
            return HttpMsg.ok().data(response);
        } else {
            return HttpMsg.error("查询短信回执失败").data(response);
        }
    }

    /**
     * 查询短信上行回复
     */
    @PreAuthorize("hasAnyRole('ROLE_admin')")
    @GetMapping("/reply")
    @ApiOperation("查询短信上行回复")
    public HttpMsg queryReply() {
        SmsReplyResponse response = smsService.queryReply();
        if (response.isSuccess() && response.getReplys() != null && !response.getReplys().isEmpty()) {
            // 保存短信上行回复
            smsService.saveSmsReplies(response.getReplys());
            // 确认短信上行回复
            smsService.confirmReply(response.getId());
            return HttpMsg.ok().data(response);
        } else {
            return HttpMsg.ok().data("暂无短信上行回复").data(response);
        }
    }

    /**
     * 生成流水号
     *
     * @return 流水号
     */
    private String generateSerialNumber() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String timestamp = sdf.format(new Date());

        // 生成3位随机数
        int random = new Random().nextInt(900) + 100;

        return timestamp + random;
    }

    /**
     * 验证图形验证码
     *
     * @param captcha 验证码
     * @param captchaKey 验证码键
     * @return 是否验证成功
     */
    private boolean validateCaptcha(String captcha, String captchaId) {
        // 实际项目中应该从缓存中获取验证码进行验证
        // 这里简单模拟验证成功
        String code = RedisUtil.get(CommonConstant.PRE_IMAGE_CODE + captchaId);
        return !StringUtils.isBlank(code) && code.equalsIgnoreCase(captcha);
    }


    /**
     * 检查IP请求频率限制
     *
     * @param ip 客户端IP
     * @return 是否通过检查
     */
    private boolean checkIpRequestLimit(String ip) {
        String key = SMS_IP_REQUEST_PREFIX + ip;
        Integer count = redisService.get(key);

        if (count == null) {
            // 首次请求，设置计数为1，有效期24小时
            redisService.set(key, 1, 24 * 60 * 60L);
            return true;
        } else if (count < smsConfig.getMaxRequestPerIpPerDay()) {
            // 未超过限制，计数加1
            redisService.set(key, count + 1, 24 * 60 * 60L);
            return true;
        } else {
            // 超过限制
            return false;
        }
    }

    /**
     * 检查手机号请求频率限制
     *
     * @param phoneNumber 手机号
     * @return 是否通过检查
     */
    private boolean checkPhoneRequestLimit(String phoneNumber) {
        String key = SMS_PHONE_REQUEST_PREFIX + phoneNumber;
        Integer count = redisService.get(key);

        if (count == null) {
            // 首次请求，设置计数为1，有效期24小时
            redisService.set(key, 1, 24 * 60 * 60L);
            return true;
        } else if (count < smsConfig.getMaxVerificationCodePerDay()) {
            // 未超过限制，计数加1
            redisService.set(key, count + 1, 24 * 60 * 60L);
            return true;
        } else {
            // 超过限制
            return false;
        }
    }

    /**
     * 检查手机号请求间隔
     *
     * @param phoneNumber 手机号
     * @return 是否通过检查
     */
    private boolean checkPhoneRequestInterval(String phoneNumber) {
        String key = SMS_PHONE_INTERVAL_PREFIX + phoneNumber;
        Long lastRequestTime = redisService.get(key);

        if (lastRequestTime == null) {
            // 首次请求
            return true;
        } else {
            // 检查间隔时间
            long currentTime = System.currentTimeMillis();
            long interval = currentTime - lastRequestTime;
            return interval >= smsConfig.getVerificationCodeInterval() * 1000;
        }
    }
}
