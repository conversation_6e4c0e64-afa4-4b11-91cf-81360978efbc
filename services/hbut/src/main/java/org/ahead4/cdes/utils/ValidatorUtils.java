package org.ahead4.cdes.utils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * hibernate-validator校验工具类
 * <p>
 * 参考文档：http://docs.jboss.org/hibernate/validator/5.4/reference/en-US/html_single/
 */
public class ValidatorUtils {

    private static final Validator validator;

    static {
        validator = Validation.buildDefaultValidatorFactory().getValidator();
    }

    /**
     * 校验对象
     *
     * @param object 待校验对象
     * @param groups 待校验的组
     * @throws IllegalArgumentException 校验不通过，则报RRException异常
     */
    public static void validateEntity(Object object, Class<?>... groups)
            throws IllegalArgumentException {
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(object, groups);
        if (!constraintViolations.isEmpty()) {
            ConstraintViolation<Object> constraint = constraintViolations.iterator().next();
            throw new IllegalArgumentException(constraint.getMessage());
        }
    }
    /**
     * 校验对象
     *
     * @param object 待校验对象
     * @param groups 待校验的组
     * @throws IllegalArgumentException 校验不通过，则报RRException异常
     */
    public static <T> List<Map<String, String>> validateEntity(T object)
            throws IllegalArgumentException {
        Set<ConstraintViolation<T>> constraintViolations = validator.validate(object);
        if (!constraintViolations.isEmpty()) {
            // 转换为可序列化的格式
            List<Map<String, String>> errors = constraintViolations.stream()
                    .map(v -> {
                        Map<String, String> error = new HashMap<>();
                        error.put("field", v.getPropertyPath().toString());
                        error.put("message", v.getMessage());
                        return error;
                    })
                    .collect(Collectors.toList());
            return errors;
        }
        return Collections.EMPTY_LIST;
    }
}
