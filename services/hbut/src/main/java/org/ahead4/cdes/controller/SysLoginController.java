package org.ahead4.cdes.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.cdes.service.SmsService;
import org.ahead4.common.service.CacheService;
import org.ahead4.common.utils.IdentitiesUtils;
import org.ahead4.logger.dto.ParamDto;
import org.ahead4.logger.service.entity.OperLog;
import org.ahead4.permission.service.UserService;
import org.ahead4.security.SecurityConst;
import org.ahead4.security.dto.IUserDetail;
import org.ahead4.security.utils.AccessHolder;
import org.ahead4.security.utils.RSASecurityHolder;
import org.ahead4.web.presentate.HttpMsg;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


/**
 * <AUTHOR>
 * <p>
 * 登录服务
 */

@RestController
@RequestMapping("sys-auth")
@Api(value = "登录服务API", tags = {"登录服务"})
@Slf4j
public class SysLoginController {
    @Autowired
    private UserService userService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private SmsService smsService;

    @PostMapping("token")
    @ApiOperation(value = "登录")
    public HttpMsg login(String username, String password, @RequestParam(required = false) String type, @RequestParam(required = false) String role) throws IOException {

        if (StringUtils.equalsIgnoreCase(type, "RSA")) {
            try {
                username = RSASecurityHolder.decryptRSADefault(username);
                password = RSASecurityHolder.decryptRSADefault(password);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        String counterKey = username + "_login_failure_counter";
        try {
            if (StringUtils.isNotEmpty(username) && SecurityConst.RETRY_LIMIT_TIMES > 0) {
                Integer failureCount = cacheService.<Integer>get(counterKey);
                failureCount = failureCount == null ? 0 : failureCount;
                long expire = cacheService.getExpire(counterKey);
                // 检查是否超出最大重试次数
                if (failureCount >= SecurityConst.RETRY_LIMIT_TIMES) {

                    OperLog.actionPush(username,
                            "登录",
                            ParamDto.args(username),
                            "用户登录失败，超出最大登录次数限制",
                            "失败", null);
                    throw new IllegalArgumentException("您已超过最大登陆次数限制，请等待" + expire + "秒后再试！");
                }
            }
        } catch (Exception e) {
            log.error("登陆参数检查异常！", e);
            throw e;
        }
        try {
            IUserDetail user = userService.findByUsername(username);
            if (user == null) throw new BadCredentialsException("用户名或密码错误！");
            if (!userService.checkPassword(user.getPassword(), password)) {
                throw new BadCredentialsException("用户名或密码错误！");
            }
            if (!user.getIsActive()) throw new BadCredentialsException("用户未启用！");
            String token = IdentitiesUtils.uuid();
            AccessHolder.token(token);
            AccessHolder.user(user);
            OperLog.actionPush(username,
                    "登录",
                    ParamDto.args(username),
                    "用户通过账号密码模式登录成功",
                    "成功", null);
            return HttpMsg.ok().put("access_token", token);
        } catch (Exception e) {
            log.error(username+"登陆异常！", e);
            e.printStackTrace();
            if (StringUtils.isNotEmpty(username) && SecurityConst.RETRY_LIMIT_TIMES > 0) {
                // 出现登陆异常登陆失败计数器加一
                cacheService.incr(counterKey, 1L, SecurityConst.RETRY_REFUSE_TIME);
                Integer failureCount = cacheService.<Integer>get(counterKey);
                failureCount = failureCount == null ? 1 : failureCount;

                OperLog.actionPush(username,
                        "登录",
                        ParamDto.args(username),
                        "用户登录失败，第" + failureCount + "次",
                        "失败", null);
                throw new BadCredentialsException("登陆失败！用户名或密码错误！你还可以尝试"+(SecurityConst.RETRY_LIMIT_TIMES - failureCount)+"次！");
            }
            throw new BadCredentialsException("登陆失败！用户名或密码错误！");
        }
    }

    @PostMapping("token/sms")
    @ApiOperation(value = "短信登录")
    public HttpMsg loginSms(@RequestParam String phoneNumber, @RequestParam String code, @RequestParam(required =
            false, defaultValue = "SMS_LOGIN") String type, @RequestParam(required = false) String role) {
        try {
            // 验证短信验证码
            if (!smsService.verifyCode(phoneNumber, code, "SMS_LOGIN")) {
                return HttpMsg.error(HttpStatus.UNAUTHORIZED, "短信验证码错误");
            }

            IUserDetail user = userService.findByUsername(phoneNumber);
            if (user == null) throw new BadCredentialsException("用户不存在！");
            if (!user.getIsActive()) throw new BadCredentialsException("用户未启用！");

            String token = IdentitiesUtils.uuid();
            AccessHolder.token(token);
            AccessHolder.user(user);

            OperLog.actionPush(phoneNumber,
                    "登录",
                    ParamDto.args(phoneNumber),
                    "用户通过短信验证码登录成功",
                    "成功", null);

            return HttpMsg.ok().put("access_token", token);
        } catch (Exception e) {
            log.error(phoneNumber + "短信登录异常！", e);
            return HttpMsg.error(HttpStatus.UNAUTHORIZED, e.getMessage());
        }
    }

    @PostMapping("token/envelope")
    @ApiOperation(value = "登录")
    public HttpMsg loginEnvelope(String username, String password, @RequestParam(required = false) String type, @RequestParam(required = false) String role) {
        try {
            return HttpMsg.ok("登录成功").data(this.login(username, password, type, role));
        } catch (Exception e) {
            return HttpMsg.error(HttpStatus.UNAUTHORIZED, e.getMessage());
        }
    }
    @PostMapping("token/rsa")
    @ApiOperation(value = "RSA登录")
    public HttpMsg loginRSA(String username, String password, @RequestParam(required = false) String role) {
        try {
            return HttpMsg.ok("登录成功").data(this.login(username, password, "RSA", role));
        } catch (Exception e) {
            return HttpMsg.error(HttpStatus.UNAUTHORIZED, e.getMessage());
        }
    }


    @PostMapping("refresh_token")
    @ApiOperation(value = "刷新token")
    public JSONObject refreshToken(HttpServletResponse response) {
        // 续一波token
        AccessHolder.user(AccessHolder.user());
        return null;
    }

    /**
     * 退出
     */
    @GetMapping("logout")
    @ApiOperation(value = "退出登录")
    public HttpMsg logout() {
        AccessHolder.logout(false);
        return HttpMsg.ok();
    }
}