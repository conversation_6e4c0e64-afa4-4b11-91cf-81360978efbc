package org.ahead4.cdes.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.ahead4.cdes.entity.Leaders;
import org.ahead4.cdes.mapper.LeadersMapper;
import org.ahead4.cdes.service.LeaderService;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class LeaderServiceImpl extends ServiceImpl<LeadersMapper, Leaders> implements LeaderService {
    @Override
    public Map<String, List<String>> getPrimaryLeaders() {
        List<Leaders> leaders = this.getBaseMapper().findPrimaryLeaders();
        Map<String, List<String>> result = new HashMap<>();
        leaders.forEach(leader -> {
            if (result.contains<PERSON>ey(leader.getUsername())) {
                result.get(leader.getUsername()).add(leader.getDeptCode());
            } else {
                List<String> list = new ArrayList<>();
                list.add(leader.getDeptCode());
                result.put(leader.getUsername(), list);
            }
        });
        return result;
    }
}
