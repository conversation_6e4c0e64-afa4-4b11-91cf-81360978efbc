package org.ahead4.cdes.utils;

import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.FileOutConfig;
import com.baomidou.mybatisplus.generator.config.IFileCreate;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import org.apache.commons.collections.CollectionUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class VueInjectionConfig extends InjectionConfig {
    String projectPath = System.getProperty("user.dir");
    private HashMap<String, Object> param;

    @Override
    public void initMap() {
        param = new HashMap<String, Object>() {{
            put("author", "Your Name");
            put("project", "MyBatis-Plus Code Generator");
        }};
        // 自定义 Map 对象，可以在模板中通过 cfg.xxx 引用
        this.setMap(param);
    }
    TableInfo tableInfo = null;
    @Override
    public void initTableMap(TableInfo tableInfo) {
        // 子类重写注入表对应补充信息
        this.tableInfo = tableInfo;
    }
    FileOutConfig getFileOut(String templatePath, String finalNameSuffix) {
        return new FileOutConfig(templatePath) {
            @Override
            public String outputFile(TableInfo tableInfo) {
                // 自定义输出文件路径
                return projectPath + "/codes/vue_codes/" + tableInfo.getEntityName() + "/" + tableInfo.getEntityName() + finalNameSuffix;
            }
        };
    }
    @Override
    public List<FileOutConfig> getFileOutConfigList() {
        List<FileOutConfig> fileOutConfigList = super.getFileOutConfigList();
        if (CollectionUtils.isEmpty(fileOutConfigList)) {
            fileOutConfigList = new ArrayList<>();
        }
        fileOutConfigList.add(getFileOut("/templates/api.js.vm", "-api.js"));
        fileOutConfigList.add(getFileOut("/templates/Edit.vue.vm", "Edit.vue"));
        fileOutConfigList.add(getFileOut("/templates/EditDialog.vue.vm", "EditDialog.vue"));
        fileOutConfigList.add(getFileOut("/templates/List.vue.vm", "List.vue"));
        fileOutConfigList.add(getFileOut("/templates/ListDialog.vue.vm", "ListDialog.vue"));
        fileOutConfigList.add(getFileOut("/templates/Vue3List.vue.vm", "Vue3List.vue"));
        fileOutConfigList.add(getFileOut("/templates/Vue3Edit.vue.vm", "Vue3Edit.vue"));

        // 自定义输出文件配置
        return fileOutConfigList;
    }

    @Override
    public IFileCreate getFileCreate() {
        // 自定义文件创建逻辑
        return (configBuilder, fileType, filePath) -> {
            File file = new File(filePath);
            // 检测文件的父目录是否存在
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                // 父目录不存在，创建父目录
                parentDir.mkdirs();
            }
            // 自定义判断是否创建文件的逻辑
            return !file.exists() || file.length() == 0;
        };
    }
}
