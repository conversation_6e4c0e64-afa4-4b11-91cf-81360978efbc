package org.ahead4.cdes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.ahead4.cdes.entity.SmsRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 短信记录Mapper接口
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
public interface SmsMapper extends BaseMapper<SmsRecord> {

    /**
     * 分页查询短信记录
     *
     * @param page 分页参数
     * @param phoneNumber 手机号码
     * @param serialNumber 流水号
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param status 发送状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页数据
     */
    IPage<SmsRecord> selectSmsRecordPage(Page<SmsRecord> page,
                                         @Param("phoneNumber") String phoneNumber,
                                         @Param("serialNumber") String serialNumber,
                                         @Param("businessType") String businessType,
                                         @Param("businessId") String businessId,
                                         @Param("status") Integer status,
                                         @Param("startTime") String startTime,
                                         @Param("endTime") String endTime);

    /**
     * 根据流水号及手机号批量更新短信发送结果
     *
     * @param records 短信记录列表，包含流水号、手机号和需要更新的字段
     * @return 更新的记录数
     */
    int updateBatchBySerialNumberAndPhoneNumber(@Param("list") List<SmsRecord> records);

}
