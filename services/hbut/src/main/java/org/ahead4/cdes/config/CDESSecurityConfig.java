package org.ahead4.cdes.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
public class CDESSecurityConfig extends WebSecurityConfigurerAdapter {
    @Autowired
    ExcludePathProperties excludePathProperties;
    @Autowired
    SysAuthenticationFilter sysAuthenticationFilter;
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
                .csrf().disable() // 根据需要禁用CSRF防护
                .authorizeRequests()
                .antMatchers(excludePathProperties.getPaths().toArray(new String[0])).permitAll()
                .antMatchers("/security/captcha/init", "/draw/captcha/{captchaId}", "/security/captcha/check",
                        "/sms/send-code").permitAll()
                .antMatchers("/user/info", "/router/tree/*", "/info/me", "/logout").authenticated()
                .antMatchers(
                        "/edu-email/**",
                        "/edu-email-log/**"
                ).hasAnyAuthority("ROLE_EduEmailAdmin", "ROLE_admin") // 要求邮箱管理员角色
                .antMatchers(
                        "/user/**",
                        "/sync-service/**",
//                        "/dept/**",
                        // "/role/**",
                        "/area/**",
                        "/app-credentials/**",
//                        "/api/**",
                        "/module/**",
                        "/datasources/**",
                        "/client/**",
                        "/client-details/**",
                        // "/dict/**",
                        // "/file/**",
                        "/oper-log/**",
                        "/permission/**",
                        "/router/**",
                        "/sql-template/**",
                        "/user-permission/**"
                ).hasAnyAuthority("ROLE_admin") // 设置登录和注册页面可以被访问其余请求需要认证
                .anyRequest().authenticated()  //
                .and()
                .addFilterBefore(sysAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
                .formLogin().disable()  // 禁用默认的登录表单
                .httpBasic().disable()  // 禁用基本的HTTP认证
                .csrf().disable();  // 禁用 CSRF
    }
}
