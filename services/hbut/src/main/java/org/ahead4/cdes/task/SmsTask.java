package org.ahead4.cdes.task;

import lombok.extern.slf4j.Slf4j;
import org.ahead4.cdes.entity.dto.SmsReplyResponse;
import org.ahead4.cdes.entity.dto.SmsReportResponse;
import org.ahead4.cdes.service.SmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 短信相关定时任务
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
@Slf4j
@Component
public class SmsTask {

    @Autowired
    private SmsService smsService;

    /**
     * 定时查询短信回执
     * 每1分钟执行一次
     */
//    @Scheduled(fixedRate = 60000)
    public void queryReport() {
        log.info("开始执行短信回执查询定时任务");
        try {
            SmsReportResponse response = smsService.queryReport();
            if (response.isSuccess() && response.getReportItems() != null && !response.getReportItems().isEmpty()) {
                log.info("成功获取短信回执数据，共{}条", response.getReportItems().size());
                // 更新短信回执状态
                smsService.updateSmsReports(response.getReportItems());
            } else {
                log.info("暂无短信回执数据");
            }
        } catch (Exception e) {
            log.error("短信回执查询定时任务执行异常", e);
        }
        log.info("短信回执查询定时任务执行完成");
    }

    /**
     * 定时查询短信上行回复
     * 每1分钟执行一次
     */
//    @Scheduled(fixedRate = 60000)
    public void queryReply() {
        log.info("开始执行短信上行回复查询定时任务");
        try {
            SmsReplyResponse response = smsService.queryReply();
            if (response.isSuccess() && response.getReplys() != null && !response.getReplys().isEmpty()) {
                log.info("成功获取短信上行回复数据，共{}条", response.getReplys().size());
                // 保存短信上行回复
                smsService.saveSmsReplies(response.getReplys());
                // 确认短信上行回复
                smsService.confirmReply(response.getId());
            } else {
                log.info("暂无短信上行回复数据");
            }
        } catch (Exception e) {
            log.error("短信上行回复查询定时任务执行异常", e);
        }
        log.info("短信上行回复查询定时任务执行完成");
    }
}
