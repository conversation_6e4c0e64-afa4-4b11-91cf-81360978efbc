package org.ahead4.cdes.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 每日业务编码映射器
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
public interface CodeMapper {

    /**
     * 初始化业务编码
     *
     * @param sequenceName 序列名称
     */
    void initBillDailyCode(@Param("sequenceName") String sequenceName);

    /**
     * 清理过期编码
     *
     * @param sequenceName 序列名称
     */
    void clearExpiredCode(@Param("sequenceName") String sequenceName);

    /**
     * 按日期获取序列列表
     */
    List<String> getSequenceList(@Param("date") String date);

    @Select("SELECT nextval(#{sequenceName})")
    Long nextVal(@Param("sequenceName") String sequenceName);
}
