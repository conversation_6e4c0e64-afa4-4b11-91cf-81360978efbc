package org.ahead4.cdes.service.impl;

import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.cdes.config.SmsConfig;
import org.ahead4.cdes.constant.CommonConstant;
import org.ahead4.cdes.entity.SmsRecord;
import org.ahead4.cdes.entity.SmsReply;
import org.ahead4.cdes.entity.dto.SmsQueryRequest;
import org.ahead4.cdes.entity.dto.SmsReplyConfirmResponse;
import org.ahead4.cdes.entity.dto.SmsReplyResponse;
import org.ahead4.cdes.entity.dto.SmsReportResponse;
import org.ahead4.cdes.entity.dto.SmsSendRequest;
import org.ahead4.cdes.entity.dto.SmsSendResponse;
import org.ahead4.cdes.mapper.SmsMapper;
import org.ahead4.cdes.mapper.SmsReplyMapper;
import org.ahead4.cdes.service.CodeService;
import org.ahead4.cdes.service.SmsService;
import org.ahead4.common.service.CacheService;
import org.ahead4.web.exception.RestException;
import org.ahead4.workflow.utils.DateUtils;
import org.ahead4.workflow.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 短信服务实现类
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SmsServiceImpl extends ServiceImpl<SmsMapper, SmsRecord> implements SmsService {

    private final CacheService redisService;

    private final SmsConfig smsConfig;

    private final SmsReplyMapper smsReplyMapper;

    private final CodeService codeService;

    /**
     * 验证码Redis前缀
     */
    private static final String SMS_CODE_PREFIX = "sms:code:";

    /**
     * 最后一次回复ID Redis键
     */
    private static final String SMS_LAST_REPLY_ID_KEY = "sms:last_reply_id";

    @Override
    public SmsSendResponse sendSms(SmsSendRequest request, Boolean skipConfigEnableCheck) {
        log.info("发送短信请求: {}", request);
        if (!skipConfigEnableCheck && (smsConfig == null || !smsConfig.getEnable())) {
            log.warn("短信服务未启用，直接返回。请求手机号: {}", request.getPhoneNumbers());
            return SmsSendResponse.failure("短信服务未启用", "短信服务未启用", request.getPhoneNumbers());
        }
        final List<String> proxyNumPool = smsConfig.getProxyNumPool();

        try {
            // 生成流水号（如果未提供）
            if (StringUtils.isEmpty(request.getSerialNumber())) {
                request.setSerialNumber(generateSerialNumber());
            }

            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            params.put("SpCode", smsConfig.getSpCode());
            params.put("LoginName", smsConfig.getLoginName());
            params.put("Password", smsConfig.getPassword());
            params.put("MessageContent", request.getMessageContent());
            params.put("UserNumber", request.getPhoneNumbers());
            params.put("SerialNumber", request.getSerialNumber());
            params.put("f", request.getF());

            if (StringUtils.isNotEmpty(request.getTemplateId())) {
                params.put("templateId", request.getTemplateId());
            }
            // 记录短信发送记录
            String[] phoneNumbers = request.getPhoneNumbers().split(",");
            // 代理号码池不空的情况下，每次都随机一个号码
            final boolean enableProxy = CollectionUtils.isNotEmpty(proxyNumPool);

            for (String phoneNumber : phoneNumbers) {
                SmsRecord smsRecord = new SmsRecord();
                smsRecord.setPhoneNumber(enableProxy ? proxyNumPool.get(new Random().nextInt(proxyNumPool.size())) :
                        phoneNumber);
                smsRecord.setContent(request.getMessageContent());
                smsRecord.setType(request.getType());
                smsRecord.setTemplateId(request.getTemplateId());
                if (request.getTemplateParams() != null) {
                    smsRecord.setTemplateParams(JsonUtils.toJsonString(request.getTemplateParams()));
                }
                smsRecord.setStatus(2); // 处理中
                smsRecord.setSendTime(new Date());
                smsRecord.setBusinessType(request.getBusinessType());
                smsRecord.setBusinessId(request.getBusinessId());
                smsRecord.setSerialNumber(request.getSerialNumber());

                // 保存记录
                save(smsRecord);
            }

            // 调用短信发送接口
            String url = smsConfig.getApiUrl() + smsConfig.getSendUrl();
            // 根据5.0平台接口规范，设置编码为GBK
            final String result = requestSmsApi(url, params, "短信发送接口返回: {}");

            // 解析返回结果
            Map<String, String> resultMap = parseResult(result);
            String resultCode = resultMap.getOrDefault("result", "-1");
            String description = resultMap.getOrDefault("description", "未知错误");
            String failList = resultMap.getOrDefault("failList", "");
            String taskId = resultMap.getOrDefault("taskid", "");

            // 更新发送状态
            if ("0".equals(resultCode)) {
                // 更新所有记录为发送成功
                baseMapper.updateBatchBySerialNumberAndPhoneNumber(
                        Arrays.stream(phoneNumbers)
                                .filter(phone -> !failList.contains(phone))
                                .map(phone -> {
                                    SmsRecord record = new SmsRecord();
                                    record.setPhoneNumber(phone);
                                    record.setSerialNumber(request.getSerialNumber());
                                    record.setStatus(1); // 成功
                                    record.setReceiptDesc("发送成功");
                                    record.setTaskId(taskId);
                                    return record;
                                })
                                .collect(Collectors.toList()));

                // 如果有失败的号码，更新失败记录
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(failList)) {
                    String[] failPhones = failList.split(",");
                    baseMapper.updateBatchBySerialNumberAndPhoneNumber(
                            Arrays.stream(failPhones).map(phone -> {
                                SmsRecord record = new SmsRecord();
                                record.setPhoneNumber(phone);
                                record.setSerialNumber(request.getSerialNumber());
                                record.setStatus(0); // 失败
                                record.setReceiptDesc("无效号码");
                                return record;
                            }).collect(Collectors.toList()));
                }

                return SmsSendResponse.success(taskId);
            } else {
                // 更新所有记录为发送失败
                baseMapper.updateBatchBySerialNumberAndPhoneNumber(
                        Arrays.stream(phoneNumbers)
                                .map(phone -> {
                                    SmsRecord record = new SmsRecord();
                                    record.setPhoneNumber(phone);
                                    record.setSerialNumber(request.getSerialNumber());
                                    record.setStatus(0); // 失败
                                    record.setReceiptDesc(description);
                                    return record;
                                })
                                .collect(Collectors.toList()));

                return SmsSendResponse.failure(resultCode, description, failList);
            }
        } catch (Exception e) {
            log.error("发送短信异常", e);
            return SmsSendResponse.failure("-1", e.getMessage(), "");
        }
    }

    @Override
    public SmsSendResponse sendSms(SmsSendRequest request) {
        return sendSms(request, false);
    }

    @Override
    public String sendVerificationCode(String phoneNumber, String businessType) {
        // 生成6位随机验证码
        String code = generateVerificationCode();

        // 获取验证码模板
        String expireMinutes = String.valueOf(smsConfig.getVerificationCode().getExpireMinutes());
        String templateId = smsConfig.getVerificationCode().getTemplateId();
        String contentTemp = smsConfig.getVerificationCode().getContent();

        // 构建短信内容
        Map<String, String> templateParams = new HashMap<>();
        templateParams.put("code", code);
        templateParams.put("expireMinutes", expireMinutes);
        StringSubstitutor substitutor = new StringSubstitutor(templateParams)
                // 允许未定义变量
                .setEnableUndefinedVariableException(false)
                // 未定义变量会被替换成空字符串
                .setPreserveEscapes(false)
                .setVariableResolver(key -> templateParams.containsKey(key) ?
                        String.valueOf(templateParams.get(key)) : "default");
        final String content = substitutor.replace(contentTemp);

//        2431012310488
        // 您的验证码为${code}，有效期{$smsConfig.getVerificationCodeExpireMinutes()}分钟

        // 构建短信发送请求
        SmsSendRequest request = new SmsSendRequest();
        request.setPhoneNumbers(phoneNumber);
        request.setType(1); // 验证码类型
        request.setTemplateId(templateId);
        request.setBusinessType(businessType);
        request.setMessageContent(content);
        request.setTemplateParams(templateParams);
        request.setSerialNumber(generateSerialNumber());

        // 发送短信
        SmsSendResponse response = sendSms(request, true);

        if (response.isSuccess()) {
            // 将验证码存入Redis，设置过期时间
            String redisKey = CommonConstant.PRE_IMAGE_CODE + businessType + ":" + phoneNumber;
            redisService.set(redisKey, code, smsConfig.getVerificationCodeExpireMinutes() * 60L);
            return code;
        } else {
            log.error("发送验证码失败: {}", response.getDescription());
            return null;
        }
    }

    @Value("${sms.dev:}")
    boolean isDev;
    @Value("${sms.super-code:}")
    String superCode;
    @Override
    public boolean verifyCode(String phoneNumber, String code, String businessType) {
        // 调试模式下，只要超级密码正确就直接通过验证
        if (isDev && StringUtils.equals(superCode, code)) {
            return true;
        }
        if (StringUtils.isAnyBlank(phoneNumber, code, businessType)) {
            return false;
        }

        // 从Redis获取验证码
        String redisKey = CommonConstant.PRE_IMAGE_CODE + businessType + ":" + phoneNumber;
        String savedCode = redisService.get(redisKey);

        // 验证码匹配则删除Redis中的验证码并返回true
        if (code.equals(savedCode)) {
            redisService.del(redisKey);
            return true;
        }

        return false;
    }

    @Override
    public SmsReportResponse queryReport() {
        try {
            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            params.put("SpCode", smsConfig.getSpCode());
            params.put("LoginName", smsConfig.getLoginName());
            params.put("Password", smsConfig.getPassword());

            // 调用回执查询接口
            String url = smsConfig.getApiUrl() + smsConfig.getReportUrl();
            // 根据5.0平台接口规范，设置编码为GBK
            final String result = requestSmsApi(url , params, "短信回执查询接口返回: {}");

            // 解析返回结果
            Map<String, String> resultMap = parseResult(result);
            String resultCode = resultMap.getOrDefault("result", "-1");
            String out = resultMap.getOrDefault("out", "");

            if ("0".equals(resultCode)) {
                return SmsReportResponse.success(resultCode, out);
            } else {
                return SmsReportResponse.failure(resultCode);
            }
        } catch (Exception e) {
            log.error("查询短信回执异常", e);
            return SmsReportResponse.failure("-1");
        }
    }

    @Override
    public SmsReplyResponse queryReply() {
        try {
            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            params.put("SpCode", smsConfig.getSpCode());
            params.put("LoginName", smsConfig.getLoginName());
            params.put("Password", smsConfig.getPassword());

            // 调用上行回复查询接口
            String url = smsConfig.getApiUrl() + smsConfig.getReplyUrl();
            // 根据5.0平台接口规范，设置编码为GBK
            final String result = requestSmsApi(url , params, "短信上行回复查询接口返回: {}");

            // 解析返回结果
            if (result.startsWith("result=0")) {
                // 提取JSON部分
                int jsonStart = result.indexOf("[");
                int jsonEnd = result.lastIndexOf("]") + 1;

                if (jsonStart > 0 && jsonEnd > jsonStart) {
                    String jsonStr = result.substring(jsonStart, jsonEnd);

                    // 提取其他参数
                    Map<String, String> resultMap = parseResult(result);
                    String resultCode = resultMap.getOrDefault("result", "-1");
                    String confirmTime = resultMap.getOrDefault("confirm_time", "");
                    String id = resultMap.getOrDefault("id", "");

                    // 解析JSON
                    List<SmsReplyResponse.SmsReplyItem> replyItems = parseReplyJson(jsonStr);

                    return SmsReplyResponse.success(resultCode, confirmTime, id, replyItems);
                }
            }

            // 解析失败或无回复
            Map<String, String> resultMap = parseResult(result);
            String resultCode = resultMap.getOrDefault("result", "-1");
            return SmsReplyResponse.failure(resultCode);
        } catch (Exception e) {
            log.error("查询短信上行回复异常", e);
            return SmsReplyResponse.failure("-1");
        }
    }

    private static String requestSmsApi(String url, Map<String, String> params, String s) {
        String result = null;

        final HttpRequest httpRequest =  HttpUtil.createPost(url)
                                                 .header("Content-Type", "application/x-www-form-urlencoded")
                                                 .charset(Charset.forName("GBK")).formStr(params);
        try (HttpResponse response = httpRequest.execute()) {
            result = response.body();
        } catch (HttpException e) {
            log.error(s , url, e);
            throw new RestException(s);
        }
        log.info(s, result);
        return result;
    }

    @Override
    public SmsReplyConfirmResponse confirmReply(String id) {
        try {
            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            params.put("SpCode", smsConfig.getSpCode());
            params.put("LoginName", smsConfig.getLoginName());
            params.put("Password", smsConfig.getPassword());
            params.put("id", id);

            // 调用上行回复确认接口
            String url = smsConfig.getApiUrl() + smsConfig.getReplyConfirmUrl();
            // 根据5.0平台接口规范，设置编码为GBK
            final String result = requestSmsApi(url , params, "短信上行回复确认接口返回: {}");

            // 解析返回结果
            Map<String, String> resultMap = parseResult(result);
            String resultCode = resultMap.getOrDefault("result", "-1");

            if ("0".equals(resultCode)) {
                // 保存最后确认的ID
                redisService.set(SMS_LAST_REPLY_ID_KEY, id);
                return SmsReplyConfirmResponse.success(resultCode);
            } else {
                return SmsReplyConfirmResponse.failure(resultCode);
            }
        } catch (Exception e) {
            log.error("确认短信上行回复异常", e);
            return SmsReplyConfirmResponse.failure("-1");
        }
    }

    @Override
    public IPage<SmsRecord> querySmsRecords(SmsQueryRequest request) {
        Page<SmsRecord> page = new Page<>(request.getPageNum(), request.getPageSize());

        // 格式化日期
        String startTime = null;
        String endTime = null;
        if (request.getStartTime() != null) {
            startTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, request.getStartTime());
        }
        if (request.getEndTime() != null) {
            endTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, request.getEndTime());
        }

        // 调用Mapper查询
        return baseMapper.selectPage(page,
                Wrappers.<SmsRecord>lambdaQuery().eq(StringUtils.isNoneBlank(request.getPhoneNumber()),
                        SmsRecord::getPhoneNumber, request.getPhoneNumber())
                        .eq(StringUtils.isNoneBlank(request.getBatchId()), SmsRecord::getSerialNumber, request.getBatchId())
                        .eq(StringUtils.isNoneBlank(request.getBusinessType()), SmsRecord::getBusinessType, request.getBusinessType()));
    }

    @Override
    public SmsRecord getBySerialNumber(String serialNumber) {
        if (StringUtils.isBlank(serialNumber)) {
            return null;
        }

        LambdaQueryWrapper<SmsRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SmsRecord::getSerialNumber, serialNumber);
        return getOne(wrapper);
    }

    @Override
    public SmsRecord getByBusinessId(String businessId) {
        if (StringUtils.isBlank(businessId)) {
            return null;
        }

        LambdaQueryWrapper<SmsRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SmsRecord::getBusinessId, businessId);
        return getOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSmsReplies(List<SmsReplyResponse.SmsReplyItem> replyItems) {
        if (replyItems == null || replyItems.isEmpty()) {
            return false;
        }

        List<SmsReply> smsReplies = replyItems.stream().map(item -> {
            SmsReply smsReply = new SmsReply();
            smsReply.setReplyId(item.getId());
            smsReply.setPhoneNumber(item.getMdn());
            smsReply.setCallMdn(item.getCallmdn());
            smsReply.setContent(item.getContent());
            try {
                smsReply.setReplyTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(item.getReplyTime()));
            } catch (Exception e) {
                log.error("解析回复时间异常", e);
                smsReply.setReplyTime(new Date());
            }
            smsReply.setIsConfirmed(false);
            return smsReply;
        }).collect(Collectors.toList());

        return smsReplyMapper.insertBatch(smsReplies);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSmsReports(List<SmsReportResponse.SmsReportItem> reportItems) {
        if (reportItems == null || reportItems.isEmpty()) {
            return false;
        }

        for (SmsReportResponse.SmsReportItem item : reportItems) {
            // 查询对应的短信记录
            LambdaQueryWrapper<SmsRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SmsRecord::getSerialNumber, item.getSerialNumber())
                    .eq(SmsRecord::getPhoneNumber, item.getPhoneNumber());

            SmsRecord smsRecord = getOne(wrapper);
            if (smsRecord != null) {
                // 更新回执状态
                smsRecord.setReceiptTime(new Date());
                smsRecord.setReceiptCode(item.getStatus());
                smsRecord.setReceiptDesc(getReceiptDesc(item.getStatus()));

                // 更新发送状态
                if ("0".equals(item.getStatus())) {
                    smsRecord.setStatus(1); // 成功
                } else {
                    smsRecord.setStatus(0); // 失败
                }

                updateById(smsRecord);
            }
        }

        return true;
    }

    /**
     * 生成流水号
     *
     * @return 流水号
     */
    private String generateSerialNumber() {
        // final String code = codeService.generateCode("sms");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String timestamp = sdf.format(new Date());

        // 生成3位随机数
        int random = new Random().nextInt(900) + 100;

        return timestamp + random;
    }

    /**
     * 生成6位随机验证码
     *
     * @return 验证码
     */
    private String generateVerificationCode() {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }

    /**
     * 解析返回结果
     *
     * @param result 返回结果字符串
     * @return 解析后的Map
     */
    private Map<String, String> parseResult(String result) {
        Map<String, String> resultMap = new HashMap<>();
        if (StringUtils.isEmpty(result)) {
            return resultMap;
        }

        result = getJsonStr(result);

        String[] parts = result.split("&");
        for (String part : parts) {
            String[] keyValue = part.split("=", 2);
            if (keyValue.length == 2) {
                resultMap.put(keyValue[0], keyValue[1]);
            }
        }

        return resultMap;
    }

    /**
     * 解析回复JSON
     *
     * @param jsonStr JSON字符串
     * @return 回复项列表
     */
    private List<SmsReplyResponse.SmsReplyItem> parseReplyJson(String jsonStr) {
        List<SmsReplyResponse.SmsReplyItem> replyItems = new ArrayList<>();

        jsonStr = getJsonStr(jsonStr);

        // 简单解析JSON数组
        jsonStr = jsonStr.trim();
        if (jsonStr.startsWith("[") && jsonStr.endsWith("]")) {
            jsonStr = jsonStr.substring(1, jsonStr.length() - 1);

            // 分割每个对象
            String[] objects = jsonStr.split("\\},\\{");
            for (int i = 0; i < objects.length; i++) {
                String obj = objects[i];
                if (i == 0) {
                    obj = obj.startsWith("{") ? obj : "{" + obj;
                }
                if (i == objects.length - 1) {
                    obj = obj.endsWith("}") ? obj : obj + "}";
                }
                if (!obj.startsWith("{")) {
                    obj = "{" + obj;
                }
                if (!obj.endsWith("}")) {
                    obj = obj + "}";
                }

                // 解析对象
                Map<String, String> objMap = parseJsonObject(obj);

                SmsReplyResponse.SmsReplyItem item = new SmsReplyResponse.SmsReplyItem();
                item.setMdn(objMap.get("mdn"));
                item.setCallmdn(objMap.get("callmdn"));
                item.setContent(objMap.get("content"));
                item.setReplyTime(objMap.get("reply_time"));
                item.setId(objMap.get("id"));

                replyItems.add(item);
            }
        }

        return replyItems;
    }

    private static String getJsonStr(String jsonStr) {
        try {
            // 如果返回结果中有乱码，尝试使用GBK解码
            if (jsonStr.contains("\ufffd")) {
                log.info("检测到可能的编码问题，尝试使用GBK重新解析");
                // 假设原始字符串是UTF-8编码，将其转换为byte数组后使用GBK重新解码
                byte[] bytes = jsonStr.getBytes(java.nio.charset.StandardCharsets.ISO_8859_1);
                jsonStr = new String(bytes, Charset.forName("GBK"));
                log.info("解码后的结果：{}", jsonStr);
            }
        } catch (Exception e) {
            log.error("解码失败：{} ", jsonStr,  e);
        }
        return jsonStr;
    }

    /**
     * 解析JSON对象
     *
     * @param jsonObj JSON对象字符串
     * @return 解析后的Map
     */
    private Map<String, String> parseJsonObject(String jsonObj) {
        Map<String, String> objMap = new HashMap<>();

        jsonObj = getJsonStr(jsonObj);

        // 简单解析JSON对象
        jsonObj = jsonObj.trim();
        if (jsonObj.startsWith("{") && jsonObj.endsWith("}")) {
            jsonObj = jsonObj.substring(1, jsonObj.length() - 1);

            // 分割每个键值对
            String[] pairs = jsonObj.split(",");
            for (String pair : pairs) {
                String[] keyValue = pair.split(":", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim();

                    // 去除引号
                    key = key.startsWith("\"") && key.endsWith("\"") ? key.substring(1, key.length() - 1) : key;
                    value = value.startsWith("\"") && value.endsWith("\"") ? value.substring(1, value.length() - 1) :
                                    value;

                    objMap.put(key, value);
                }
            }
        }

        return objMap;
    }

    /**
     * 获取回执状态描述
     *
     * @param status 状态码
     * @return 状态描述
     */
    private String getReceiptDesc(String status) {
        switch (status) {
            case "0":
                return "成功到达";
            case "1":
                return "手机号停机或空号";
            case "2":
                return "关机时间长过期";
            case "3":
                return "网关拒绝关机时间长过期";
            case "4":
                return "其他";
            case "5":
                return "手机短信箱或者内存满了";
            case "6":
                return "网关黑名单";
            default:
                return "未知状态";
        }
    }
}
