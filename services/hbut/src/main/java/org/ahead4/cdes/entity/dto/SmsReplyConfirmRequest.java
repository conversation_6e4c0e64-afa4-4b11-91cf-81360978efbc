package org.ahead4.cdes.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 短信上行回复确认请求DTO
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
@Data
public class SmsReplyConfirmRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业编号
     */
    private String spCode;

    /**
     * 用户名
     */
    private String loginName;

    /**
     * 接口密钥
     */
    private String password;

    /**
     * 上次查询返回的最后一条回复的id号
     */
    private String id;
}
