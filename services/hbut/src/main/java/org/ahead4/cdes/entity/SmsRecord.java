package org.ahead4.cdes.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.ahead4.workflow.domain.common.BaseEntity;

import java.util.Date;

/**
 * 短信记录实体
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
@Data
@Accessors(chain = true)
@TableName("t_sms_record")
public class SmsRecord  {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 手机号码
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 短信内容
     */
    @TableField("content")
    private String content;

    /**
     * 短信类型（1：验证码，2：通知，3：营销）
     */
    @TableField("type")
    private Integer type;

    /**
     * 短信模板ID
     */
    @TableField("template_id")
    private String templateId;

    /**
     * 短信模板参数（JSON格式）
     */
    @TableField("template_params")
    private String templateParams;

    /**
     * 发送状态（0：失败，1：成功，2：处理中）
     */
    @TableField("status")
    private Integer status;

    /**
     * 发送时间
     */
    @TableField("send_time")
    private Date sendTime;

    /**
     * 回执时间
     */
    @TableField("receipt_time")
    private Date receiptTime;

    /**
     * 回执状态码
     */
    @TableField("receipt_code")
    private String receiptCode;

    /**
     * 回执描述
     */
    @TableField("receipt_desc")
    private String receiptDesc;

    /**
     * 业务类型
     */
    @TableField("business_type")
    private String businessType;

    /**
     * 业务ID
     */
    @TableField("business_id")
    private String businessId;

    /**
     * 流水号（20位数字，唯一）
     */
    @TableField("serial_number")
    private String serialNumber;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private String taskId;

    // @ApiModelProperty(value = "删除标志（0代表存在 时间戳代表删除时间）")
    // @TableField(value = "del_flag")
    // @TableLogic(value = "0", delval = "EXTRACT(EPOCH FROM NOW()) * 1000")
    // private String delFlag;

    @ApiModelProperty(value = "创建者")
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(value = "updator", fill = FieldFill.INSERT_UPDATE)
    private String updator;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
