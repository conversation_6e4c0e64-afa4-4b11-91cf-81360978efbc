package org.ahead4.cdes.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 短信上行回复确认响应DTO
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
@Data
public class SmsReplyConfirmResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 结果代码
     */
    private String result;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 创建成功响应
     *
     * @param result 结果代码
     * @return 响应对象
     */
    public static SmsReplyConfirmResponse success(String result) {
        SmsReplyConfirmResponse response = new SmsReplyConfirmResponse();
        response.setResult(result);
        response.setSuccess(true);
        return response;
    }

    /**
     * 创建失败响应
     *
     * @param result 结果代码
     * @return 响应对象
     */
    public static SmsReplyConfirmResponse failure(String result) {
        SmsReplyConfirmResponse response = new SmsReplyConfirmResponse();
        response.setResult(result);
        response.setSuccess(false);
        return response;
    }
}
