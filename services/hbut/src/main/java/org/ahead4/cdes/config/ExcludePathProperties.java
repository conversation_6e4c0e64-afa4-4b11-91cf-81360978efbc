package org.ahead4.cdes.config;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 白名单配置信息
 *
 * @author: xinfa
 * @date: 2024/12/04
 **/

@Data
@NoArgsConstructor
@ToString
@ConfigurationProperties(prefix = "exclude")
@Configuration
public class ExcludePathProperties {

    /**
     * 白名单
     */
    private List<String> paths;


}
