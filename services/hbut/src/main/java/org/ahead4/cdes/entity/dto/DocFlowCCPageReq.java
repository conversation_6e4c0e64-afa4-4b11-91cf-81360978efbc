package org.ahead4.cdes.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 公文抄送分页请求
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@ApiModel(value = "公文抄送分页请求", description = "公文抄送分页请求")
public class DocFlowCCPageReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户 ID
     */
    @ApiModelProperty(value = "用户 ID")
    private String userId;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "流程编号")
    private String flowCode;

    @ApiModelProperty(value = "公文标题")
    private String docTitle;

    @ApiModelProperty(value = "公文类型")
    private String docType;

    @ApiModelProperty(value = "当前页码")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页记录数")
    private Integer pageSize = 10;
}
