package org.ahead4.cdes.config;

import com.google.common.base.Function;
import com.google.common.base.Optional;
import com.google.common.base.Predicate;
import io.swagger.annotations.Api;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.service.VendorExtension;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 * </pre>
 *
 * <AUTHOR>
 * 2019年06月04日
 */
@Configuration
@EnableSwagger2
@Profile("swagger")
public class SwaggerConfig {
    // 定义分隔符
    private static final String splitor = ";";

    @Bean
    public Docket docketFactory() {
        ParameterBuilder parameterBuilder = new ParameterBuilder();
        List<Parameter> parameters = new ArrayList<>();
        parameterBuilder.name("authorization").description("当前用户TOKEN")
                .modelRef(new ModelRef("String"))
                .parameterType("header").required(false)
                .build();
        parameters.add(parameterBuilder.build());


        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(new ApiInfo(
                        " Cadre Democratic Evaluation System (CDES)\n" +
                                "干部民主测评系统。",
                        "干部民主测评系统",
                        "1.0",
                        "urn:tos",
                        ApiInfo.DEFAULT_CONTACT,
                        "private",
                        "private",
                        new ArrayList<VendorExtension>())
                ).enable(true)
                .globalOperationParameters(parameters)
                .select()
                .apis(RequestHandlerSelectors.withClassAnnotation(Api.class))
                .apis(basePackage("org.ahead4"))
                .paths(PathSelectors.any())
                .build();
    }

    public static Predicate<RequestHandler> basePackage(final String basePackage) {
        return input -> declaringClass(input).transform(handlerPackage(basePackage)).or(true);
    }

    private static Function<Class<?>, Boolean> handlerPackage(final String basePackage) {
        return input -> {
            // 循环判断匹配
            for (String strPackage : basePackage.split(splitor)) {
                boolean isMatch = input.getPackage().getName().startsWith(strPackage);
                if (isMatch) {
                    return true;
                }
            }
            return false;
        };
    }

    private static Optional<? extends Class<?>> declaringClass(RequestHandler input) {
        return Optional.fromNullable(input.declaringClass());
    }
}
