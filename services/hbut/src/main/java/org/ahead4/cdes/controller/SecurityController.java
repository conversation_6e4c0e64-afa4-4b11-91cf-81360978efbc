package org.ahead4.cdes.controller;

import cn.hutool.core.util.IdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.cdes.constant.CommonConstant;
import org.ahead4.cdes.utils.CreateVerifyCode;
import org.ahead4.common.dto.RSAKeys;
import org.ahead4.common.service.CacheService;
import org.ahead4.common.utils.RandomValidateCodeUtil;
import org.ahead4.permission.service.UserService;
import org.ahead4.redis.utils.RedisUtil;
import org.ahead4.security.dto.IUserDetail;
import org.ahead4.security.utils.AccessHolder;
import org.ahead4.security.utils.RSASecurityHolder;
import org.ahead4.web.exception.RestException;
import org.ahead4.web.presentate.HttpMsg;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.naming.AuthenticationException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

import static org.ahead4.cdes.utils.CaptchaHolder.CAPTCHA_KEY;
import static org.ahead4.cdes.utils.CaptchaHolder.USER_SHOULD_CAPTCHA_FLAG;

/**
 * <AUTHOR>
 * <p>
 * 登录服务
 */

@RestController()
//@RequestMapping(value = "security")
@Api(value = "安全机制API", tags = {"登录服务", "安全机制"})
@Slf4j
public class SecurityController {
    @Autowired
    UserService userService;
    @Autowired
    CacheService cacheService;
//    @PostMapping("/stop-impersonate")
//    @ApiOperation(value = "停止假冒")
//    public IUserDetail stopImpersonate() {
//        if(!CollectionUtils.containsAny(AccessHolder.roleCodes(), Arrays.asList("admin"))) {
//            throw new RestException("无权限！", HttpStatus.FORBIDDEN);
//        }
//        IUserDetail user = cacheService.get("IMPERSONATE:"+AccessHolder.token());
//        AccessHolder.user(user);
//        return user;
//    }
    @GetMapping("/impersonate/{username}")
    @ApiOperation(value = "获取当前登录用户信息并将token续期")
    public IUserDetail impersonate(@PathVariable String username) {
        if(!CollectionUtils.containsAny(AccessHolder.roleCodes(), Arrays.asList("admin"))) {
            throw new RestException("无权限！", HttpStatus.FORBIDDEN);
        }
//        cacheService.set("IMPERSONATE:"+AccessHolder.token(), AccessHolder.user());
        IUserDetail user = userService.findByUsername(username);
        AccessHolder.user(user);
        return user;
    }
    @GetMapping("/info/me")
    @ApiOperation(value = "获取当前登录用户信息并将token续期")
    public IUserDetail userInfo() {
        IUserDetail user = userService.findByUsername(AccessHolder.username());
        return AccessHolder.user(user);
    }
    @GetMapping("/info/check")
    @ApiOperation(value = "检查当前登录用户信息")
    public IUserDetail userInfoCache() {
        return AccessHolder.user();
    }
    /**
     * 获取RSA公钥
     * @return
     */
    @GetMapping("/security/rsa-public-key")
    @ApiOperation(value = "获取RSA公钥")
    public RSAKeys getRsaPublicKey() throws NoSuchAlgorithmException {
        return RSASecurityHolder.getKeyPairs();
    }

    /**
     * 检查是否需要验证码
     */
    @GetMapping(value = "/security/has-captcha/{username}")
    public HttpMsg hasCaptcha(@PathVariable String username) {
        return HttpMsg.ok().put("hasCaptcha", CacheService.getInstance().hasKey(USER_SHOULD_CAPTCHA_FLAG + username));
    }
    /**
     * 生成验证码
     */
    @GetMapping(value = "/security/captcha.jpg")
    public void getCaptcha(HttpServletRequest request, HttpServletResponse response) {
        try {
            String username = request.getParameter("username");
            if (StringUtils.isEmpty(username)) {
                throw new AuthenticationException("请输入账号！");
            }
            if(!RedisUtil.hasKey(USER_SHOULD_CAPTCHA_FLAG + username)) {
                throw new AuthenticationException("用户无需验证码登录！");
            }
            response.setContentType("image/jpeg");
            //不缓存
            response.setHeader("Pragma", "No-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setDateHeader("Expire", 0);
            //输出验证码图片方法
            String captcha = RandomValidateCodeUtil.genCaptcha(request, response);
            // 五分钟有效
            RedisUtil.set(CAPTCHA_KEY+username, captcha, 5 * 60L);
        } catch (Exception e) {
            log.error("获取验证码失败>>>>   ", e);
        }
    }


    /**
     * 初始化验证码
     *
     * @param isDigit 是数字
     * @param length  长度
     * @return {@link HttpMsg }
     */
    @GetMapping(value = "/security/captcha/init")
    @ApiOperation(value = "初始化验证码")
    public HttpMsg initCaptcha(@ApiParam(value = "是否数字验证码") @RequestParam(required = false, defaultValue = "false") Boolean isDigit,
                               @ApiParam(value = "验证码长度") @RequestParam(required = false, defaultValue = "4") Integer length) {

        String captchaKey = IdUtil.simpleUUID();
        String code;
        if (Boolean.TRUE.equals(isDigit)) {
            code = new CreateVerifyCode().randomDigit(length);
        } else {
            code = new CreateVerifyCode().randomStr(length);
        }
        // 缓存验证码 120秒
        RedisUtil.set(CommonConstant.PRE_IMAGE_CODE + captchaKey, code, 120L);
        return HttpMsg.ok().data(captchaKey);
    }

    /**
     * 绘制验证码
     *
     * @param captchaKey CAPTCHA ID （验证码 ID）
     * @param response  响应
     * @throws IOException io异常
     */
    @GetMapping(value = "/draw/captcha/{captchaKey}")
    @ApiOperation(value = "根据验证码Key获取验证码图片")
    public void drawCaptcha(@PathVariable("captchaKey") String captchaKey,
                            HttpServletResponse response) throws IOException {
        // 得到验证码 生成指定验证码
        String code = RedisUtil.get(CommonConstant.PRE_IMAGE_CODE + captchaKey);
        CreateVerifyCode vCode = new CreateVerifyCode(116, 36, 4, 10, code);
        response.setContentType("image/png");
        vCode.write(response.getOutputStream());
    }

    /**
     * 验证码校验
     */
    @PostMapping(value = "/security/captcha/check")
    @ApiOperation(value = "验证码校验")
    public HttpMsg checkCaptcha(@RequestParam String captchaId, @RequestParam String captcha) {
        String code = RedisUtil.get(CommonConstant.PRE_IMAGE_CODE + captchaId);
        if (StringUtils.isBlank(code) || !code.equalsIgnoreCase(captcha)) {
            return HttpMsg.error("验证码错误");
        }
        return HttpMsg.ok();
    }

}