package org.ahead4.cdes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.Editor;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.cdes.entity.DocFlow;
import org.ahead4.cdes.entity.dto.SmsSendRequest;
import org.ahead4.cdes.enums.DocFlowStatusEnum;
import org.ahead4.cdes.mapper.DocFlowMapper;
import org.ahead4.cdes.service.CodeService;
import org.ahead4.cdes.service.DocFlowService;
import org.ahead4.cdes.service.SmsService;
import org.ahead4.common.dto.PageParam;
import org.ahead4.fs.dto.ResourceFileDto;
import org.ahead4.permission.service.UserService;
import org.ahead4.security.dto.IUserDetail;
import org.ahead4.security.utils.AccessHolder;
import org.ahead4.web.exception.RestException;
import org.ahead4.workflow.service.IWfProcessService;
import org.ahead4.workflow.utils.SpringUtils;
import org.ahead4.workflow.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static org.ahead4.cdes.enums.DocFlowStatusEnum.*;
import static org.ahead4.jdbc.utils.MybatisPlusUtils.param2PlusPage;
import static org.ahead4.jdbc.utils.MybatisPlusUtils.param2PlusWrapper;

/**
 * <p>
 * 公文流转信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class DocFlowServiceImpl extends ServiceImpl<DocFlowMapper, DocFlow> implements DocFlowService {

    private final IWfProcessService processService;

    private final CodeService codeService;

    private final TaskService taskService;

    private final UserService userService;

    private final SmsService smsService;
    // 注入HttpServletRequest
    private final HttpServletRequest request;

    @Value("${kkview.host: https://hbut.54w.com/}")
    private String kkviewHost;

    @Override
    public DocFlow saveDocFlow(DocFlow entity) {
        if (StringUtils.isBlank(entity.getFlowCode())) {
            final String docFlowCode = codeService.generateCode();
            entity.setFlowCode(docFlowCode);
        }
        entity.setStatus("submit");
        return updateDocFileAndGenKKViewCache(entity);
    }

    /**
     * 更新 doc 文件并生成 kkview 缓存
     *
     * @param entity 实体
     * @return {@link DocFlow }
     */
    private DocFlow updateDocFileAndGenKKViewCache(DocFlow entity) {
        // 处理文件信息,重写文件名
        if (CollectionUtils.isNotEmpty(entity.getDocFiles())) {
            entity.getDocFiles().forEach(file -> {
                if (StringUtils.isNotBlank(file.getId()) || StringUtils.isNotBlank(file.getOriginalName())) {
                    file.setOriginalName(file.getId() + "_" + file.getOriginalName());
                }
            });
        }
        if (this.saveOrUpdate(entity)) {
            // 请求kklife预览接口，提前缓存，提高前台打开速度
            try {
                // host
                String host = StringUtils.isNotBlank(request.getHeader("Referer")) ?
                        request.getHeader("Referer") : request.getHeader("Host");
                if (org.apache.commons.lang3.StringUtils.endsWith(host, "/")) {
                    host = host.substring(0, host.length() - 1);
                }
                // 获取 cookie 中的Authorization token
                String token = null;
                final Cookie[] cookies = request.getCookies();

                if (cookies != null) {
                    for (Cookie cookie : cookies) {
                        if ("Authorization".equals(cookie.getName())) {
                            token = cookie.getValue();
                        }
                    }
                }
                String finalHost = host;
                String finalToken = token;
                new Thread(() -> {
                    getKkLifeViewUrl(entity.getDocFiles(), finalHost, finalToken);
                }).start();
                getKkLifeViewUrl(entity.getDocFiles(), host, token);
            } catch (Exception e) {
                log.error("加入转码队列失败：", e);
            }
        }
        return entity;
    }

    /**
     *
     * @param docFiles files
     * @param host host
     * @param token token
     * @return {@link List }
     */
    private List<String> getKkLifeViewUrl(List<ResourceFileDto> docFiles, String host, String token) {
        List<String> urls = new ArrayList<>();
        if (CollectionUtils.isEmpty(docFiles)) {
            return urls;
        }

        String finalToken = token;
        String finalHost = host;
        docFiles.forEach(file -> {
            // Step 1: 构建原始下载链接
            try {
                String downloadUrl = String.format("%s/docFlow-v1/file/download/%s?Authorization=%s&fullfilename=%s",
                        finalHost,
                        file.getId(),
                        finalToken,
                        URLEncoder.encode(file.getOriginalName(), "UTF-8"));
                String encodedUrl = URLEncoder.encode(Base64.encode(downloadUrl, "UTF-8"));
                log.info("{} 下载链接：{}", file.getId(), downloadUrl);
                String viewUrl = kkviewHost + "onlinePreview?url=" + encodedUrl;
                final String resultBody = HttpUtil.createGet(viewUrl).cookie("Authorization=" + finalToken).execute()
                        .body();
                log.info("{} 转码链接：{}, get result: {}", file.getId(), viewUrl, resultBody);
            } catch (UnsupportedEncodingException e) {
                log.error("{} 编码错误", file.getId(), e);
            }
        });
        return urls;
    }


    @Override
    public DocFlow addDocFlow(DocFlow entity) {
        entity = this.saveDocFlow(entity);
        final DocFlow docFlow = this.getById(entity.getId());
        if (StringUtils.isNotEmpty(docFlow.getProcInstId())) {
            throw new RestException("流程已启动，请勿重复操作");
        }
        if (Objects.nonNull(entity)) {
            // 转换变量
            final Map<String, Object> variableMap = this.genVariables(entity.getId());
            // 启动流程
            final String procInstId = processService.startProcessByDefKey("doc_flow_v2", entity.getId(), variableMap);
            if (procInstId != null) {
                // 保存流程实例id
                entity.setProcInstId(procInstId);
                this.update(null,
                        Wrappers.<DocFlow>lambdaUpdate().set(DocFlow::getProcInstId, procInstId ).eq(DocFlow::getId,
                                entity.getId()));
            }
        }
        return entity;
    }

    @Override
    public DocFlow updateDocFlow(DocFlow entity) {
        final DocFlow dbEntity = this.getById(entity.getId());
        if (dbEntity == null) {
            throw new RestException("数据不存在");
        }
        if (!entity.getStatus().equals(SUBMIT.getCode())) {
            throw new RestException("流程已启动，不支持修改基本信息");
        }
        entity.setUpdateTime(new Date());
        entity.setUpdator(AccessHolder.username());
        entity = this.saveDocFlow(entity);
        if (StringUtils.isNotBlank(entity.getProcInstId())) {
            // 更新流程变量
            Map<String, Object> variableMap = this.genVariables(entity.getId());
            final RuntimeService runtimeService = SpringUtils.getBean(RuntimeService.class);
            final ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(entity.getProcInstId()).singleResult();
            runtimeService.setVariables(entity.getProcInstId(), variableMap);
        }
        return entity;
    }

    /**
     * 生成流程变量
     * 只保留实体类的字段，忽略文件信息
     * 忽略的字段：docFiles、otherFiles
     * 转换为map
     * 转换key为驼峰命名法
     *
     * @param id 身份证
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    @Override
    public Map<String, Object> genVariables(String id) {
        DocFlow entity = this.getById(id);
        // 忽略文件信息
        Editor<String> keyEditor = key -> {
            switch (key) {
                case "docFiles":
                case "otherFiles":
                    return null; // 返回 null 表示忽略该字段
                default:
                    return key; // 其他字段正常返回
            }
        };
        // 转换为map
        Map<String, Object> map = new HashMap<>();
        BeanUtil.beanToMap(entity, map, true, keyEditor);
        return map;
    }

    @Override
    public IPage<DocFlow> ccPage(PageParam params) {
        return this.baseMapper.ccPage(param2PlusPage(params), param2PlusWrapper(params),   AccessHolder.username());
    }

    @Override
    public Boolean remind(String businessKey, String username) {
        final DocFlow docFlow = this.getById(businessKey);
        if (Objects.isNull(docFlow)) {
            throw new RestException("公文信息不存在,请检查");
        }
        final String status = docFlow.getStatus();
        DocFlowStatusEnum statusEnum = DocFlowStatusEnum.getByCode(status);
        if (Objects.isNull(statusEnum)) {
            throw new RestException("公文状态异常,请检查");
        }
        if (statusEnum.equals(SUBMIT)) {
            throw new RestException("公文未提交, 请先提交");
        }
        if (statusEnum.equals(CANCELED) || statusEnum.equals(TERMINATED) || statusEnum.equals(COMPLETED)) {
            throw new RestException("公文流程已取消或已终止,请检查");
        }
        final String procInstId = docFlow.getProcInstId();
        if (StringUtils.isBlank(procInstId)) {
            throw new RestException("公文未启动流程,请检查");
        }

        // 查询当前活动的任务
        List<Task> activeTasks = taskService.createTaskQuery().processInstanceId(procInstId).active().list();
        if (activeTasks.isEmpty()) {
            throw new RestException("公文流程已结束,请检查");
        }
        // 如果username不为空，则只检查当前用户的任务，并发送消息
        if (StringUtils.isNotBlank(username)) {
            activeTasks = activeTasks.stream().filter(task -> task.getAssignee().equals(username))
                    .collect(Collectors.toList());
            if (activeTasks.isEmpty()) {
                throw new RestException("未获取到当前用户的待办任务，请检查");
            }
        }
        final Set<String> mobiles = activeTasks.stream().map(task -> {
            final String assignee = task.getAssignee();
            // todo 通过assignee 去OA查询用户手机号
            return Optional.ofNullable(userService.findByUsername(assignee)).map(IUserDetail::getMobile).orElse(null);
        }).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(mobiles)) {
            throw new RestException("未获取到用户的手机号，请检查用户信息");
        }
        final SmsSendRequest smsSendRequest = new SmsSendRequest();
        smsSendRequest.setBusinessId(businessKey);
        smsSendRequest.setPhoneNumbers(String.join(",", mobiles));
        // 2431012310485
        // {文本1,12}提醒您，您有一个待审核的公文《{文本2,20}》，请您尽快前往公文系统处理。
        final String docTitle = docFlow.getDocTitle();
        // 截取前20个字符
        final String title = docTitle.substring(0, Math.min(docTitle.length(), 20));
        final String msg = String.format("%s提醒您，您有一个待审核的公文《%s》，请您尽快前往公文系统处理。", "公文系统", title);
        smsSendRequest.setMessageContent(msg);
        smsService.sendSms(smsSendRequest);
        return Boolean.TRUE;
    }

    @Override
    public IPage<DocFlow> finishedList(PageParam param) {
        return this.baseMapper.finishedList(param2PlusPage(param), param2PlusWrapper(param), AccessHolder.username());
    }
}
