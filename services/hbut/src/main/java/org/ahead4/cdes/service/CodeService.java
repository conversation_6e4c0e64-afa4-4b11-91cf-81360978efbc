package org.ahead4.cdes.service;

import org.springframework.scheduling.annotation.Scheduled;

public interface CodeService {

    String generateCode();


    /**
     * 生成编码
     *
     * @param prefix 前缀
     * @return 生成的编码
     */
    String generateCode(String prefix);

    /**
     * 清理过期的 SEQUENCE（定时任务）
     */
    @Scheduled(cron = "0 0 0 * * ?") // 每天 0 点执行
    void cleanUpExpiredSequences();
}