package org.ahead4.cdes.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短信查询请求DTO
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
@Data
public class SmsQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 批次号
     */
    private String batchId;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 发送状态（0：失败，1：成功，2：处理中）
     */
    private Integer status;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize = 10;
}
