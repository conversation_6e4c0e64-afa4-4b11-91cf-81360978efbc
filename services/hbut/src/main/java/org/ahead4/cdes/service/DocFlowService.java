package org.ahead4.cdes.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.ahead4.cdes.entity.DocFlow;
import org.ahead4.common.dto.PageParam;

import java.util.Map;

/**
 * <p>
 * 公文流转信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
public interface DocFlowService extends IService<DocFlow> {

    /**
     * 添加文档流程并开始审批
     *
     * @param entity 实体
     * @return {@link DocFlow }
     */
    DocFlow addDocFlow(DocFlow entity);

    DocFlow saveDocFlow(DocFlow entity);

    Map<String, Object> genVariables(String id);

    DocFlow updateDocFlow(DocFlow entity);

    IPage<DocFlow> ccPage(PageParam params);

    /**
     * 催办
     *
     * @param businessKey 业务密钥
     * @param username    用户名
     * @return {@link Boolean }
     */
    Boolean remind(String businessKey, String username);

    /**
     * 我参与的列表
     *
     * @param params 参数
     * @return {@link IPage }<{@link DocFlow }>
     */
    IPage<DocFlow> finishedList(PageParam params);
}
