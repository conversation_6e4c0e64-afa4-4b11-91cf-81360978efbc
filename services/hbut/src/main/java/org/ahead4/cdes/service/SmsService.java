package org.ahead4.cdes.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.ahead4.cdes.entity.SmsRecord;
import org.ahead4.cdes.entity.dto.SmsQueryRequest;
import org.ahead4.cdes.entity.dto.SmsReplyConfirmResponse;
import org.ahead4.cdes.entity.dto.SmsReplyResponse;
import org.ahead4.cdes.entity.dto.SmsReportResponse;
import org.ahead4.cdes.entity.dto.SmsSendRequest;
import org.ahead4.cdes.entity.dto.SmsSendResponse;

import java.util.List;

/**
 * 短信服务接口
 *
 * <AUTHOR>
 * @date 2025/04/15
 */
public interface SmsService {

    /**
     * 发送短信
     *
     * @param request         短信发送请求
     * @return 发送结果
     */
    SmsSendResponse sendSms(SmsSendRequest request);

    /**
     * 发送短信
     *
     * @param request               短信发送请求
     * @param skipConfigEnableCheck 是否跳过配置开关检查
     * @return 发送结果
     */
    SmsSendResponse sendSms(SmsSendRequest request, Boolean skipConfigEnableCheck);

    /**
     * 发送验证码短信
     *
     * @param phoneNumber 手机号码
     * @param businessType 业务类型
     * @return 验证码
     */
    String sendVerificationCode(String phoneNumber, String businessType);

    /**
     * 验证短信验证码
     *
     * @param phoneNumber 手机号码
     * @param code 验证码
     * @param businessType 业务类型
     * @return 是否验证成功
     */
    boolean verifyCode(String phoneNumber, String code, String businessType);

    /**
     * 查询短信回执
     *
     * @return 短信回执响应
     */
    SmsReportResponse queryReport();

    /**
     * 查询短信上行回复
     *
     * @return 短信上行回复响应
     */
    SmsReplyResponse queryReply();

    /**
     * 确认短信上行回复
     *
     * @param id 上次查询返回的最后一条回复的id号
     * @return 短信上行回复确认响应
     */
    SmsReplyConfirmResponse confirmReply(String id);

    /**
     * 查询短信记录
     *
     * @param request 查询请求
     * @return 短信记录分页数据
     */
    IPage<SmsRecord> querySmsRecords(SmsQueryRequest request);

    /**
     * 根据流水号查询短信记录
     *
     * @param serialNumber 流水号
     * @return 短信记录
     */
    SmsRecord getBySerialNumber(String serialNumber);

    /**
     * 根据业务ID查询短信记录
     *
     * @param businessId 业务ID
     * @return 短信记录
     */
    SmsRecord getByBusinessId(String businessId);

    /**
     * 保存短信上行回复
     *
     * @param replyItems 短信上行回复项列表
     * @return 是否保存成功
     */
    boolean saveSmsReplies(List<SmsReplyResponse.SmsReplyItem> replyItems);

    /**
     * 更新短信回执状态
     *
     * @param reportItems 短信回执项列表
     * @return 是否更新成功
     */
    boolean updateSmsReports(List<SmsReportResponse.SmsReportItem> reportItems);
}
