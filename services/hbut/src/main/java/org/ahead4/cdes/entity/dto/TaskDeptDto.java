package org.ahead4.cdes.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "任务部门信息", description = "抄送人")
public class TaskDeptDto implements Serializable {

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String deptCode;

    /**
     * 显示名称
     */
    @ApiModelProperty(value = "显示名称")
    private String deptName;
}

