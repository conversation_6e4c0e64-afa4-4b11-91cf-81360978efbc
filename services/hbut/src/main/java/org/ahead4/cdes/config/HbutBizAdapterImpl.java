package org.ahead4.cdes.config;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.cdes.entity.DocFlow;
import org.ahead4.cdes.entity.dto.TaskDeptDto;
import org.ahead4.cdes.service.DocFlowService;
import org.ahead4.permission.entity.*;
import org.ahead4.permission.service.*;
import org.ahead4.security.dto.IUserDetail;
import org.ahead4.security.dto.IUserDetail.NameCodeDto;
import org.ahead4.security.utils.AccessHolder;
import org.ahead4.workflow.spi.BizSystemAdapter;
import org.ahead4.workflow.utils.SpringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class HbutBizAdapterImpl implements BizSystemAdapter {

    private final UserService userService;

    private final RoleService roleService;

    private final DeptService deptService;

    private final UserRoleService userRoleService;

    private final UserDeptService userDeptService;

    private final UserPositionService userPositionService;

    @Override
    public List<String> getTaskCandidateUsers(String taskDefinitionKey, Map<String, Object> variables,
            String businessKey) {
        return Collections.emptyList();
    }

    @Override
    public void onTaskCompleted(String taskDefinitionKey, String businessKey, Map<String, Object> variables) {
        log.info("任务完成：{}, businessKey: {}", taskDefinitionKey, businessKey);
    }

    @Override
    public void onProcessCompleted(String processInstanceId, String businessKey, Map<String, Object> variables) {
        log.info("流程完成：{} , businessKey: {}", processInstanceId, businessKey);
    }

    @Override
    public Map<String, Object> getBusinessFormData(String scopeType, String businessKey) {
        return Collections.emptyMap();
    }

    @Override
    public String getLonginUserId() {
        return Optional.ofNullable(AccessHolder.user()).map(IUserDetail::getUsername).orElse(null);
    }

    @Override
    public String getLoginUserName() {
        return Optional.ofNullable(AccessHolder.user()).map(IUserDetail::getDisplayname).orElse(null);
    }

    @Override
    public List<String> getLoginUserRoleIds() {
        return Optional.ofNullable(AccessHolder.user()).map(IUserDetail::getRoles).orElse(Collections.emptyList())
            .stream().map(NameCodeDto::getCode).collect(Collectors.toList());

    }

    @Override
    public List<String> getLoginUserDeptIds() {
        return Optional.ofNullable(AccessHolder.user()).map(IUserDetail::getDepts).orElse(Collections.emptyList())
           .stream().map(NameCodeDto::getCode).collect(Collectors.toList());
    }

    @Override
    public String getUserNameById(String userId) {
        return Optional.ofNullable(userId).map(userService::findByUsername).map(IUserDetail::getDisplayname).orElse(null);
    }

    @Override
    public String getNickNameById(String userId) {
        return Optional.ofNullable(userId).map(userService::findByUsername).map(IUserDetail::getDisplayname).orElse(null);
    }

    @Override
    public String getRoleNameById(String roleId) {
        return Optional.ofNullable(roleId).map(roleService::getById).map(Role::getName).orElse(null);
    }

    @Override
    public String getDeptNameById(String deptId) {
        return Optional.ofNullable(deptId).map(code -> deptService.getOne(Wrappers.<Dept>lambdaQuery().eq(Dept::getCode, code))).map(Dept::getName).orElse(null);
    }

    @Override
    public List<String> getUserIdsByRoleIds(List<String> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        final List<UserRole> list = userRoleService.list(Wrappers.<UserRole>lambdaQuery().in(UserRole::getRoleCode, roleIds));
        return list.stream().map(UserRole::getUsername).collect(Collectors.toList());
    }

    @Override
    public List<String> getUserIdsByDeptIds(List<String> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        final List<UserDept> list = userDeptService.list(Wrappers.<UserDept>lambdaQuery().in(UserDept::getDeptCode, deptIds));
        return list.stream().map(UserDept::getUsername).collect(Collectors.toList());
    }

    @Override
    public void updateStatus(String businessId, String status) {
        log.info("更新业务状态：{}, status: {}", businessId, status);
        final DocFlowService flowService = SpringUtils.getBean(DocFlowService.class);
        flowService.update(null, Wrappers.<DocFlow>lambdaUpdate().set(DocFlow::getStatus, status).eq(DocFlow::getId, businessId));
    }

    @Override
    public List<String> getDeptsInBusinessFormData(String id, String taskId) {
        final DocFlowService docFlowService = SpringUtils.getBean(DocFlowService.class);
        final DocFlow docFlow = docFlowService.getById(id);
        final List<TaskDeptDto> list =
                Optional.ofNullable(docFlow).map(BeanUtil::beanToMap).map(o -> o.get(taskId)).filter(List.class::isInstance)
                .map(List.class::cast).orElse(Collections.emptyList());
        return list.stream().map(TaskDeptDto::getDeptCode).collect(Collectors.toList());
    }

    @Override
    public List<String> getUserIdsByDeptAndPosition(List<String> deptCodes, List<String> postCodes) {
        final List<UserPosition> list = userPositionService.list(Wrappers.<UserPosition>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(deptCodes), UserPosition::getDeptCode, deptCodes)
                .in(CollectionUtils.isNotEmpty(postCodes), UserPosition::getPositionCode, postCodes));
        return list.stream().map(UserPosition::getUsername).collect(Collectors.toList());
    }
}
