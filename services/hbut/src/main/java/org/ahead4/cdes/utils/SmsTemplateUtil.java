package org.ahead4.cdes.utils;


import org.ahead4.workflow.utils.StringUtils;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 短信模板工具类
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
public class SmsTemplateUtil {

    /**
     * 模板变量正则表达式
     */
    private static final Pattern TEMPLATE_PATTERN = Pattern.compile("\\{([^{}]+)\\}");

    /**
     * 根据模板和参数生成短信内容
     *
     * @param template 模板
     * @param params 参数
     * @return 短信内容
     */
    public static String generateContent(String template, Map<String, String> params) {
        if (StringUtils.isEmpty(template)) {
            return "";
        }
        
        if (params == null || params.isEmpty()) {
            return template;
        }
        
        StringBuffer sb = new StringBuffer();
        Matcher matcher = TEMPLATE_PATTERN.matcher(template);
        
        while (matcher.find()) {
            String key = matcher.group(1);
            String value = params.getOrDefault(key, matcher.group(0));
            matcher.appendReplacement(sb, value);
        }
        
        matcher.appendTail(sb);
        return sb.toString();
    }
    
    /**
     * 验证短信内容是否符合模板
     *
     * @param template 模板
     * @param content 短信内容
     * @return 是否符合
     */
    public static boolean validateContent(String template, String content) {
        if (StringUtils.isEmpty(template) || StringUtils.isEmpty(content)) {
            return false;
        }
        
        // 将模板中的变量部分替换为正则表达式
        String regex = template.replaceAll("\\{([^{}]+)\\}", "(.+)");
        
        // 编译正则表达式
        Pattern pattern = Pattern.compile("^" + regex + "$");
        
        // 匹配内容
        Matcher matcher = pattern.matcher(content);
        
        return matcher.matches();
    }
    
    /**
     * 获取测试模板
     *
     * @param templateId 模板ID
     * @return 模板内容
     */
    public static String getTestTemplate(String templateId) {
        // 测试帐户模板1
        if ("TEST_TEMPLATE_1".equals(templateId)) {
            return "你有一项编号为{xxxxxxxxx}的事务需要处理{x}";
        }
        // 测试帐户模板2
        else if ("TEST_TEMPLATE_2".equals(templateId)) {
            return "您的验证码为{xxxxxxx}";
        }
        // 默认返回验证码模板
        else {
            return "您的验证码为{code}";
        }
    }
}
