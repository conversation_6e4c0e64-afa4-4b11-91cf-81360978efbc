package org.ahead4.cdes.config;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.security.dto.IUserDetail;
import org.ahead4.security.utils.AccessHolder;
import org.ahead4.web.exception.RestException;
import org.ahead4.web.presentate.HttpMsg;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.security.sasl.AuthenticationException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

/**
 * 线程拦截器，在线程结束后执行清除线程绑定token的方法
 */
@Slf4j
@Component
public class SysAuthenticationFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest req, HttpServletResponse red, FilterChain filterChain) throws ServletException, IOException {

        try {
            AccessHolder.clean();
            UserDetails userDetails = AccessHolder.user();
            if(userDetails != null) {
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
            filterChain.doFilter(req, red);
        }finally {
            AccessHolder.clean();
            SecurityContextHolder.getContext().setAuthentication(null);
        }
    }
}
