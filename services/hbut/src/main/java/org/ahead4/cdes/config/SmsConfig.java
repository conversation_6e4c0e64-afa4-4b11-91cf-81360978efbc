package org.ahead4.cdes.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 短信配置类
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sms")
public class SmsConfig {

    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 代理号码池：在启用的情况下，如果不为空，则发送到代理池中的号码中
     */
    private List<String> proxyNumPool;

    /**
     * 企业编号
     */
    private String spCode;

    /**
     * 用户名
     */
    private String loginName;

    /**
     * 接口密钥
     */
    private String password;

    /**
     * 接口地址
     */
    private String apiUrl = "https://api.ums86.com:9600/sms/Api";

    /**
     * 发送短信接口
     */
    private String sendUrl = "/Send.do";

    /**
     * 回执查询接口
     */
    private String reportUrl = "/report.do";

    /**
     * 上行回复查询接口
     */
    private String replyUrl = "/reply.do";

    /**
     * 上行回复确认接口
     */
    private String replyConfirmUrl = "/replyConfirm.do";

    /**
     * 验证码有效期（分钟）
     */
    private int verificationCodeExpireMinutes = 5;

    /**
     * 验证码模板ID
     */
    private String verificationCodeTemplateId = "2431012310488";

    /**
     * 验证码获取间隔时间（秒）
     */
    private int verificationCodeInterval = 120;

    /**
     * 同一手机号24小时内验证码发送最大次数
     */
    private int maxVerificationCodePerDay = 5;

    /**
     * 同一IP 24小时内验证码请求最大次数
     */
    private int maxRequestPerIpPerDay = 20;

    private VerificationCode verificationCode;

    @Setter
    @Getter
    public static class VerificationCode {
        private Long expireMinutes;
        private String templateId;
        private String content;
    }
}
