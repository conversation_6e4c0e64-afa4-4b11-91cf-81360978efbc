package org.ahead4.cdes.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.ahead4.security.utils.AccessHolder;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 *@description:在方法插入或修改之前拦截，设置字段默认值
 *<AUTHOR>
 *@classname MetaObjectHandlerConfig.java
 *@create 2023-06-04, 星期日, 20:42:17
 */
@Component
public class MetaObjectHandlerConfig implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        // 自动填充 createTime 和 createName
        this.strictInsertFill(metaObject, "createTime", Date.class, new Date()); // 填充创建时间
        this.strictInsertFill(metaObject, "creator", String.class, getCurrentUserName()); // 填充创建者

        // 自动填充 updateTime 和 updateName
        this.strictInsertFill(metaObject, "updateTime", Date.class, new Date()); // 填充更新时间
        this.strictInsertFill(metaObject, "updator", String.class, getCurrentUserName()); // 填充更新者
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        // 更新时自动填充 updateTime 和 updateName
        this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date()); // 填充更新时间
        this.strictUpdateFill(metaObject, "updator", String.class, getCurrentUserName()); // 填充更新者
    }

    private String getCurrentUserName() {
        // 获取当前用户名的逻辑, 这里可以从上下文中获取当前用户信息
        return AccessHolder.displayname(); // 实际应用中替换为实际的用户名获取逻辑
    }
}