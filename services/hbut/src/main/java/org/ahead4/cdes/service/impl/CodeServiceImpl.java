package org.ahead4.cdes.service.impl;

import lombok.RequiredArgsConstructor;
import org.ahead4.cdes.mapper.CodeMapper;
import org.ahead4.cdes.service.CodeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class CodeServiceImpl implements CodeService {

    private final CodeMapper codeMapper;

    private final SimpleDateFormat DATEFORMAT = new SimpleDateFormat("yyMMdd");
    private final SimpleDateFormat DATEFORMAT_YEAR = new SimpleDateFormat("yyyy");

    @Override
    public String generateCode() {
        String curYear = DATEFORMAT_YEAR.format(new Date());
        String sequenceName = "DF_" + curYear + "_seq";
        createSequenceIfNotExists(sequenceName);
        Long nextId = codeMapper.nextVal(sequenceName);
        String code = String.format("%04d", nextId);

        return curYear + code;
    }

    /**
     * 生成编码
     *
     * @param prefix 前缀
     * @return 生成的编码
     */
    @Override
    public synchronized String generateCode(String prefix) {
        if (StringUtils.isBlank(prefix)) {
            throw new IllegalArgumentException("Prefix cannot be empty.");
        }
        String today = DATEFORMAT.format(new Date());
        String sequenceName = prefix + "_" +  today + "_seq";

        // 如果 SEQUENCE 不存在，则创建
        createSequenceIfNotExists(sequenceName);

        // 获取下一个值
        Long nextId = codeMapper.nextVal(sequenceName);

        // 格式化为 4 位数字
        String code = String.format("%04d", nextId);
        return prefix + today + code;
    }

    /**
     * 创建 SEQUENCE（如果不存在）
     *
     * @param sequenceName SEQUENCE 名称
     */
    private void createSequenceIfNotExists(String sequenceName) {
        codeMapper.initBillDailyCode(sequenceName);
    }

    /**
     * 清理过期的 SEQUENCE（定时任务）
     */
//    @Scheduled(cron = "0 0 0 * * ?")
    @Override
    public void cleanUpExpiredSequences() {
        String thirtyDaysAgo = DATEFORMAT.format(new Date(System.currentTimeMillis() - 30L * 24 * 60 * 60 * 1000));
        final List<String> sequenceList = codeMapper.getSequenceList(thirtyDaysAgo);
        for (String sequence : sequenceList) {
            codeMapper.clearExpiredCode(sequence);
        }
    }
}
