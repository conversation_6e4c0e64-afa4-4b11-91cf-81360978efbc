package org.ahead4.cdes.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Map;

/**
 * 短信发送请求DTO
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
@Data
public class SmsSendRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号码，多个手机号用逗号分隔，最多100个号码
     */
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}(,1[3-9]\\d{9})*$", message = "手机号码格式不正确")
    private String phoneNumbers;

    /**
     * 短信内容，最大402个字或字符
     */
    @NotBlank(message = "短信内容不能为空")
    @Size(max = 402, message = "短信内容长度不能超过402个字符")
    private String messageContent;

    /**
     * 短信类型（1：验证码，2：通知，3：营销）
     */
    private Integer type = 2;

    /**
     * 短信模板ID
     */
    private String templateId;

    /**
     * 短信模板参数
     */
    private Map<String, String> templateParams;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 流水号，20位数字，唯一
     */
    private String serialNumber;

    /**
     * 提交时检测方式
     * 1 --- 提交号码中有效的号码仍正常发出短信，无效的号码在返回参数faillist中列出
     * 不为1 或该参数不存在 --- 提交号码中只要有无效的号码，那么所有的号码都不发出短信，无效号码在返回参数faillist中列出
     */
    private String f = "1";
}
