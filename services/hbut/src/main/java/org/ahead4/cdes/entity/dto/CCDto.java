package org.ahead4.cdes.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "抄送人信息", description = "抄送人")
public class CCDto implements Serializable {

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String username;

    /**
     * 显示名称
     */
    @ApiModelProperty(value = "显示名称")
    private String displayname;

    /**
     * 部门编码
     */
    @ApiModelProperty(value = "部门编码")
    private String deptCode;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String deptName;

    /**
     * 职务编码
     */
    @ApiModelProperty(value = "职务编码")
    private String markCode;

    /**
     * 选人类型
     */
    @ApiModelProperty(value = "选人类型", notes = "init： 初始化; cc： 抄送; add： 加签")
    private String type = "cc";
}

