package org.ahead4.cdes.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.ahead4.workflow.domain.common.BaseEntity;

import java.util.Date;

/**
 * 短信上行回复实体
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
@Data
@Accessors(chain = true)
@TableName("t_sms_reply")
public class SmsReply  {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 回复ID（平台返回的ID）
     */
    @TableField("reply_id")
    private String replyId;

    /**
     * 手机号码
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 接入号
     */
    @TableField("call_mdn")
    private String callMdn;

    /**
     * 回复内容
     */
    @TableField("content")
    private String content;

    /**
     * 回复时间
     */
    @TableField("reply_time")
    private Date replyTime;

    /**
     * 是否已确认
     */
    @TableField("is_confirmed")
    private Boolean isConfirmed;

    /**
     * 确认时间
     */
    @TableField("confirm_time")
    private Date confirmTime;

    // @ApiModelProperty(value = "删除标志（0代表存在 时间戳代表删除时间）")
    // @TableField(value = "del_flag")
    // @TableLogic(value = "0", delval = "EXTRACT(EPOCH FROM NOW()) * 1000")
    // private String delFlag;

    @ApiModelProperty(value = "创建者")
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(value = "updator", fill = FieldFill.INSERT_UPDATE)
    private String updator;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
