package org.ahead4.cdes;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import net.unicon.cas.client.configuration.EnableCasClient;
import org.ahead4.workflow.spi.BizSystemAdapter;
import org.ahead4.workflow.spi.DefaultBizSystemAdapterImpl;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
//import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(
        scanBasePackages = {
                "org.ahead4",
                "org.ahead4.web",
                "org.ahead4.redis",
                "org.ahead4.common",
                "org.ahead4.dbms",
                "org.ahead4.logger",
                "org.ahead4.security",
                "org.ahead4.email",
                "org.ahead4.email",
                "org.ahead4.fs",
                "org.ahead4.cdes",
                "org.ahead4.oauth.service",
                "org.ahead4.oauth.application"
        },
        exclude = {
                DruidDataSourceAutoConfigure.class,
                DataSourceAutoConfiguration.class
        }
)
@EntityScan({"org.ahead4.cdes.**", "org.ahead4.hbut.**", "org.ahead4.email.**", "org.ahead4.fs.**"})
@EnableScheduling
@EnableAsync
@MapperScan(basePackages = {
        "org.ahead4.hbut.mapper",
        "org.ahead4.cdes.mapper",
        "org.ahead4.workflow.mapper",
        "org.ahead4.email.mapper",
        "org.ahead4.fs.mapper",
        "org.ahead4.common.mapper",
        "org.ahead4.logger.mapper"
})
@EnableCasClient
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@EnableRetry
public class HbutApplication {

    public static void main(String[] args) {
        SpringApplication.run(HbutApplication.class, args);
    }

    @Bean
    @ConditionalOnMissingBean
    public BizSystemAdapter defaultBizSystemAdapter() {
        return new DefaultBizSystemAdapterImpl();
    }

}
