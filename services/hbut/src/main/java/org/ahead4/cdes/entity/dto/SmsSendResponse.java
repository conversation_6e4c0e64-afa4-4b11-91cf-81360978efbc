package org.ahead4.cdes.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 短信发送响应DTO
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
@Data
public class SmsSendResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 结果代码
     */
    private String result;

    /**
     * 结果描述
     */
    private String description;

    /**
     * 失败号码列表
     */
    private String failList;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 创建成功响应
     *
     * @param taskId 任务ID
     * @return 响应对象
     */
    public static SmsSendResponse success(String taskId) {
        SmsSendResponse response = new SmsSendResponse();
        response.setResult("0");
        response.setDescription("发送短信成功");
        response.setTaskId(taskId);
        response.setSuccess(true);
        return response;
    }

    /**
     * 创建失败响应
     *
     * @param result 结果代码
     * @param description 结果描述
     * @param faillist 失败号码列表
     * @return 响应对象
     */
    public static SmsSendResponse failure(String result, String description, String failList) {
        SmsSendResponse response = new SmsSendResponse();
        response.setResult(result);
        response.setDescription(description);
        response.setFailList(failList);
        response.setSuccess(false);
        return response;
    }
}
