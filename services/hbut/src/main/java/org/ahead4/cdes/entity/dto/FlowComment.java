package org.ahead4.cdes.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.flowable.engine.impl.persistence.entity.CommentEntityImpl;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "流程审批意见", description = "流程审批意见")
public class FlowComment extends CommentEntityImpl {

    private static final long serialVersionUID = 1L;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    private String displayname;

    /**
     * 部门编码
     */
    @ApiModelProperty(value = "部门编码")
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;

}
