package org.ahead4.cdes.utils;

import org.ahead4.redis.utils.RedisUtil;
import org.apache.commons.lang3.StringUtils;

import java.security.InvalidParameterException;

public class CaptchaHolder {
    public static final String CAPTCHA_KEY = "CaptchaKey::";
    public static final String USER_SHOULD_CAPTCHA_FLAG = "UserShouldCaptchaFlag::";
    private static final ThreadLocal<String> usernameThreadLocal = new ThreadLocal<>();
    private static final ThreadLocal<String> sessionIdThreadLocal = new ThreadLocal<>();


    /**
     * 清除当前用户全部验证码相关状态
     */
    public static void resetStatus() {
        if (StringUtils.isEmpty(username())) return;
        // 只要用户认证成功，删除用户需要验证码的标志
        RedisUtil.del(shouldCaptchaKey());
        RedisUtil.del(captchaKey());
    }

    /**
     * 是否需要输入验证码
     * @return
     */
    public static boolean isShouldCaptcha() {
        return RedisUtil.hasKey(shouldCaptchaKey());
    }

    /**
     * 将用户标记为需要输入验证码
     */
    public static void userShouldCaptcha() {
        RedisUtil.set(shouldCaptchaKey(), "1", 5 * 60 * 60L);
    }

    /**
     * 检查验证码
     * @param captcha
     */
    public static void checkCaptcha(String captcha) throws InvalidParameterException {
        captcha = StringUtils.defaultString(captcha);

        if(isShouldCaptcha()) {
            if (StringUtils.isEmpty(captcha))
                throw new InvalidParameterException("请输入验证码！");

            String captchaCode = RedisUtil.get(captchaKey());

            if(!captcha.trim().equalsIgnoreCase(captchaCode))
                throw new InvalidParameterException("验证码错误！");
        }

    }



    /**
     * 清理线程
     */
    public static void clean() {
        usernameThreadLocal.set(null);
        sessionIdThreadLocal.set(null);
    }

    private static void username(String username) {
        usernameThreadLocal.set(username);
    }
    public static String username() {
        return  StringUtils.defaultString(usernameThreadLocal.get());
    }
    private static void sessionId(String sessionId) {
        sessionIdThreadLocal.set(sessionId);
    }
    public static String sessionId() {
        return StringUtils.defaultString(sessionIdThreadLocal.get());
    }

    public static String shouldCaptchaKey() {
        String captchaKey = StringUtils.defaultString(username());

        if(captchaKey == null)
            throw new InvalidParameterException("请输入用户名！");

        return USER_SHOULD_CAPTCHA_FLAG + captchaKey;
    }
    public static String captchaKey() {
        String captchaKey = StringUtils.defaultString(username());

        if(captchaKey == null)
            throw new InvalidParameterException("请输入用户名！");

        return CAPTCHA_KEY + captchaKey;
    }
    public static void init(String username, String sessionId) {
        clean();
        username(username);
        sessionId(sessionId);
    }
}
