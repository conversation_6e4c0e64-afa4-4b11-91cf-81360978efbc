package org.ahead4.cdes.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 短信上行回复查询响应DTO
 *
 * <AUTHOR> Assistant
 * @date 2025/04/15
 */
@Data
public class SmsReplyResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 结果代码
     */
    private String result;

    /**
     * 确认时间
     */
    private String confirmTime;

    /**
     * 最后一条回复ID
     */
    private String id;

    /**
     * 回复列表
     */
    private List<SmsReplyItem> replys;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 创建成功响应
     *
     * @param result 结果代码
     * @param confirmTime 确认时间
     * @param id 最后一条回复ID
     * @param replys 回复列表
     * @return 响应对象
     */
    public static SmsReplyResponse success(String result, String confirmTime, String id, List<SmsReplyItem> replys) {
        SmsReplyResponse response = new SmsReplyResponse();
        response.setResult(result);
        response.setConfirmTime(confirmTime);
        response.setId(id);
        response.setReplys(replys);
        response.setSuccess(true);
        return response;
    }

    /**
     * 创建失败响应
     *
     * @param result 结果代码
     * @return 响应对象
     */
    public static SmsReplyResponse failure(String result) {
        SmsReplyResponse response = new SmsReplyResponse();
        response.setResult(result);
        response.setSuccess(false);
        return response;
    }

    /**
     * 短信上行回复项
     */
    @Data
    public static class SmsReplyItem implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 手机号码
         */
        private String mdn;

        /**
         * 接入号
         */
        private String callmdn;

        /**
         * 回复内容
         */
        private String content;

        /**
         * 回复时间
         */
        private String replyTime;

        /**
         * 回复ID
         */
        private String id;
    }
}
