package org.ahead4.email.provider.tencent.internal;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

class MailLogServiceTest {
    private MailLogService mailLogService;
    @BeforeEach
    void setUp() throws Exception {

        String copid = System.getenv("COPID");
        String emailSecret = System.getenv("EMAIL_SECRET");
        String logSecret = System.getenv("LOG_SECRET");

        EmailClient client  = new EmailClient(copid, emailSecret, logSecret);
        this.mailLogService = client.Log();
    }

    @Test
    void getLogTest() throws Exception {
        MailLogService.UserMailLogRequest request = new MailLogService.UserMailLogRequest();
        request.setUserId("<EMAIL>");

        request.setBeginDate("2024-11-01");
        request.setEndDate("2025-04-07");
        MailLogService.UserMailLogResponse response = mailLogService.queryUserMailStatus(request);

        assertNotNull(response);
    }
}