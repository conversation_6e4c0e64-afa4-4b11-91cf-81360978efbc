package org.ahead4.email.provider.tencent;

import org.ahead4.email.operation.*;
import org.ahead4.email.provider.tencent.internal.EmailClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;


class TencentOpTest {
    private EmailClient client;
    @BeforeEach
    void setUp() throws Exception {

        String copid = System.getenv("COPID");
        String emailSecret = System.getenv("EMAIL_SECRET");
        String logSecret = System.getenv("LOG_SECRET");

        this.client  = new EmailClient(copid, emailSecret, logSecret);
    }

    @Test
    void listDept() {
        DepartmentOp op = new TencentDepartmentOp(client);
        List<Department> departments = op.list(1);
        System.out.println(departments);
    }

    @Test
    void listAllDept() {
        DepartmentOp op = new TencentDepartmentOp(client);
        List<Department> departments = op.listAll();
        System.out.println(departments.size());
    }

    @Test
    void createAndDeleteUser() throws OperationException {
        EmailAccountOp op = new TencentEmailAccountOp(client);
        UserEmailAccount account = new UserEmailAccount();
        account.setUserId("<EMAIL>");
        account.setDisplayName("测试");
        account.setMobile("***********");
        account.setDepartmentId(6399633858310500085l);
        account.setAlias("<EMAIL>");
        account.setPassword("1Morganzh0");

        assertDoesNotThrow(() -> {
            op.createEmailAccount(account);
        });

        UserEmailAccount check = op.getEmailAccount("<EMAIL>");

        assertNotNull(check);

        assertEquals(check.getAlias(), account.getAlias());

        assertEquals(check.getDepartment(), account.getDepartment());

        assertDoesNotThrow(() -> {
            op.changePassword("<EMAIL>", "1Morganzh110");
        });

        assertDoesNotThrow(() -> {
            op.deleteEmailAccount("<EMAIL>");
        });
    }


    @Test
    void getLogTest() throws ParseException {
        StatsOp op = new TencentStatsOp(client);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date start = sdf.parse("2025-01-01");
        Date end = sdf.parse("2025-03-01");


        Map<String, MailStats> stats = op.getEmailStats(start, end);

        assertNotNull(stats);
    }
}