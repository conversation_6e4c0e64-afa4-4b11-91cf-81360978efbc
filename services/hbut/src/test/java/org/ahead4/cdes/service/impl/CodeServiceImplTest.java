package org.ahead4.cdes.service.impl;

import org.ahead4.cdes.mapper.CodeMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.codehaus.groovy.runtime.DefaultGroovyMethods.any;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CodeServiceImplTest {

    private CodeMapper codeMapper;
    private CodeServiceImpl codeService;

    @BeforeEach
    void setUp() {
        codeMapper = mock(CodeMapper.class);
        codeService = new CodeServiceImpl(codeMapper);
    }

    @Test
    void testGenerateCode_success() {
        // 准备测试数据
        String curYear = "2023";
        String sequenceName = curYear + "_seq";
        Long nextId = 123L;
        String expectedCode = curYear + String.format("%04d", nextId);

        // 模拟日期格式化
        SimpleDateFormat mockYearFormat = Mockito.spy(new SimpleDateFormat("yyyy"));

        // 使用反射替换私有字段
        try {
            CodeServiceImpl.class.getDeclaredField("DATEFORMAT_YEAR").set(codeService, mockYearFormat);
        } catch (Exception e) {
            fail("设置日期格式化器时发生错误: " + e.getMessage());
        }

        // 定义模拟行为
        when(mockYearFormat.format(any(Date.class))).thenReturn(curYear);
        doNothing().when(codeMapper).initBillDailyCode(sequenceName);
        when(codeMapper.nextVal(sequenceName)).thenReturn(nextId);

        // 执行测试
        String result = codeService.generateCode();

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedCode, result);

        // 验证交互
        verify(mockYearFormat, times(1)).format(any(Date.class));
        verify(codeMapper, times(1)).initBillDailyCode(sequenceName);
        verify(codeMapper, times(1)).nextVal(sequenceName);
    }

    @Test
    void testGenerateCode_concurrent() throws InterruptedException {
        // 准备测试数据
        String curYear = "2023";
        String sequenceName = curYear + "_seq";
        int threadCount = 10;
        int loopCount = 50;

        // 模拟日期格式化
        SimpleDateFormat mockYearFormat = Mockito.spy(new SimpleDateFormat("yyyy"));

        // 使用反射替换私有字段
        try {
            CodeServiceImpl.class.getDeclaredField("DATEFORMAT_YEAR").set(codeService, mockYearFormat);
        } catch (Exception e) {
            fail("设置日期格式化器时发生错误: " + e.getMessage());
        }

        // 定义模拟行为
        when(mockYearFormat.format(any(Date.class))).thenReturn(curYear);
        doNothing().when(codeMapper).initBillDailyCode(sequenceName);

        // 创建固定大小的线程池
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);

        // 使用AtomicInteger来验证最终值
        AtomicInteger finalCounter = new AtomicInteger();

        // 使用CountDownLatch来同步测试
        CountDownLatch latch = new CountDownLatch(threadCount * loopCount);

        // 模拟并发调用
        for (int i = 0; i < threadCount; i++) {
            int finalI = i;
            when(codeMapper.nextVal(sequenceName)).thenAnswer(invocation -> finalI + 1); // 返回不同的ID

            for (int j = 0; j < loopCount; j++) {
                executorService.submit(() -> {
                    try {
                        String result = codeService.generateCode();

                        // 验证结果格式
                        assertNotNull(result);
                        assertEquals(curYear.length() + 4, result.length());
                        assertTrue(result.startsWith(curYear));

                        // 解析最后四位数字
                        int id = Integer.parseInt(result.substring(curYear.length()));
                        finalCounter.incrementAndGet();
                    } finally {
                        latch.countDown();
                    }
                });
            }
        }

        // 等待所有任务完成
        try {
            boolean completed = latch.await(1, TimeUnit.MINUTES);
            assertTrue(completed, "并非所有线程都在指定时间内完成");

            // 验证总调用次数
            verify(codeMapper, times(threadCount * loopCount)).nextVal(sequenceName);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            fail("测试过程中发生中断异常: " + e.getMessage());
        } finally {
            executorService.shutdownNow();
        }
    }
}