plugins {
    id 'java'
}

project.description = '微服务包'

allprojects {
    if('services'.equals(it.name)) return

    apply plugin: "com.palantir.docker"
    apply plugin: 'org.springframework.boot'
    apply plugin: 'java'
    task cleanold {
//       doLast {
//          exec {
//             workingDir '.'
//            // 筛选指定镜像删除
//             commandLine 'sh', '-c', 'docker rmi $( docker images -q -f dangling=true)  || true'
//            // 删除全部没有tag的镜像
//             commandLine 'sh', '-c', 'docker images|grep none|awk \'{print $3}\'|xargs docker rmi'
//          }
//       }
    }

    docker {
        dockerfile file("${rootDir}/docker/jar-dockerfile")
        name "${project.group}/${bootJar.getArchiveBaseName().get()}:${bootJar. getArchiveVersion().get()}"
        tag 'hub', "${hub}/${docker_group}/${bootJar.getArchiveBaseName().get()}:${bootJar. getArchiveVersion().get()}"
        copySpec.from("./bins/dockerize").into("/")
        files bootJar.archiveFile
        buildArgs(['JAR_FILE': "${bootJar.getArchiveFileName().get()}"])
    }
    tasks.('dockerPrepare').dependsOn  'bootJar','cleanold'
    dependencies {
        implementation group: 'javax.xml.bind', name: 'jaxb-api'
        implementation group: 'com.sun.xml.bind', name: 'jaxb-impl', version: '2.2.11'
        implementation group: 'org.glassfish.jaxb', name: 'jaxb-runtime', version: '2.2.10-b140310.1920'
        implementation group: 'javax.activation', name: 'activation', version: '1.1.1'
        implementation 'javax.ws.rs:javax.ws.rs-api:2.1.1'

        // swagger2
        implementation 'io.springfox:springfox-swagger2:2.9.2'
        implementation 'io.springfox:springfox-swagger-ui:2.9.2'
        implementation 'cn.hutool:hutool-all:5.8.16'
    }


}

dependencies {
}
