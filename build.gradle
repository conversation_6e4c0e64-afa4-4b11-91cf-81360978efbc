plugins {
    id 'org.springframework.boot' version '2.4.6'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'com.palantir.docker' version '0.22.1' apply false
    id "com.avast.gradle.docker-compose" version "0.9.4" apply false
    id "com.github.node-gradle.node" version "3.2.1" apply false
    id 'idea'
    id 'java'
}
ext {
    springBootVersion = "2.4.6"
    springCloudAlibabaVersion = "2021.1"
    springCloudVersion = "2020.0.3"
//    orgAhead4CloudVersion= "3.1.18"
    orgAhead4CloudVersion = "4.0.3.04"
    geotoolsversion='15.1'
}

test {
    useJUnitPlatform()
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

allprojects  {

    apply plugin: 'java'
    apply plugin: 'idea'
    ext {
        mybatisPlusVersion = "3.4.3"
        lombokVersion = '1.18.22'
        springBootVersion = "2.4.6"
        hub = System.getenv('DOCKER_HUB') ?: "$hub"
    }
// 输出用于验证
    println "Docker Hub: $hub"
    sourceCompatibility = '1.8'
    targetCompatibility = '1.8'

    // 静态资源配置
    processResources {
        from('src/main/java') {
            include '**/*'      // 导入里面的所有文件，也可以自定义正则表达式
            exclude "**/*.java" // 源码文件中.java 文件是不需要的
        }
    }
    repositories {
        mavenLocal()
        maven {
            name "aliyunmaven"
            content {
                excludeModule("javax.media", "jai_core")
            }
            url "https://maven.aliyun.com/nexus/content/groups/public/"
        }
        maven {
            name "lon-yuan-repo"
            url "https://repo.lon-yuan.com/repository/maven-public/"
            credentials {
                username = "user"
                password = "user@lon-yuan\$123"
            }
        }
    }
    processResources {
        from('src/main/java') {
            include '**/*'      // 导入里面的所有文件，也可以自定义正则表达式
            exclude "**/*.java" // 源码文件中.java 文件是不需要的
        }
    }

    dependencies {

        // lombok依赖，只支持jdk8
        compileOnly "org.projectlombok:lombok:$lombokVersion"
        annotationProcessor "org.projectlombok:lombok:$lombokVersion"

        // 工具类库
        implementation 'org.apache.commons:commons-lang3:3.12.0'

        implementation 'org.apache.commons:commons-pool2:2.10.0'

        implementation 'commons-codec:commons-codec:1.15'

        implementation 'commons-io:commons-io:2.11.0'

        implementation 'commons-beanutils:commons-beanutils:1.9.4'

        implementation 'com.alibaba:fastjson:1.2.79'

    }

}


dependencies {



implementation 'com.fasterxml.jackson:jackson-base:2.14.2'
    implementation 'com.fasterxml:jackson-xml-databind:0.6.2'
}

test {
    useJUnitPlatform()
}